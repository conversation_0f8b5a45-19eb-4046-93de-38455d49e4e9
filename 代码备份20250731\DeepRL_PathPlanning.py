"""
标准DQN路径规划实现
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import random
import matplotlib.pyplot as plt
from collections import deque

class DQNNetwork(nn.Module):
    def __init__(self, action_size=8):
        super(DQNNetwork, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, kernel_size=8, stride=4)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=4, stride=2)
        self.conv3 = nn.Conv2d(64, 64, kernel_size=3, stride=1)
        self.fc1 = nn.Linear(7 * 7 * 64, 512)
        self.fc2 = nn.Linear(512, action_size)

    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        x = x.view(x.size(0), -1)
        x = F.relu(self.fc1(x))
        return self.fc2(x)

class ReplayBuffer:
    def __init__(self, capacity=10000):
        self.buffer = deque(maxlen=capacity)

    def push(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done

    def __len__(self):
        return len(self.buffer)

class ClassicDQN:
    def __init__(self, start, goal, grid_map):
        self.start = start
        self.goal = goal
        self.grid_map = np.array(grid_map)

        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.q_network = DQNNetwork().to(self.device)
        self.target_network = DQNNetwork().to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=0.0001)
        self.memory = ReplayBuffer(10000)

        self.epsilon = 1.0
        self.epsilon_decay = 0.995
        self.epsilon_min = 0.01
        self.gamma = 0.99
        self.batch_size = 32

        self.actions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]
        self.episode_rewards = []

        self.update_target_network()

    def update_target_network(self):
        self.target_network.load_state_dict(self.q_network.state_dict())

    def preprocess_state(self, position):
        state = np.zeros((3, 84, 84), dtype=np.float32)
        x, y = position
        window_size = 84
        half_window = window_size // 2

        start_x = max(0, x - half_window)
        end_x = min(self.grid_map.shape[0], x + half_window)
        start_y = max(0, y - half_window)
        end_y = min(self.grid_map.shape[1], y + half_window)

        state_start_x = half_window - (x - start_x)
        state_end_x = state_start_x + (end_x - start_x)
        state_start_y = half_window - (y - start_y)
        state_end_y = state_start_y + (end_y - start_y)

        state[0, state_start_x:state_end_x, state_start_y:state_end_y] = \
            self.grid_map[start_x:end_x, start_y:end_y]

        goal_x = self.goal[0] - x + half_window
        goal_y = self.goal[1] - y + half_window
        if 0 <= goal_x < window_size and 0 <= goal_y < window_size:
            state[1, goal_x, goal_y] = 1.0

        state[2, half_window, half_window] = 1.0
        return state

    def get_action(self, state):
        if random.random() < self.epsilon:
            return random.randint(0, 7)

        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        q_values = self.q_network(state_tensor)
        return q_values.argmax().item()

    def is_valid_position(self, pos):
        x, y = pos
        if x < 0 or x >= self.grid_map.shape[0] or y < 0 or y >= self.grid_map.shape[1]:
            return False
        return self.grid_map[x, y] == 0

    def get_reward(self, current_pos, next_pos, done):
        if done and next_pos == self.goal:
            return 100.0
        if not self.is_valid_position(next_pos):
            return -10.0

        current_dist = np.linalg.norm(np.array(current_pos) - np.array(self.goal))
        next_dist = np.linalg.norm(np.array(next_pos) - np.array(self.goal))
        return (current_dist - next_dist) * 0.1 - 0.01

    def step(self, current_pos, action):
        dx, dy = self.actions[action]
        next_pos = (current_pos[0] + dx, current_pos[1] + dy)
        done = (next_pos == self.goal)

        if not self.is_valid_position(next_pos):
            next_pos = current_pos

        reward = self.get_reward(current_pos, next_pos, done)
        return next_pos, reward, done

    def train(self, episodes=200):
        print(f"开始DQN训练，回合数: {episodes}")

        for episode in range(episodes):
            current_pos = self.start
            state = self.preprocess_state(current_pos)
            total_reward = 0

            for _ in range(200):
                action = self.get_action(state)
                next_pos, reward, done = self.step(current_pos, action)
                next_state = self.preprocess_state(next_pos)

                self.memory.push(state, action, reward, next_state, done)

                if len(self.memory) > self.batch_size:
                    self.replay()

                state = next_state
                current_pos = next_pos
                total_reward += reward

                if done:
                    break

            self.episode_rewards.append(total_reward)

            if self.epsilon > self.epsilon_min:
                self.epsilon *= self.epsilon_decay

            if episode % 50 == 0:
                self.update_target_network()
                avg_reward = np.mean(self.episode_rewards[-50:]) if len(self.episode_rewards) >= 50 else np.mean(self.episode_rewards)
                print(f"回合 {episode}, 平均奖励: {avg_reward:.2f}, Epsilon: {self.epsilon:.3f}")

        print("DQN训练完成!")

    def replay(self):
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)

        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)

        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))

        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0].detach()
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)

        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)

        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

    def plan_path(self):
        path = [self.start]
        current_pos = self.start
        visited = set()
        visited.add(self.start)

        for _ in range(500):
            state = self.preprocess_state(current_pos)
            action = self.get_action(state)

            next_pos, _, done = self.step(current_pos, action)
            path.append(next_pos)
            current_pos = next_pos

            if done:
                print(f"DQN找到完整路径，长度: {len(path)}")
                return path

            # 检测循环：如果访问过相同位置，停止探索
            if next_pos in visited:
                if len(path) > 10:  # 至少探索了一些步骤
                    break
            visited.add(next_pos)

        # 即使未找到完整路径，也返回探索的部分路径
        if len(path) > 1:
            print(f"DQN未找到完整路径，返回部分探索路径，长度: {len(path)}")
            return path
        else:
            print("DQN未能进行有效探索")
            return None

    def visualize_training(self):
        if not self.episode_rewards:
            return

        plt.figure(figsize=(10, 6))
        plt.plot(self.episode_rewards)
        plt.title('DQN训练奖励')
        plt.xlabel('回合')
        plt.ylabel('总奖励')
        plt.grid(True)
        plt.show()
