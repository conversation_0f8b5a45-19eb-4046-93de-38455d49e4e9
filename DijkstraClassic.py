import math
import time
import heapq
import matplotlib.pyplot as plt
import numpy as np

########################################
# 常量设置
########################################

SQRT2 = math.sqrt(2)

class DiagonalMovement:
    """
    定义对角线移动策略
    """
    ALWAYS = 1                  # 总是允许对角线移动
    NEVER = 2                   # 从不允许对角线移动
    IF_AT_MOST_ONE_OBSTACLE = 3 # 邻接正交方向中只要至少有一个可通行
    ONLY_WHEN_NO_OBSTACLE = 4   # 只有当两个正交方向均可通行时才允许

########################################
# 邻居查找函数
########################################

def get_neighbors(x: int, y: int, grid: list, diagonal_movement: int = DiagonalMovement.NEVER) -> list:
    """
    返回 (x, y) 周围可通行的邻居坐标
    :param x, y: 当前坐标
    :param grid: 二维地图列表（0 表示可通行，1 表示障碍）
    :param diagonal_movement: 对角线移动策略
    :return: 邻居坐标列表 [(nx, ny), ...]
    """
    neighbors = []
    rows = len(grid)
    cols = len(grid[0])
    # 处理四个正交方向
    for dx, dy in [(0, -1), (1, 0), (0, 1), (-1, 0)]:
        nx, ny = x + dx, y + dy
        if 0 <= nx < cols and 0 <= ny < rows and grid[ny][nx] == 0:
            neighbors.append((nx, ny))

    # 允许对角线移动时处理对角方向
    if diagonal_movement != DiagonalMovement.NEVER:
        for dx, dy in [(-1, -1), (1, -1), (1, 1), (-1, 1)]:
            nx, ny = x + dx, y + dy
            if not (0 <= nx < cols and 0 <= ny < rows and grid[ny][nx] == 0):
                continue

            if diagonal_movement == DiagonalMovement.ONLY_WHEN_NO_OBSTACLE:
                # 只有当相邻正交方向均可通行时，才能对角移动
                if 0 <= x + dx < cols and 0 <= y < rows and grid[y][x + dx] == 0 and \
                   0 <= x < cols and 0 <= y + dy < rows and grid[y + dy][x] == 0:
                    neighbors.append((nx, ny))
            elif diagonal_movement == DiagonalMovement.IF_AT_MOST_ONE_OBSTACLE:
                # 只要其中至少有一个正交方向可通行即可
                cond1 = 0 <= x + dx < cols and 0 <= y < rows and grid[y][x + dx] == 0
                cond2 = 0 <= x < cols and 0 <= y + dy < rows and grid[y + dy][x] == 0
                if cond1 or cond2:
                    neighbors.append((nx, ny))
            else:
                # DiagonalMovement.ALWAYS
                neighbors.append((nx, ny))
    return neighbors

########################################
# Dijkstra 算法实现
########################################

def DijkstraClassic(grid: list,
             start: tuple,
             end: tuple,
             diagonal_movement: int = DiagonalMovement.NEVER,
             time_limit: float = float('inf'),
             max_runs: float = float('inf')) -> tuple:
    """
    Dijkstra 算法用于寻找最短路径
    :param grid: 二维地图列表，0 表示可通行，1 表示障碍
    :param start: 起点坐标 (x, y)
    :param end: 终点坐标 (x, y)
    :param diagonal_movement: 对角线移动策略
    :param time_limit: 最大运行时间（秒）
    :param max_runs: 最大迭代次数
    :return: (路径坐标列表, 迭代次数) 若未找到路径则返回 (None, 迭代次数)
    """
    start_time = time.time()
    rows = len(grid)
    cols = len(grid[0])
    # 用于记录每个点的 g 值（起点到该点的实际代价）
    g_cost = { (x, y): float('inf') for y in range(rows) for x in range(cols) }
    parent = {}  # 用于记录路径的父节点

    # 开放列表：存储 (g 值, 插入顺序, 坐标)
    open_set = []
    counter = 0
    g_cost[start] = 0
    heapq.heappush(open_set, (g_cost[start], counter, start))
    counter += 1

    runs = 0
    closed = set()

    while open_set:
        runs += 1
        if runs >= max_runs:
            raise Exception("迭代次数已达上限")
        if time.time() - start_time >= time_limit:
            raise Exception("运行时间超过限制")

        current = heapq.heappop(open_set)[2]
        if current == end:
            # 找到路径，回溯构造路径列表
            path = []
            while current in parent:
                path.append(current)
                current = parent[current]
            path.append(start)
            path.reverse()
            return path, runs

        closed.add(current)
        for neighbor in get_neighbors(current[0], current[1], grid, diagonal_movement):
            if neighbor in closed:
                continue
            # 计算从 current 到 neighbor 的移动代价（对角移动代价为 SQRT2，正交为 1）
            step_cost = SQRT2 if current[0] != neighbor[0] and current[1] != neighbor[1] else 1
            tentative_g = g_cost[current] + step_cost
            if tentative_g < g_cost[neighbor]:
                parent[neighbor] = current
                g_cost[neighbor] = tentative_g
                heapq.heappush(open_set, (g_cost[neighbor], counter, neighbor))
                counter += 1

    return None, runs  # 未找到路径

########################################
# 测试示例
########################################

if __name__ == '__main__':
    # 定义一个简单的地图：0 表示可通行，1 表示障碍
    grid_map = [
        [0, 0, 0, 0, 0],
        [0, 1, 1, 1, 0],
        [0, 0, 0, 1, 0],
        [0, 1, 0, 0, 0],
        [0, 0, 0, 0, 0]
    ]

    start = (0, 0)
    end = (4, 4)
    # 设置对角线移动策略（此处选择总是允许对角线移动）
    diagonal = DiagonalMovement.ALWAYS

    try:
        path, runs = Dijkstra(grid_map, start, end, diagonal_movement=diagonal)
        if path:
            print("找到路径，迭代次数：", runs)
            for coord in path:
                print(coord)
            
            # 可视化结果
            plt.figure(figsize=(5, 5))
            grid_np = np.array(grid_map)
            plt.imshow(grid_np, cmap='gray_r', origin='upper')
            
            # 绘制路径（红色）
            path_x = [p[0] for p in path]
            path_y = [p[1] for p in path]
            plt.plot(path_x, path_y, color='red', linewidth=2)
            
            # 绘制起点（绿色）和终点（蓝色）
            plt.scatter(start[0], start[1], color='green', s=100, label='Start')
            plt.scatter(end[0], end[1], color='blue', s=100, label='End')
            
            plt.legend()
            plt.title('Dijkstra Pathfinding')
            plt.xticks(range(len(grid_map[0])))
            plt.yticks(range(len(grid_map)))
            plt.grid(True)
            plt.show()
        else:
            print("未找到路径")
    except Exception as e:
        print("搜索过程中出错：", e)
