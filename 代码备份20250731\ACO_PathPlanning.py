"""
蚁群优化算法用于路径规划
基于python_motion_planning-master项目的ACO实现
适配海冰分割路径规划系统
"""
import numpy as np
import matplotlib.pyplot as plt
import json
import time
import random
import math
from bisect import bisect_left

class ACOPathPlanner:
    def __init__(self, start, goal, grid_map, num_ants=20, max_iterations=100, 
                 evaporation_rate=0.1, alpha=1.0, beta=2.0, step_size=0.5):
        """
        改进的ACO路径规划器
        
        参数:
        start: 起点坐标 (x, y)
        goal: 终点坐标 (x, y)
        grid_map: 网格地图 (0=可通行, 1=障碍物)
        num_ants: 蚂蚁数量
        max_iterations: 最大迭代次数
        evaporation_rate: 信息素蒸发率
        alpha: 信息素重要性因子
        beta: 启发式信息重要性因子
        step_size: 蚂蚁移动步长
        """
        self.start = np.array(start, dtype=float)
        self.goal = np.array(goal, dtype=float)
        self.grid_map = np.array(grid_map)
        self.map_shape = self.grid_map.shape
        
        self.num_ants = num_ants
        self.max_iterations = max_iterations
        self.evaporation_rate = evaporation_rate
        self.alpha = alpha
        self.beta = beta
        self.step_size = step_size
        
        # 初始化蚂蚁位置
        self.ants = np.tile(self.start, (num_ants, 1))
        
        # 信息素矩阵 - 每只蚂蚁维护自己的信息素记录
        self.pheromone = np.ones((num_ants, 2))
        
        # 记录最优解
        self.best_ant = self.start.copy()
        self.best_score = float('inf')
        self.best_path = [self.start.copy()]
        
        # 统计信息
        self.best_positions = []
        self.exploration_values = []
        self.exploitation_values = []
        self.convergence_history = []
        self.all_paths = []
        
    def distance(self, point1, point2):
        """计算两点间的欧几里得距离"""
        return np.linalg.norm(point1 - point2)
    
    def is_valid_position(self, position):
        """检查位置是否有效（在地图范围内且不是障碍物）"""
        x, y = int(position[0]), int(position[1])
        if x < 0 or x >= self.map_shape[1] or y < 0 or y >= self.map_shape[0]:
            return False
        return self.grid_map[y, x] == 0
    
    def get_heuristic_value(self, position):
        """计算启发式值（距离目标越近值越大）"""
        dist = self.distance(position, self.goal)
        return 1.0 / (dist + 1e-6)  # 避免除零
    
    def move_towards_target(self, ant_position, target_position):
        """改进的移动策略：考虑障碍物避让"""
        direction = target_position - ant_position
        dist = np.linalg.norm(direction)
        
        if dist < 1e-6:
            return ant_position
        
        # 归一化方向向量
        direction = direction / dist
        
        # 计算新位置
        new_position = ant_position + direction * self.step_size
        
        # 检查新位置是否有效
        if self.is_valid_position(new_position):
            return new_position
        
        # 如果直接移动遇到障碍物，尝试绕行
        return self.avoid_obstacle(ant_position, direction)
    
    def avoid_obstacle(self, ant_position, original_direction):
        """障碍物避让策略"""
        # 尝试多个方向
        angles = [-45, 45, -90, 90, -135, 135, 180]  # 度数
        
        for angle in angles:
            # 旋转方向向量
            rad = np.radians(angle)
            rotation_matrix = np.array([[np.cos(rad), -np.sin(rad)],
                                      [np.sin(rad), np.cos(rad)]])
            new_direction = rotation_matrix @ original_direction
            new_position = ant_position + new_direction * self.step_size
            
            if self.is_valid_position(new_position):
                return new_position
        
        # 如果所有方向都被阻挡，保持原位置
        return ant_position
    
    def update_pheromone(self, ant_index, position):
        """更新信息素"""
        # 基于距离目标的远近更新信息素
        distance_to_goal = self.distance(position, self.goal)
        pheromone_deposit = 1.0 / (distance_to_goal + 1e-6)
        self.pheromone[ant_index] += pheromone_deposit
    
    def select_next_move(self, ant_position):
        """基于信息素和启发式信息选择下一步移动"""
        # 生成候选移动方向
        angles = np.linspace(0, 2*np.pi, 8, endpoint=False)  # 8个方向
        candidates = []
        probabilities = []
        
        for angle in angles:
            direction = np.array([np.cos(angle), np.sin(angle)])
            candidate_pos = ant_position + direction * self.step_size
            
            if self.is_valid_position(candidate_pos):
                candidates.append(candidate_pos)
                
                # 计算概率（简化版本）
                heuristic = self.get_heuristic_value(candidate_pos)
                # 使用平均信息素值作为信息素强度
                pheromone_strength = np.mean(self.pheromone)
                
                probability = (pheromone_strength ** self.alpha) * (heuristic ** self.beta)
                probabilities.append(probability)
        
        if not candidates:
            return ant_position  # 无处可去，保持原位
        
        # 归一化概率
        probabilities = np.array(probabilities)
        if probabilities.sum() > 0:
            probabilities = probabilities / probabilities.sum()
            # 根据概率选择
            selected_idx = np.random.choice(len(candidates), p=probabilities)
            return candidates[selected_idx]
        else:
            # 随机选择
            return candidates[np.random.randint(len(candidates))]
    
    def optimize(self):
        """运行ACO优化"""
        print(f"开始ACO优化: {self.num_ants}只蚂蚁, {self.max_iterations}次迭代")
        start_time = time.time()
        
        for iteration in range(self.max_iterations):
            iteration_paths = []
            
            # 重置蚂蚁位置到起点
            self.ants = np.tile(self.start, (self.num_ants, 1))
            
            # 每只蚂蚁构建路径
            for ant_idx in range(self.num_ants):
                # 确保每只蚂蚁都从起点开始
                current_pos = self.start.copy()
                ant_path = [current_pos.copy()]
                
                # 限制每只蚂蚁的最大步数
                max_steps = 200
                for _ in range(max_steps):
                    # 选择下一步移动
                    if np.random.random() < 0.7:  # 70%概率使用启发式移动
                        next_pos = self.move_towards_target(current_pos, self.goal)
                    else:  # 30%概率使用概率选择
                        next_pos = self.select_next_move(current_pos)
                    
                    current_pos = next_pos
                    ant_path.append(current_pos.copy())
                    
                    # 更新信息素
                    self.update_pheromone(ant_idx, current_pos)
                    
                    # 检查是否到达目标（更严格的判断）
                    if self.distance(current_pos, self.goal) < 0.5:
                        # 确保路径以目标点结束
                        if not np.array_equal(current_pos, self.goal):
                            ant_path.append(self.goal.copy())
                        break
                
                self.ants[ant_idx] = current_pos
                iteration_paths.append(ant_path)
                
                # 更新全局最优
                score = self.distance(current_pos, self.goal)
                if score < self.best_score:
                    self.best_score = score
                    self.best_ant = current_pos.copy()
                    self.best_path = ant_path.copy()

                    # 验证路径完整性
                    if len(self.best_path) > 0:
                        print(f"  找到更好路径: 距离={score:.3f}, 长度={len(self.best_path)}, 起点={self.best_path[0]}, 终点={self.best_path[-1]}")
            
            # 信息素蒸发
            self.pheromone *= (1 - self.evaporation_rate)
            
            # 记录统计信息
            exploration_value = np.mean(self.pheromone)
            exploitation_value = 1.0 / (self.best_score + 1e-6)
            
            self.exploration_values.append(exploration_value)
            self.exploitation_values.append(exploitation_value)
            self.best_positions.append(self.best_ant.copy())
            self.convergence_history.append(self.best_score)
            self.all_paths.append(iteration_paths)
            
            # 打印进度
            if iteration % 20 == 0:
                print(f"迭代 {iteration}: 最佳距离 = {self.best_score:.3f}")
        
        end_time = time.time()
        print(f"ACO优化完成! 用时: {end_time - start_time:.2f}秒")
        print(f"最终最佳距离: {self.best_score:.3f}")

        # 检查是否真正找到了到达目标的路径
        if self.best_score < 1.0:  # 距离目标很近
            print("✅ 找到了到达目标的路径")
            return self.convert_path_to_grid()
        else:
            print(f"❌ 未能到达目标，最近距离: {self.best_score:.3f}")
            # 即使没有完全到达，也返回部分路径供分析
            if self.best_path and len(self.best_path) > 1:
                print("🔄 返回部分探索路径")
                return self.convert_path_to_grid()
            else:
                return None
    
    def convert_path_to_grid(self):
        """将连续路径转换为网格路径"""
        if not self.best_path:
            print("❌ 没有找到有效路径")
            return None

        print(f"🔄 转换路径: 原始长度={len(self.best_path)}")
        print(f"   起点: {self.best_path[0]} -> 目标: {self.goal}")
        print(f"   终点: {self.best_path[-1]}")

        # 确保路径以起点开始
        path_to_convert = self.best_path.copy()
        if not np.allclose(path_to_convert[0], self.start, atol=1e-3):
            print("⚠️ 路径不是从起点开始，添加起点")
            path_to_convert.insert(0, self.start.copy())

        # 确保路径以终点结束
        if not np.allclose(path_to_convert[-1], self.goal, atol=1e-3):
            print("⚠️ 路径没有到达终点，添加终点")
            path_to_convert.append(self.goal.copy())

        grid_path = []
        for pos in path_to_convert:
            grid_x = int(round(pos[0]))
            grid_y = int(round(pos[1]))
            # 确保坐标在有效范围内
            grid_x = max(0, min(self.map_shape[1]-1, grid_x))
            grid_y = max(0, min(self.map_shape[0]-1, grid_y))
            grid_point = (grid_y, grid_x)  # (row, col) 格式
            if not grid_path or grid_path[-1] != grid_point:
                grid_path.append(grid_point)

        print(f"✅ 转换完成: 网格路径长度={len(grid_path)}")
        print(f"   网格起点: {grid_path[0]}")
        print(f"   网格终点: {grid_path[-1]}")

        # 验证起点终点
        expected_start = (int(round(self.start[1])), int(round(self.start[0])))
        expected_goal = (int(round(self.goal[1])), int(round(self.goal[0])))

        if grid_path[0] != expected_start:
            print(f"⚠️ 起点不匹配: 期望{expected_start}, 实际{grid_path[0]}")
        if grid_path[-1] != expected_goal:
            print(f"⚠️ 终点不匹配: 期望{expected_goal}, 实际{grid_path[-1]}")

        return grid_path
    
    def save_results(self, filename='improved_aco_results.json'):
        """保存结果到JSON文件"""
        data = {
            'best_score': float(self.best_score),
            'best_path': [pos.tolist() for pos in self.best_path],
            'convergence_history': self.convergence_history,
            'exploration_values': self.exploration_values,
            'exploitation_values': self.exploitation_values,
            'parameters': {
                'num_ants': self.num_ants,
                'max_iterations': self.max_iterations,
                'evaporation_rate': self.evaporation_rate,
                'alpha': self.alpha,
                'beta': self.beta,
                'step_size': self.step_size
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=4)
        print(f"结果已保存到 {filename}")
    
    def visualize_results(self):
        """可视化结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 路径可视化
        ax = axes[0, 0]
        
        # 显示地图
        ax.imshow(self.grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制最佳路径
        if self.best_path:
            path_array = np.array(self.best_path)
            ax.plot(path_array[:, 0], path_array[:, 1], 'r-', linewidth=3, label='Best Path')
            ax.plot(path_array[:, 0], path_array[:, 1], 'ro', markersize=4)
        
        # 标记起点和终点
        ax.plot(self.start[0], self.start[1], 'go', markersize=12, label='Start')
        ax.plot(self.goal[0], self.goal[1], 'bo', markersize=12, label='Goal')
        
        ax.set_title('ACO最佳路径')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 2. 收敛曲线
        ax = axes[0, 1]
        ax.plot(self.convergence_history, 'b-', linewidth=2)
        ax.set_title('收敛曲线')
        ax.set_xlabel('迭代次数')
        ax.set_ylabel('最佳距离')
        ax.grid(True, alpha=0.3)
        
        # 3. 探索vs利用
        ax = axes[1, 0]
        
        # 归一化
        norm_exploration = np.array(self.exploration_values)
        norm_exploitation = np.array(self.exploitation_values)
        
        if len(norm_exploration) > 0:
            norm_exploration = norm_exploration / np.max(norm_exploration)
        if len(norm_exploitation) > 0:
            norm_exploitation = norm_exploitation / np.max(norm_exploitation)
        
        ax.plot(norm_exploration, 'b-', linewidth=2, label='探索率')
        ax.plot(norm_exploitation, 'r-', linewidth=2, label='利用率')
        ax.set_title('探索vs利用')
        ax.set_xlabel('迭代次数')
        ax.set_ylabel('归一化值')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 4. 统计信息
        ax = axes[1, 1]
        ax.axis('off')
        
        stats_text = f"""
        算法统计信息:
        
        蚂蚁数量: {self.num_ants}
        迭代次数: {self.max_iterations}
        最佳距离: {self.best_score:.3f}
        路径长度: {len(self.best_path)}
        
        参数设置:
        蒸发率: {self.evaporation_rate}
        α (信息素): {self.alpha}
        β (启发式): {self.beta}
        步长: {self.step_size}
        """
        
        ax.text(0.1, 0.9, stats_text, transform=ax.transAxes, 
               fontsize=12, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        plt.show()

def test_improved_aco():
    """测试改进的ACO算法"""
    # 创建测试环境
    grid_map = np.zeros((20, 20))
    
    # 添加障碍物
    grid_map[5:15, 8] = 1  # 垂直墙
    grid_map[10, 5:15] = 1  # 水平墙
    grid_map[3:6, 3:6] = 1  # 小块障碍
    
    start = (1, 1)
    goal = (18, 18)
    
    print("测试改进的ACO算法...")
    print(f"环境: {grid_map.shape}")
    print(f"起点: {start}, 终点: {goal}")
    
    # 创建ACO规划器
    aco = ACOPathPlanner(
        start=start,
        goal=goal, 
        grid_map=grid_map,
        num_ants=15,
        max_iterations=80,
        evaporation_rate=0.1,
        alpha=1.0,
        beta=2.0,
        step_size=0.8
    )
    
    # 运行优化
    path = aco.optimize()
    
    # 保存和可视化结果
    aco.save_results()
    aco.visualize_results()
    
    return path

if __name__ == "__main__":
    test_improved_aco()
