%!PS-Adobe-3.0 EPSF-3.0
%%Title: DQN_Accumulated_Reward.eps
%%Creator: Matplotlib v3.7.5, https://matplotlib.org/
%%CreationDate: Sat Feb 24 16:10:16 2024
%%Orientation: portrait
%%BoundingBox: 75 223 537 569
%%HiResBoundingBox: 75.600000 223.200000 536.400000 568.800000
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Matplotlib.
10 dict begin
/FontName /DejaVuSans def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-2090 -948 3673 2524] def
/FontType 3 def
/Encoding [/minus /space /zero /one /two /three /five /seven /A /E /a /c /d /e /i /l /m /o /p /r /s /t /u /w] def
/CharStrings 25 dict dup begin
/.notdef 0 def
/minus{1716 0 217 557 1499 727 sc
217 727 m
1499 727 l
1499 557 l
217 557 l
217 727 l

ce} _d
/space{651 0 0 0 0 0 sc
ce} _d
/zero{1303 0 135 -29 1167 1520 sc
651 1360 m
547 1360 469 1309 416 1206 c
364 1104 338 950 338 745 c
338 540 364 387 416 284 c
469 182 547 131 651 131 c
756 131 834 182 886 284 c
939 387 965 540 965 745 c
965 950 939 1104 886 1206 c
834 1309 756 1360 651 1360 c

651 1520 m
818 1520 946 1454 1034 1321 c
1123 1189 1167 997 1167 745 c
1167 494 1123 302 1034 169 c
946 37 818 -29 651 -29 c
484 -29 356 37 267 169 c
179 302 135 494 135 745 c
135 997 179 1189 267 1321 c
356 1454 484 1520 651 1520 c

ce} _d
/one{1303 0 225 0 1114 1493 sc
254 170 m
584 170 l
584 1309 l
225 1237 l
225 1421 l
582 1493 l
784 1493 l
784 170 l
1114 170 l
1114 0 l
254 0 l
254 170 l

ce} _d
/two{1303 0 150 0 1098 1520 sc
393 170 m
1098 170 l
1098 0 l
150 0 l
150 170 l
227 249 331 356 463 489 c
596 623 679 709 713 748 c
778 821 823 882 848 932 c
874 983 887 1032 887 1081 c
887 1160 859 1225 803 1275 c
748 1325 675 1350 586 1350 c
523 1350 456 1339 385 1317 c
315 1295 240 1262 160 1217 c
160 1421 l
241 1454 317 1478 388 1495 c
459 1512 523 1520 582 1520 c
737 1520 860 1481 952 1404 c
1044 1327 1090 1223 1090 1094 c
1090 1033 1078 974 1055 919 c
1032 864 991 800 930 725 c
913 706 860 650 771 557 c
682 465 556 336 393 170 c

ce} _d
/three{1303 0 156 -29 1139 1520 sc
831 805 m
928 784 1003 741 1057 676 c
1112 611 1139 530 1139 434 c
1139 287 1088 173 987 92 c
886 11 742 -29 555 -29 c
492 -29 428 -23 361 -10 c
295 2 227 20 156 45 c
156 240 l
212 207 273 183 340 166 c
407 149 476 141 549 141 c
676 141 772 166 838 216 c
905 266 938 339 938 434 c
938 522 907 591 845 640 c
784 690 698 715 588 715 c
414 715 l
414 881 l
596 881 l
695 881 771 901 824 940 c
877 980 903 1037 903 1112 c
903 1189 876 1247 821 1288 c
767 1329 689 1350 588 1350 c
533 1350 473 1344 410 1332 c
347 1320 277 1301 201 1276 c
201 1456 l
278 1477 349 1493 416 1504 c
483 1515 547 1520 606 1520 c
759 1520 881 1485 970 1415 c
1059 1346 1104 1252 1104 1133 c
1104 1050 1080 980 1033 923 c
986 866 918 827 831 805 c

ce} _d
/five{1303 0 158 -29 1124 1493 sc
221 1493 m
1014 1493 l
1014 1323 l
406 1323 l
406 957 l
435 967 465 974 494 979 c
523 984 553 987 582 987 c
749 987 881 941 978 850 c
1075 759 1124 635 1124 479 c
1124 318 1074 193 974 104 c
874 15 733 -29 551 -29 c
488 -29 424 -24 359 -13 c
294 -2 227 14 158 35 c
158 238 l
218 205 280 181 344 165 c
408 149 476 141 547 141 c
662 141 754 171 821 232 c
888 293 922 375 922 479 c
922 583 888 665 821 726 c
754 787 662 817 547 817 c
493 817 439 811 385 799 c
332 787 277 768 221 743 c
221 1493 l

ce} _d
/seven{1303 0 168 0 1128 1493 sc
168 1493 m
1128 1493 l
1128 1407 l
586 0 l
375 0 l
885 1323 l
168 1323 l
168 1493 l

ce} _d
/A{1401 0 16 0 1384 1493 sc
700 1294 m
426 551 l
975 551 l
700 1294 l

586 1493 m
815 1493 l
1384 0 l
1174 0 l
1038 383 l
365 383 l
229 0 l
16 0 l
586 1493 l

ce} _d
/E{1294 0 201 0 1163 1493 sc
201 1493 m
1145 1493 l
1145 1323 l
403 1323 l
403 881 l
1114 881 l
1114 711 l
403 711 l
403 170 l
1163 170 l
1163 0 l
201 0 l
201 1493 l

ce} _d
/a{1255 0 123 -29 1069 1147 sc
702 563 m
553 563 450 546 393 512 c
336 478 307 420 307 338 c
307 273 328 221 371 182 c
414 144 473 125 547 125 c
649 125 731 161 792 233 c
854 306 885 402 885 522 c
885 563 l
702 563 l

1069 639 m
1069 0 l
885 0 l
885 170 l
843 102 791 52 728 19 c
665 -13 589 -29 498 -29 c
383 -29 292 3 224 67 c
157 132 123 218 123 326 c
123 452 165 547 249 611 c
334 675 460 707 627 707 c
885 707 l
885 725 l
885 810 857 875 801 921 c
746 968 668 991 567 991 c
503 991 441 983 380 968 c
319 953 261 930 205 899 c
205 1069 l
272 1095 338 1114 401 1127 c
464 1140 526 1147 586 1147 c
748 1147 869 1105 949 1021 c
1029 937 1069 810 1069 639 c

ce} _d
/c{1126 0 113 -29 999 1147 sc
999 1077 m
999 905 l
947 934 895 955 842 969 c
790 984 737 991 684 991 c
565 991 472 953 406 877 c
340 802 307 696 307 559 c
307 422 340 316 406 240 c
472 165 565 127 684 127 c
737 127 790 134 842 148 c
895 163 947 184 999 213 c
999 43 l
948 19 894 1 839 -11 c
784 -23 726 -29 664 -29 c
495 -29 361 24 262 130 c
163 236 113 379 113 559 c
113 742 163 885 263 990 c
364 1095 501 1147 676 1147 c
733 1147 788 1141 842 1129 c
896 1118 948 1100 999 1077 c

ce} _d
/d{1300 0 113 -29 1114 1556 sc
930 950 m
930 1556 l
1114 1556 l
1114 0 l
930 0 l
930 168 l
891 101 842 52 783 19 c
724 -13 654 -29 571 -29 c
436 -29 325 25 240 133 c
155 241 113 383 113 559 c
113 735 155 877 240 985 c
325 1093 436 1147 571 1147 c
654 1147 724 1131 783 1098 c
842 1066 891 1017 930 950 c

303 559 m
303 424 331 317 386 240 c
442 163 519 125 616 125 c
713 125 790 163 846 240 c
902 317 930 424 930 559 c
930 694 902 800 846 877 c
790 954 713 993 616 993 c
519 993 442 954 386 877 c
331 800 303 694 303 559 c

ce} _d
/e{1260 0 113 -29 1151 1147 sc
1151 606 m
1151 516 l
305 516 l
313 389 351 293 419 226 c
488 160 583 127 705 127 c
776 127 844 136 910 153 c
977 170 1043 196 1108 231 c
1108 57 l
1042 29 974 8 905 -7 c
836 -22 765 -29 694 -29 c
515 -29 374 23 269 127 c
165 231 113 372 113 549 c
113 732 162 878 261 985 c
360 1093 494 1147 662 1147 c
813 1147 932 1098 1019 1001 c
1107 904 1151 773 1151 606 c

967 660 m
966 761 937 841 882 901 c
827 961 755 991 664 991 c
561 991 479 962 417 904 c
356 846 320 764 311 659 c
967 660 l

ce} _d
/i{569 0 193 0 377 1556 sc
193 1120 m
377 1120 l
377 0 l
193 0 l
193 1120 l

193 1556 m
377 1556 l
377 1323 l
193 1323 l
193 1556 l

ce} _d
/l{569 0 193 0 377 1556 sc
193 1556 m
377 1556 l
377 0 l
193 0 l
193 1556 l

ce} _d
/m{1995 0 186 0 1821 1147 sc
1065 905 m
1111 988 1166 1049 1230 1088 c
1294 1127 1369 1147 1456 1147 c
1573 1147 1663 1106 1726 1024 c
1789 943 1821 827 1821 676 c
1821 0 l
1636 0 l
1636 670 l
1636 777 1617 857 1579 909 c
1541 961 1483 987 1405 987 c
1310 987 1234 955 1179 892 c
1124 829 1096 742 1096 633 c
1096 0 l
911 0 l
911 670 l
911 778 892 858 854 909 c
816 961 757 987 678 987 c
584 987 509 955 454 891 c
399 828 371 742 371 633 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
413 1015 463 1065 522 1098 c
581 1131 650 1147 731 1147 c
812 1147 881 1126 938 1085 c
995 1044 1038 984 1065 905 c

ce} _d
/o{1253 0 113 -29 1141 1147 sc
627 991 m
528 991 450 952 393 875 c
336 798 307 693 307 559 c
307 425 335 319 392 242 c
449 165 528 127 627 127 c
725 127 803 166 860 243 c
917 320 946 426 946 559 c
946 692 917 797 860 874 c
803 952 725 991 627 991 c

627 1147 m
787 1147 913 1095 1004 991 c
1095 887 1141 743 1141 559 c
1141 376 1095 232 1004 127 c
913 23 787 -29 627 -29 c
466 -29 340 23 249 127 c
158 232 113 376 113 559 c
113 743 158 887 249 991 c
340 1095 466 1147 627 1147 c

ce} _d
/p{1300 0 186 -426 1188 1147 sc
371 168 m
371 -426 l
186 -426 l
186 1120 l
371 1120 l
371 950 l
410 1017 458 1066 517 1098 c
576 1131 647 1147 729 1147 c
865 1147 975 1093 1060 985 c
1145 877 1188 735 1188 559 c
1188 383 1145 241 1060 133 c
975 25 865 -29 729 -29 c
647 -29 576 -13 517 19 c
458 52 410 101 371 168 c

997 559 m
997 694 969 800 913 877 c
858 954 781 993 684 993 c
587 993 510 954 454 877 c
399 800 371 694 371 559 c
371 424 399 317 454 240 c
510 163 587 125 684 125 c
781 125 858 163 913 240 c
969 317 997 424 997 559 c

ce} _d
/r{842 0 186 0 842 1147 sc
842 948 m
821 960 799 969 774 974 c
750 980 723 983 694 983 c
590 983 510 949 454 881 c
399 814 371 717 371 590 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
410 1014 460 1064 522 1097 c
584 1130 659 1147 748 1147 c
761 1147 775 1146 790 1144 c
805 1143 822 1140 841 1137 c
842 948 l

ce} _d
/s{1067 0 111 -29 967 1147 sc
907 1087 m
907 913 l
855 940 801 960 745 973 c
689 986 631 993 571 993 c
480 993 411 979 365 951 c
320 923 297 881 297 825 c
297 782 313 749 346 724 c
379 700 444 677 543 655 c
606 641 l
737 613 829 573 884 522 c
939 471 967 400 967 309 c
967 205 926 123 843 62 c
761 1 648 -29 504 -29 c
444 -29 381 -23 316 -11 c
251 0 183 18 111 41 c
111 231 l
179 196 246 169 312 151 c
378 134 443 125 508 125 c
595 125 661 140 708 169 c
755 199 778 241 778 295 c
778 345 761 383 727 410 c
694 437 620 462 506 487 c
442 502 l
328 526 246 563 195 612 c
144 662 119 730 119 817 c
119 922 156 1004 231 1061 c
306 1118 412 1147 549 1147 c
617 1147 681 1142 741 1132 c
801 1122 856 1107 907 1087 c

ce} _d
/t{803 0 55 0 754 1438 sc
375 1438 m
375 1120 l
754 1120 l
754 977 l
375 977 l
375 369 l
375 278 387 219 412 193 c
437 167 488 154 565 154 c
754 154 l
754 0 l
565 0 l
423 0 325 26 271 79 c
217 132 190 229 190 369 c
190 977 l
55 977 l
55 1120 l
190 1120 l
190 1438 l
375 1438 l

ce} _d
/u{1298 0 174 -29 1112 1147 sc
174 442 m
174 1120 l
358 1120 l
358 449 l
358 343 379 263 420 210 c
461 157 523 131 606 131 c
705 131 784 163 841 226 c
899 289 928 376 928 485 c
928 1120 l
1112 1120 l
1112 0 l
928 0 l
928 172 l
883 104 831 53 772 20 c
713 -13 645 -29 567 -29 c
438 -29 341 11 274 91 c
207 171 174 288 174 442 c

637 1147 m
637 1147 l

ce} _d
/w{1675 0 86 0 1589 1120 sc
86 1120 m
270 1120 l
500 246 l
729 1120 l
946 1120 l
1176 246 l
1405 1120 l
1589 1120 l
1296 0 l
1079 0 l
838 918 l
596 0 l
379 0 l
86 1120 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
75.6 223.2 translate
460.8 345.6 0 0 clipbox
gsave
0 0 m
460.8 0 l
460.8 345.6 l
0 345.6 l
cl
1.000 setgray
fill
grestore
gsave
83.6125 46.971875 m
450 46.971875 l
450 334.8 l
83.6125 334.8 l
cl
1.000 setgray
fill
grestore
0.800 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
100.266 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

96.4462 30.8469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
155.965 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

148.325 30.8469 translate
0 rotate
0 0 m /five glyphshow
7.63477 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
211.664 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

200.203 30.8469 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
267.363 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

255.902 30.8469 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /five glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
323.062 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

311.601 30.8469 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
378.761 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

367.3 30.8469 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /five glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
434.46 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

422.999 30.8469 translate
0 rotate
0 0 m /three glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

239.314 13.7063 translate
0 rotate
0 0 m /E glyphshow
8.8457 0 m /p glyphshow
17.7324 0 m /i glyphshow
21.6221 0 m /s glyphshow
28.916 0 m /o glyphshow
37.4814 0 m /d glyphshow
46.3682 0 m /e glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 52.9876 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 48.4251 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /two glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
40.5938 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 86.5779 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 82.0154 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /seven glyphshow
25.3242 0 m /five glyphshow
32.959 0 m /zero glyphshow
40.5938 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 120.168 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 115.606 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /five glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
40.5938 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 153.759 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 149.196 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /two glyphshow
25.3242 0 m /five glyphshow
32.959 0 m /zero glyphshow
40.5938 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 187.349 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 182.786 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
40.5938 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 220.939 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 216.377 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /seven glyphshow
17.6895 0 m /five glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 254.53 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 249.967 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /five glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 288.12 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 283.557 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /two glyphshow
17.6895 0 m /five glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.6125 321.71 o
grestore
/DejaVuSans 12.000 selectfont
gsave

68.9719 317.148 translate
0 rotate
0 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

21.4406 118.605 translate
90 rotate
0 0 m /A glyphshow
9.32715 0 m /c glyphshow
17.0244 0 m /c glyphshow
24.7217 0 m /u glyphshow
33.5947 0 m /m glyphshow
47.2324 0 m /u glyphshow
56.1055 0 m /l glyphshow
59.9951 0 m /a glyphshow
68.5742 0 m /t glyphshow
74.0635 0 m /e glyphshow
82.6768 0 m /d glyphshow
91.5635 0 m /space glyphshow
96.0137 0 m /r glyphshow
101.52 0 m /e glyphshow
110.133 0 m /w glyphshow
121.583 0 m /a glyphshow
130.162 0 m /r glyphshow
135.668 0 m /d glyphshow
grestore
1.500 setlinewidth
2 setlinecap
0.000 0.000 1.000 setrgbcolor
gsave
366.387 287.828 83.613 46.972 clipbox
100.266477 303.961056 m
101.380456 299.009842 l
102.494434 275.308507 l
103.608413 315.932649 l
104.722391 290.471181 l
105.836369 60.054972 l
106.950348 298.223828 l
108.064326 265.983832 l
109.178305 301.267112 l
110.292283 318.545977 l
111.406261 292.358957 l
112.52024 291.02878 l
113.634218 301.555989 l
114.748197 314.770424 l
115.862175 293.796623 l
116.976153 310.470862 l
118.090132 319.882872 l
119.20411 291.331093 l
120.318089 312.728132 l
121.432067 278.143531 l
122.546046 304.532092 l
123.660024 314.810732 l
124.774002 312.472845 l
125.887981 312.647515 l
127.001959 319.244655 l
128.115938 313.682097 l
129.229916 320.16503 l
130.343894 308.347953 l
131.457873 313.339476 l
132.571851 312.224277 l
133.68583 319.580559 l
134.799808 317.914478 l
135.913786 313.95082 l
137.027765 319.412607 l
138.141743 318.2571 l
139.255722 318.250382 l
140.3697 314.421084 l
141.483679 309.610949 l
142.597657 318.821417 l
143.711635 318.807981 l
144.825614 316.194654 l
145.939592 319.990361 l
147.053571 319.533532 l
148.167549 318.236945 l
149.281527 319.029677 l
150.395506 317.390469 l
151.509484 317.565139 l
152.623463 315.448948 l
153.737441 318.975933 l
154.85142 319.002805 l
155.965398 317.605447 l
157.079376 319.385735 l
158.193355 320.37329 l
159.307333 319.47307 l
160.421312 320.30611 l
161.53529 320.904017 l
162.649268 318.781109 l
163.763247 315.247406 l
164.877225 320.809965 l
165.991204 318.129456 l
167.105182 320.265801 l
168.21916 318.505668 l
169.333139 317.094874 l
170.447117 317.309852 l
171.561096 320.057541 l
172.675074 305.667445 l
173.789053 318.908752 l
174.903031 313.077471 l
176.017009 298.371626 l
177.130988 318.935624 l
178.244966 318.398179 l
179.358945 316.503685 l
180.472923 318.908752 l
181.586901 315.993112 l
182.70088 316.335733 l
183.814858 318.525822 l
184.928837 319.50666 l
186.042815 306.708745 l
187.156793 317.155337 l
188.270772 318.814699 l
189.38475 320.218775 l
190.498729 318.687056 l
191.612707 321.092123 l
192.726686 321.105559 l
193.840664 317.497958 l
194.954642 319.318554 l
196.068621 318.344435 l
197.182599 321.105559 l
198.296578 320.326264 l
199.410556 320.588268 l
200.524534 320.346418 l
201.638513 321.434745 l
202.752491 318.713928 l
203.86647 321.085405 l
204.980448 320.78981 l
206.094426 320.091132 l
207.208405 320.96448 l
208.322383 320.285955 l
209.436362 320.608423 l
210.55034 320.689039 l
211.664319 321.448181 l
212.778297 319.68133 l
213.892275 319.352144 l
215.006254 318.411615 l
216.120232 320.615141 l
217.234211 318.908752 l
218.348189 321.501925 l
219.462167 321.253357 l
220.576146 321.454899 l
221.690124 321.105559 l
222.804103 321.058533 l
223.918081 320.816683 l
225.03206 320.991352 l
226.146038 320.904017 l
227.260016 320.628577 l
228.373995 317.988377 l
229.487973 321.038379 l
230.601952 320.984634 l
231.71593 320.292674 l
232.829908 320.198621 l
233.943887 320.628577 l
235.057865 320.729348 l
236.171844 321.656441 l
238.3998 321.031661 l
239.513779 321.595978 l
240.627757 318.975933 l
241.741736 321.159304 l
242.855714 320.736066 l
243.969693 321.024943 l
245.083671 320.709194 l
246.197649 320.635295 l
247.311628 321.078687 l
248.425606 320.823401 l
249.539585 319.587277 l
250.653563 321.239921 l
251.767541 321.683313 l
252.88152 319.688048 l
253.995498 321.004788 l
256.223455 321.683313 l
257.337433 320.984634 l
258.451412 321.011507 l
259.56539 319.970206 l
260.679369 321.52208 l
261.793347 321.286947 l
262.907326 320.561396 l
264.021304 320.30611 l
265.135282 320.218775 l
266.249261 320.709194 l
267.363239 321.038379 l
268.477218 320.400163 l
269.591196 321.307101 l
270.705174 320.910736 l
271.819153 320.010515 l
272.933131 321.092123 l
276.275067 321.595978 l
277.389045 321.656441 l
278.503023 320.803247 l
279.617002 321.41459 l
280.73098 319.74851 l
281.844959 320.856991 l
282.958937 321.367564 l
284.072915 321.367564 l
285.186894 320.910736 l
286.300872 321.367564 l
287.414851 320.937608 l
288.528829 321.058533 l
289.642807 321.643005 l
290.756786 320.58155 l
291.870764 318.989369 l
292.984743 321.703467 l
294.098721 321.394436 l
296.326678 321.703467 l
297.440656 321.683313 l
298.554635 321.065251 l
299.668613 320.742784 l
300.782592 321.428027 l
301.89657 321.407872 l
303.010548 321.636287 l
305.238505 321.582542 l
306.352484 321.098841 l
307.466462 320.16503 l
308.58044 321.374282 l
309.694419 321.192894 l
311.922376 321.649723 l
313.036354 321.320538 l
314.150333 321.710185 l
315.264311 321.266793 l
317.492268 321.374282 l
318.606246 320.662167 l
319.720225 321.401154 l
320.834203 321.656441 l
323.06216 321.669877 l
324.176138 320.870427 l
325.290117 321.696749 l
326.404095 320.836837 l
327.518074 321.307101 l
328.632052 321.407872 l
329.74603 321.643005 l
330.860009 320.883863 l
331.973987 321.34741 l
333.087966 320.816683 l
334.201944 321.374282 l
335.315922 321.663159 l
336.429901 321.676595 l
337.543879 320.816683 l
338.657858 321.374282 l
339.771836 321.716903 l
340.885814 321.609414 l
341.999793 321.643005 l
343.113771 321.17274 l
344.22775 321.125714 l
345.341728 321.663159 l
346.455707 321.058533 l
347.569685 321.286947 l
348.683663 321.098841 l
349.797642 321.609414 l
350.91162 321.132432 l
352.025599 321.690031 l
353.139577 321.676595 l
354.253555 321.105559 l
356.481512 321.676595 l
357.595491 321.293665 l
358.709469 321.663159 l
359.823447 321.690031 l
360.937426 321.340692 l
362.051404 321.394436 l
363.165383 321.179458 l
364.279361 321.649723 l
365.39334 321.313819 l
366.507318 321.186176 l
367.621296 320.836837 l
368.735275 321.656441 l
369.849253 321.428027 l
370.963232 321.690031 l
372.07721 321.690031 l
373.191188 320.836837 l
374.305167 321.186176 l
375.419145 321.710185 l
377.647102 321.441463 l
378.76108 321.716903 l
379.875059 321.636287 l
380.989037 321.421309 l
382.103016 321.394436 l
383.216994 321.669877 l
384.330973 321.441463 l
385.444951 321.609414 l
386.558929 321.340692 l
388.786886 321.475053 l
389.900865 321.716903 l
392.128821 321.716903 l
393.2428 321.394436 l
394.356778 321.710185 l
395.470757 321.690031 l
396.584735 321.071969 l
397.698714 321.683313 l
398.812692 321.669877 l
399.92667 321.441463 l
403.268606 321.454899 l
404.382584 321.669877 l
405.496562 321.132432 l
406.610541 321.454899 l
407.724519 321.448181 l
408.838498 321.716903 l
411.066454 321.716903 l
412.180433 321.454899 l
413.294411 321.676595 l
416.636347 321.716903 l
419.978282 321.716903 l
421.09226 321.434745 l
422.206239 321.716903 l
433.346023 321.716903 l
433.346023 321.716903 l
stroke
grestore
0.800 setlinewidth
0 setlinejoin
0.000 setgray
gsave
83.6125 46.971875 m
83.6125 334.8 l
stroke
grestore
gsave
450 46.971875 m
450 334.8 l
stroke
grestore
gsave
83.6125 46.971875 m
450 46.971875 l
stroke
grestore
gsave
83.6125 334.8 m
450 334.8 l
stroke
grestore

end
showpage
