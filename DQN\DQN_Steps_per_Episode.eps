%!PS-Adobe-3.0 EPSF-3.0
%%Title: DQN_Steps_per_Episode.eps
%%Creator: Mat<PERSON><PERSON>lib v3.7.5, https://matplotlib.org/
%%CreationDate: Sat Feb 24 16:10:16 2024
%%Orientation: portrait
%%BoundingBox: 75 223 537 569
%%HiResBoundingBox: 75.600000 223.200000 536.400000 568.800000
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Matplotlib.
10 dict begin
/FontName /DejaVuSans def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-2090 -948 3673 2524] def
/FontType 3 def
/Encoding [/space /zero /one /two /three /four /five /E /T /a /d /e /i /k /n /o /p /s /t] def
/CharStrings 20 dict dup begin
/.notdef 0 def
/space{651 0 0 0 0 0 sc
ce} _d
/zero{1303 0 135 -29 1167 1520 sc
651 1360 m
547 1360 469 1309 416 1206 c
364 1104 338 950 338 745 c
338 540 364 387 416 284 c
469 182 547 131 651 131 c
756 131 834 182 886 284 c
939 387 965 540 965 745 c
965 950 939 1104 886 1206 c
834 1309 756 1360 651 1360 c

651 1520 m
818 1520 946 1454 1034 1321 c
1123 1189 1167 997 1167 745 c
1167 494 1123 302 1034 169 c
946 37 818 -29 651 -29 c
484 -29 356 37 267 169 c
179 302 135 494 135 745 c
135 997 179 1189 267 1321 c
356 1454 484 1520 651 1520 c

ce} _d
/one{1303 0 225 0 1114 1493 sc
254 170 m
584 170 l
584 1309 l
225 1237 l
225 1421 l
582 1493 l
784 1493 l
784 170 l
1114 170 l
1114 0 l
254 0 l
254 170 l

ce} _d
/two{1303 0 150 0 1098 1520 sc
393 170 m
1098 170 l
1098 0 l
150 0 l
150 170 l
227 249 331 356 463 489 c
596 623 679 709 713 748 c
778 821 823 882 848 932 c
874 983 887 1032 887 1081 c
887 1160 859 1225 803 1275 c
748 1325 675 1350 586 1350 c
523 1350 456 1339 385 1317 c
315 1295 240 1262 160 1217 c
160 1421 l
241 1454 317 1478 388 1495 c
459 1512 523 1520 582 1520 c
737 1520 860 1481 952 1404 c
1044 1327 1090 1223 1090 1094 c
1090 1033 1078 974 1055 919 c
1032 864 991 800 930 725 c
913 706 860 650 771 557 c
682 465 556 336 393 170 c

ce} _d
/three{1303 0 156 -29 1139 1520 sc
831 805 m
928 784 1003 741 1057 676 c
1112 611 1139 530 1139 434 c
1139 287 1088 173 987 92 c
886 11 742 -29 555 -29 c
492 -29 428 -23 361 -10 c
295 2 227 20 156 45 c
156 240 l
212 207 273 183 340 166 c
407 149 476 141 549 141 c
676 141 772 166 838 216 c
905 266 938 339 938 434 c
938 522 907 591 845 640 c
784 690 698 715 588 715 c
414 715 l
414 881 l
596 881 l
695 881 771 901 824 940 c
877 980 903 1037 903 1112 c
903 1189 876 1247 821 1288 c
767 1329 689 1350 588 1350 c
533 1350 473 1344 410 1332 c
347 1320 277 1301 201 1276 c
201 1456 l
278 1477 349 1493 416 1504 c
483 1515 547 1520 606 1520 c
759 1520 881 1485 970 1415 c
1059 1346 1104 1252 1104 1133 c
1104 1050 1080 980 1033 923 c
986 866 918 827 831 805 c

ce} _d
/four{1303 0 100 0 1188 1493 sc
774 1317 m
264 520 l
774 520 l
774 1317 l

721 1493 m
975 1493 l
975 520 l
1188 520 l
1188 352 l
975 352 l
975 0 l
774 0 l
774 352 l
100 352 l
100 547 l
721 1493 l

ce} _d
/five{1303 0 158 -29 1124 1493 sc
221 1493 m
1014 1493 l
1014 1323 l
406 1323 l
406 957 l
435 967 465 974 494 979 c
523 984 553 987 582 987 c
749 987 881 941 978 850 c
1075 759 1124 635 1124 479 c
1124 318 1074 193 974 104 c
874 15 733 -29 551 -29 c
488 -29 424 -24 359 -13 c
294 -2 227 14 158 35 c
158 238 l
218 205 280 181 344 165 c
408 149 476 141 547 141 c
662 141 754 171 821 232 c
888 293 922 375 922 479 c
922 583 888 665 821 726 c
754 787 662 817 547 817 c
493 817 439 811 385 799 c
332 787 277 768 221 743 c
221 1493 l

ce} _d
/E{1294 0 201 0 1163 1493 sc
201 1493 m
1145 1493 l
1145 1323 l
403 1323 l
403 881 l
1114 881 l
1114 711 l
403 711 l
403 170 l
1163 170 l
1163 0 l
201 0 l
201 1493 l

ce} _d
/T{1251 0 -6 0 1257 1493 sc
-6 1493 m
1257 1493 l
1257 1323 l
727 1323 l
727 0 l
524 0 l
524 1323 l
-6 1323 l
-6 1493 l

ce} _d
/a{1255 0 123 -29 1069 1147 sc
702 563 m
553 563 450 546 393 512 c
336 478 307 420 307 338 c
307 273 328 221 371 182 c
414 144 473 125 547 125 c
649 125 731 161 792 233 c
854 306 885 402 885 522 c
885 563 l
702 563 l

1069 639 m
1069 0 l
885 0 l
885 170 l
843 102 791 52 728 19 c
665 -13 589 -29 498 -29 c
383 -29 292 3 224 67 c
157 132 123 218 123 326 c
123 452 165 547 249 611 c
334 675 460 707 627 707 c
885 707 l
885 725 l
885 810 857 875 801 921 c
746 968 668 991 567 991 c
503 991 441 983 380 968 c
319 953 261 930 205 899 c
205 1069 l
272 1095 338 1114 401 1127 c
464 1140 526 1147 586 1147 c
748 1147 869 1105 949 1021 c
1029 937 1069 810 1069 639 c

ce} _d
/d{1300 0 113 -29 1114 1556 sc
930 950 m
930 1556 l
1114 1556 l
1114 0 l
930 0 l
930 168 l
891 101 842 52 783 19 c
724 -13 654 -29 571 -29 c
436 -29 325 25 240 133 c
155 241 113 383 113 559 c
113 735 155 877 240 985 c
325 1093 436 1147 571 1147 c
654 1147 724 1131 783 1098 c
842 1066 891 1017 930 950 c

303 559 m
303 424 331 317 386 240 c
442 163 519 125 616 125 c
713 125 790 163 846 240 c
902 317 930 424 930 559 c
930 694 902 800 846 877 c
790 954 713 993 616 993 c
519 993 442 954 386 877 c
331 800 303 694 303 559 c

ce} _d
/e{1260 0 113 -29 1151 1147 sc
1151 606 m
1151 516 l
305 516 l
313 389 351 293 419 226 c
488 160 583 127 705 127 c
776 127 844 136 910 153 c
977 170 1043 196 1108 231 c
1108 57 l
1042 29 974 8 905 -7 c
836 -22 765 -29 694 -29 c
515 -29 374 23 269 127 c
165 231 113 372 113 549 c
113 732 162 878 261 985 c
360 1093 494 1147 662 1147 c
813 1147 932 1098 1019 1001 c
1107 904 1151 773 1151 606 c

967 660 m
966 761 937 841 882 901 c
827 961 755 991 664 991 c
561 991 479 962 417 904 c
356 846 320 764 311 659 c
967 660 l

ce} _d
/i{569 0 193 0 377 1556 sc
193 1120 m
377 1120 l
377 0 l
193 0 l
193 1120 l

193 1556 m
377 1556 l
377 1323 l
193 1323 l
193 1556 l

ce} _d
/k{1186 0 186 0 1180 1556 sc
186 1556 m
371 1556 l
371 637 l
920 1120 l
1155 1120 l
561 596 l
1180 0 l
940 0 l
371 547 l
371 0 l
186 0 l
186 1556 l

ce} _d
/n{1298 0 186 0 1124 1147 sc
1124 676 m
1124 0 l
940 0 l
940 670 l
940 776 919 855 878 908 c
837 961 775 987 692 987 c
593 987 514 955 457 892 c
400 829 371 742 371 633 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
415 1013 467 1064 526 1097 c
586 1130 655 1147 733 1147 c
862 1147 959 1107 1025 1027 c
1091 948 1124 831 1124 676 c

ce} _d
/o{1253 0 113 -29 1141 1147 sc
627 991 m
528 991 450 952 393 875 c
336 798 307 693 307 559 c
307 425 335 319 392 242 c
449 165 528 127 627 127 c
725 127 803 166 860 243 c
917 320 946 426 946 559 c
946 692 917 797 860 874 c
803 952 725 991 627 991 c

627 1147 m
787 1147 913 1095 1004 991 c
1095 887 1141 743 1141 559 c
1141 376 1095 232 1004 127 c
913 23 787 -29 627 -29 c
466 -29 340 23 249 127 c
158 232 113 376 113 559 c
113 743 158 887 249 991 c
340 1095 466 1147 627 1147 c

ce} _d
/p{1300 0 186 -426 1188 1147 sc
371 168 m
371 -426 l
186 -426 l
186 1120 l
371 1120 l
371 950 l
410 1017 458 1066 517 1098 c
576 1131 647 1147 729 1147 c
865 1147 975 1093 1060 985 c
1145 877 1188 735 1188 559 c
1188 383 1145 241 1060 133 c
975 25 865 -29 729 -29 c
647 -29 576 -13 517 19 c
458 52 410 101 371 168 c

997 559 m
997 694 969 800 913 877 c
858 954 781 993 684 993 c
587 993 510 954 454 877 c
399 800 371 694 371 559 c
371 424 399 317 454 240 c
510 163 587 125 684 125 c
781 125 858 163 913 240 c
969 317 997 424 997 559 c

ce} _d
/s{1067 0 111 -29 967 1147 sc
907 1087 m
907 913 l
855 940 801 960 745 973 c
689 986 631 993 571 993 c
480 993 411 979 365 951 c
320 923 297 881 297 825 c
297 782 313 749 346 724 c
379 700 444 677 543 655 c
606 641 l
737 613 829 573 884 522 c
939 471 967 400 967 309 c
967 205 926 123 843 62 c
761 1 648 -29 504 -29 c
444 -29 381 -23 316 -11 c
251 0 183 18 111 41 c
111 231 l
179 196 246 169 312 151 c
378 134 443 125 508 125 c
595 125 661 140 708 169 c
755 199 778 241 778 295 c
778 345 761 383 727 410 c
694 437 620 462 506 487 c
442 502 l
328 526 246 563 195 612 c
144 662 119 730 119 817 c
119 922 156 1004 231 1061 c
306 1118 412 1147 549 1147 c
617 1147 681 1142 741 1132 c
801 1122 856 1107 907 1087 c

ce} _d
/t{803 0 55 0 754 1438 sc
375 1438 m
375 1120 l
754 1120 l
754 977 l
375 977 l
375 369 l
375 278 387 219 412 193 c
437 167 488 154 565 154 c
754 154 l
754 0 l
565 0 l
423 0 325 26 271 79 c
217 132 190 229 190 369 c
190 977 l
55 977 l
55 1120 l
190 1120 l
190 1438 l
375 1438 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
75.6 223.2 translate
460.8 345.6 0 0 clipbox
gsave
0 0 m
460.8 0 l
460.8 345.6 l
0 345.6 l
cl
1.000 setgray
fill
grestore
gsave
65.909375 46.971875 m
450 46.971875 l
450 334.8 l
65.909375 334.8 l
cl
1.000 setgray
fill
grestore
0.800 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.368 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

79.5477 30.8469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
141.758 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

134.118 30.8469 translate
0 rotate
0 0 m /five glyphshow
7.63477 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
200.148 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

188.687 30.8469 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
258.539 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

247.078 30.8469 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /five glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
316.929 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

305.468 30.8469 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
375.319 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

363.858 30.8469 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /five glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
433.709 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

422.248 30.8469 translate
0 rotate
0 0 m /three glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

230.462 13.7063 translate
0 rotate
0 0 m /E glyphshow
8.8457 0 m /p glyphshow
17.7324 0 m /i glyphshow
21.6221 0 m /s glyphshow
28.916 0 m /o glyphshow
37.4814 0 m /d glyphshow
46.3682 0 m /e glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 59.3359 o
grestore
/DejaVuSans 12.000 selectfont
gsave

51.2687 54.7734 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 107.277 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 102.714 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
22.9043 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 155.218 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 150.655 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
22.9043 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 203.159 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 198.596 translate
0 rotate
0 0 m /three glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
22.9043 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 251.1 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 246.537 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
22.9043 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 299.041 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 294.478 translate
0 rotate
0 0 m /five glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
22.9043 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

21.4406 149.948 translate
90 rotate
0 0 m /T glyphshow
6.17676 0 m /a glyphshow
14.7559 0 m /k glyphshow
22.3633 0 m /e glyphshow
30.9766 0 m /n glyphshow
39.8496 0 m /space glyphshow
44.2998 0 m /s glyphshow
51.5938 0 m /t glyphshow
57.083 0 m /e glyphshow
65.6963 0 m /p glyphshow
74.583 0 m /s glyphshow
grestore
1.500 setlinewidth
2 setlinecap
0.000 0.000 1.000 setrgbcolor
gsave
384.091 287.828 65.909 46.972 clipbox
83.36804 85.511638 m
84.535843 87.956629 l
85.703647 110.536836 l
86.871451 69.019937 l
88.039254 95.627187 l
89.207058 321.716903 l
90.374862 83.210471 l
91.542665 122.186497 l
92.710469 79.231368 l
93.878273 61.541142 l
95.046076 94.188958 l
96.21388 92.127495 l
97.381684 79.566955 l
98.549487 68.68435 l
99.717291 87.237514 l
100.885095 71.56081 l
102.052898 61.92467 l
103.220702 90.066032 l
104.388506 69.403465 l
105.556309 102.003339 l
106.724113 79.423132 l
107.891917 68.588468 l
109.05972 70.026698 l
110.227524 74.389328 l
111.395328 64.849071 l
112.563131 71.369046 l
113.730935 62.643785 l
114.898739 72.759334 l
116.066542 65.807891 l
117.234346 72.471688 l
118.40215 61.349378 l
119.569953 64.129956 l
120.737757 70.074639 l
121.905561 61.876729 l
123.073364 63.123195 l
124.241168 61.44526 l
125.408972 63.890251 l
126.576775 71.416987 l
127.744579 62.068493 l
128.912383 62.979372 l
130.080186 64.657307 l
131.24799 60.822027 l
132.415794 65.616127 l
133.583597 65.376422 l
134.751401 62.260257 l
135.919205 64.417602 l
137.087008 64.273779 l
138.254812 69.739052 l
139.422616 62.356139 l
140.590419 63.027313 l
141.758223 63.506723 l
142.926027 64.225838 l
144.09383 61.205555 l
145.261634 61.637024 l
146.429438 63.602605 l
147.597241 60.390559 l
148.765045 62.308198 l
149.932849 71.56081 l
151.100652 61.44526 l
152.268456 68.156999 l
153.43626 62.308198 l
154.604063 66.431123 l
155.771867 66.670828 l
156.939671 70.745813 l
158.107474 61.44526 l
159.275278 97.20924 l
160.443082 65.712009 l
161.610885 84.217231 l
162.778689 118.49504 l
163.946493 64.992894 l
165.114296 63.458782 l
166.2821 70.937577 l
167.449904 65.712009 l
168.617707 76.738437 l
169.785511 74.868738 l
170.953315 62.260257 l
172.121118 62.356139 l
173.288922 99.12688 l
174.456726 69.930816 l
175.624529 68.732291 l
176.792333 64.992894 l
177.960137 64.849071 l
179.12794 61.205555 l
180.295744 60.534382 l
181.463548 72.759334 l
182.631351 67.198179 l
183.799155 63.554664 l
184.966959 62.212316 l
187.302566 60.534382 l
188.47037 61.205555 l
189.638173 61.109673 l
190.805977 63.267018 l
191.97378 61.397319 l
195.477191 61.013791 l
196.644995 61.44526 l
197.812799 60.534382 l
198.980602 61.109673 l
200.148406 60.246736 l
201.31621 60.96585 l
202.484013 61.972611 l
203.651817 62.547903 l
204.819621 61.205555 l
205.987424 62.787608 l
207.155228 60.96585 l
208.323032 60.822027 l
209.490835 61.013791 l
210.658639 60.4385 l
211.826443 60.630263 l
212.994246 61.397319 l
215.329854 60.342618 l
216.497657 61.205555 l
217.665461 62.260257 l
218.833265 61.541142 l
220.001068 60.96585 l
221.168872 61.493201 l
222.336676 61.061732 l
223.504479 61.253496 l
225.840087 60.4385 l
228.175694 60.774086 l
229.343498 60.582322 l
230.511301 62.356139 l
231.679105 60.342618 l
232.846909 60.869968 l
234.014712 60.822027 l
236.35032 61.205555 l
237.518123 60.630263 l
238.685927 60.582322 l
239.853731 61.253496 l
241.021534 60.054972 l
242.189338 60.294677 l
243.357142 60.96585 l
244.524945 60.822027 l
245.692749 60.390559 l
246.860553 60.294677 l
248.028356 60.869968 l
255.035178 60.534382 l
256.202982 60.917909 l
258.538589 60.726145 l
259.706393 61.061732 l
260.874197 60.582322 l
262.042 61.109673 l
263.209804 60.630263 l
264.377608 60.582322 l
265.545411 60.774086 l
266.713215 60.774086 l
269.048822 60.390559 l
270.216626 60.630263 l
271.38443 60.342618 l
272.552233 61.541142 l
273.720037 60.486441 l
276.055644 60.4385 l
277.223448 60.294677 l
278.391252 60.486441 l
279.559055 60.246736 l
280.726859 60.678204 l
281.894663 60.486441 l
283.062466 60.534382 l
284.23027 62.212316 l
285.398074 60.198795 l
287.733681 60.630263 l
288.901485 60.294677 l
290.069288 60.294677 l
292.404896 60.869968 l
293.572699 60.294677 l
297.07611 60.486441 l
298.243914 60.678204 l
299.411718 60.534382 l
300.579521 61.013791 l
301.747325 60.486441 l
302.915129 60.246736 l
305.250736 60.4385 l
306.41854 60.630263 l
307.586343 60.198795 l
308.754147 60.774086 l
311.089754 60.486441 l
312.257558 61.157614 l
313.425362 60.390559 l
314.593165 60.342618 l
315.760969 60.486441 l
316.928773 60.342618 l
318.096576 60.4385 l
319.26438 60.294677 l
320.432184 60.582322 l
321.599987 60.678204 l
322.767791 60.390559 l
327.439005 60.630263 l
329.774613 60.342618 l
330.942416 60.342618 l
332.11022 60.582322 l
334.445827 60.198795 l
335.613631 60.486441 l
337.949238 60.294677 l
339.117042 60.486441 l
340.284846 60.390559 l
341.452649 60.678204 l
342.620453 60.726145 l
343.788257 60.486441 l
344.95606 60.534382 l
347.291668 60.294677 l
348.459471 60.294677 l
349.627275 60.486441 l
351.962882 60.294677 l
353.130686 60.726145 l
354.29849 60.390559 l
355.466293 60.294677 l
356.634097 60.630263 l
357.801901 60.390559 l
358.969704 60.294677 l
360.137508 60.390559 l
361.305312 60.630263 l
362.473115 60.294677 l
363.640919 60.534382 l
367.14433 60.342618 l
368.312134 60.294677 l
369.479937 60.534382 l
370.647741 60.246736 l
371.815545 60.246736 l
372.983348 60.486441 l
374.151152 60.246736 l
375.318956 60.198795 l
376.486759 60.390559 l
377.654563 60.294677 l
379.99017 60.342618 l
381.157974 60.246736 l
382.325778 60.534382 l
383.493581 60.534382 l
385.829189 60.150854 l
390.500403 60.4385 l
391.668207 60.246736 l
392.836011 60.294677 l
394.003814 60.582322 l
395.171618 60.294677 l
432.541335 60.198795 l
432.541335 60.198795 l
stroke
grestore
0.800 setlinewidth
0 setlinejoin
0.000 setgray
gsave
65.909375 46.971875 m
65.909375 334.8 l
stroke
grestore
gsave
450 46.971875 m
450 334.8 l
stroke
grestore
gsave
65.909375 46.971875 m
450 46.971875 l
stroke
grestore
gsave
65.909375 334.8 m
450 334.8 l
stroke
grestore

end
showpage
