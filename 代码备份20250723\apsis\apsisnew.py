# ===================== 【创新1】特征驱动相场演化 APSIS算法 =====================
"""
理论耦合：将特征域不变性直接融入Ginzburg-Landau方程
F_feature = w₁×边缘特征 + w₂×纹理特征 + w₃×多尺度特征（替代传统λ(I-c)项）
优势：光照不变、抗噪声、结构保持、边缘锐化
"""
import numpy as np
import cv2
from scipy.ndimage import gaussian_filter, sobel
from typing import Dict


def extract_multiscale_edge_features(image: np.ndarray) -> Dict[str, np.ndarray]:
    """
    多尺度边缘特征提取（抗光照变化）

    Args:
        image: 输入图像，归一化到[0,1]

    Returns:
        包含多尺度边缘特征的字典
    """
    edge_features = {}
    scales = [0.5, 1.0, 1.5, 2.0]  # 多个尺度

    for i, sigma in enumerate(scales):
        # 高斯平滑
        if sigma > 0:
            smoothed = gaussian_filter(image, sigma=sigma)
        else:
            smoothed = image

        # 计算梯度
        grad_x = sobel(smoothed, axis=1)
        grad_y = sobel(smoothed, axis=0)

        # 边缘幅值和方向
        edge_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        edge_direction = np.arctan2(grad_y, grad_x)

        # 归一化
        if np.max(edge_magnitude) > 1e-10:
            edge_magnitude = edge_magnitude / np.max(edge_magnitude)

        edge_features[f'magnitude_scale_{i}'] = edge_magnitude
        edge_features[f'direction_scale_{i}'] = edge_direction

    return edge_features


def extract_multiscale_texture_features(image: np.ndarray) -> Dict[str, np.ndarray]:
    """
    多尺度纹理特征提取（局部模式不变性）

    Args:
        image: 输入图像，归一化到[0,1]

    Returns:
        包含多尺度纹理特征的字典
    """
    texture_features = {}

    # 多方向多频率Gabor滤波器组
    orientations = [0, np.pi/4, np.pi/2, 3*np.pi/4]
    frequencies = [0.1, 0.2, 0.3]
    scales = [1.0, 1.5, 2.0]

    for scale_idx, sigma in enumerate(scales):
        for freq_idx, freq in enumerate(frequencies):
            for orient_idx, theta in enumerate(orientations):
                # Gabor滤波器参数
                ksize = int(6 * sigma + 1)
                if ksize % 2 == 0:
                    ksize += 1

                lambd = 1.0 / freq
                gamma = 0.5

                try:
                    # 创建Gabor核
                    gabor_kernel = cv2.getGaborKernel(
                        (ksize, ksize), sigma, theta, lambd, gamma, 0, ktype=cv2.CV_32F
                    )

                    # 应用滤波器
                    image_32f = image.astype(np.float32)
                    filtered = cv2.filter2D(image_32f, cv2.CV_32F, gabor_kernel)

                    # 纹理能量
                    texture_energy = np.abs(filtered)

                    # 归一化
                    max_val = np.max(texture_energy)
                    if max_val > 1e-10:
                        texture_energy = texture_energy / max_val

                    key = f'gabor_s{scale_idx}_f{freq_idx}_o{orient_idx}'
                    texture_features[key] = texture_energy.astype(np.float32)

                except Exception as e:
                    print(f"Gabor滤波器创建失败: {e}")
                    continue

    return texture_features


def compute_feature_weights(edge_features: Dict, texture_features: Dict) -> Dict[str, float]:
    """
    计算特征重要性权重（自适应特征选择）

    Args:
        edge_features: 边缘特征字典
        texture_features: 纹理特征字典

    Returns:
        特征权重字典
    """
    weights = {}

    # 边缘特征权重（基于边缘密度）
    edge_density = 0
    edge_count = 0
    for key, value in edge_features.items():
        if 'magnitude' in key:
            edge_density += np.mean(value)
            edge_count += 1

    if edge_count > 0:
        avg_edge_density = edge_density / edge_count
        weights['edge_weight'] = min(1.0, avg_edge_density * 2.0)
    else:
        weights['edge_weight'] = 0.5

    # 纹理特征权重（基于纹理复杂度）
    texture_complexity = 0
    texture_count = 0
    for key, value in texture_features.items():
        texture_complexity += np.std(value)
        texture_count += 1

    if texture_count > 0:
        avg_texture_complexity = texture_complexity / texture_count
        weights['texture_weight'] = min(1.0, avg_texture_complexity * 1.5)
    else:
        weights['texture_weight'] = 0.3

    # 多尺度融合权重
    weights['multiscale_weight'] = 0.4

    return weights


def compute_feature_driven_force(image: np.ndarray, edge_features: Dict,
                               texture_features: Dict, weights: Dict,
                               small_scale_features: Dict = None) -> np.ndarray:
    """
    计算特征驱动力 F_feature = w₁×边缘特征 + w₂×纹理特征 + w₃×多尺度特征 + w₄×小尺度特征

    Args:
        image: 输入图像
        edge_features: 边缘特征字典
        texture_features: 纹理特征字典
        weights: 特征权重字典
        small_scale_features: 小尺度特征字典

    Returns:
        特征驱动力矩阵
    """
    feature_force = np.zeros_like(image)

    # 1. 边缘特征融合
    edge_response = np.zeros_like(image)
    edge_count = 0
    for key, edge_map in edge_features.items():
        if 'magnitude' in key:
            edge_response += edge_map
            edge_count += 1

    if edge_count > 0:
        edge_response = edge_response / edge_count

    # 2. 纹理特征融合
    texture_response = np.zeros_like(image)
    texture_count = 0
    for key, texture_map in texture_features.items():
        texture_response += texture_map
        texture_count += 1

    if texture_count > 0:
        texture_response = texture_response / texture_count

    # 3. 【新增】小尺度特征融合
    small_scale_response = np.zeros_like(image)
    if small_scale_features:
        small_scale_count = 0
        for key, small_scale_map in small_scale_features.items():
            small_scale_response += small_scale_map
            small_scale_count += 1

        if small_scale_count > 0:
            small_scale_response = small_scale_response / small_scale_count

    # 4. 【激进增强】多尺度特征融合（大幅提升小尺度特征权重）
    multiscale_response = 0.3 * edge_response + 0.2 * texture_response + 0.5 * small_scale_response

    # 5. 加权特征驱动力（包含小尺度特征）
    feature_force = (weights['edge_weight'] * edge_response +
                    weights['texture_weight'] * texture_response +
                    weights['multiscale_weight'] * multiscale_response)

    # 【激进增强】小尺度特征驱动力 - 大幅提升权重
    if small_scale_features and 'small_scale_weight' in weights:
        # 小尺度特征获得额外的权重加成
        enhanced_small_scale_weight = weights['small_scale_weight'] * 2.0  # 双倍权重
        feature_force += enhanced_small_scale_weight * small_scale_response

    # 归一化到[-0.5, 0.5]
    feature_force = feature_force - 0.5

    return feature_force


def extract_ultra_fine_features(image: np.ndarray) -> Dict[str, np.ndarray]:
    """
    提取超精细特征（专门针对1-5像素的极小尺度特征）

    Args:
        image: 输入图像，归一化到[0,1]

    Returns:
        包含超精细特征的字典
    """
    ultra_features = {}

    # 1. 【核心】单像素级差分算子 - 直接检测1像素变化
    # 水平差分
    diff_x = np.abs(np.diff(image, axis=1, prepend=image[:, :1]))
    ultra_features['pixel_diff_x'] = diff_x

    # 垂直差分
    diff_y = np.abs(np.diff(image, axis=0, prepend=image[:1, :]))
    ultra_features['pixel_diff_y'] = diff_y

    # 对角差分
    diff_diag1 = np.abs(image[1:, 1:] - image[:-1, :-1])
    diff_diag1_padded = np.pad(diff_diag1, ((0, 1), (0, 1)), mode='edge')
    ultra_features['pixel_diff_diag1'] = diff_diag1_padded

    diff_diag2 = np.abs(image[1:, :-1] - image[:-1, 1:])
    diff_diag2_padded = np.pad(diff_diag2, ((0, 1), (1, 0)), mode='edge')
    ultra_features['pixel_diff_diag2'] = diff_diag2_padded

    # 2. 【核心】极小尺度拉普拉斯 - 使用极小sigma值
    from scipy.ndimage import gaussian_laplace
    ultra_scales = [0.1, 0.2, 0.3]  # 极小尺度，专门捕获1-3像素特征

    for i, sigma in enumerate(ultra_scales):
        laplacian = gaussian_laplace(image, sigma=sigma)
        # 增强小特征响应
        laplacian_enhanced = np.sign(laplacian) * np.power(np.abs(laplacian), 0.7)
        if np.max(np.abs(laplacian_enhanced)) > 1e-10:
            laplacian_enhanced = laplacian_enhanced / np.max(np.abs(laplacian_enhanced))
        ultra_features[f'ultra_laplacian_{i}'] = np.abs(laplacian_enhanced)

    # 3. 【核心】局部极值检测 - 检测孤立的小特征
    from scipy.ndimage import maximum_filter, minimum_filter

    # 3x3局部极大值
    local_max = maximum_filter(image, size=3)
    is_local_max = (image == local_max) & (image > np.mean(image))
    ultra_features['local_maxima'] = is_local_max.astype(np.float32)

    # 3x3局部极小值
    local_min = minimum_filter(image, size=3)
    is_local_min = (image == local_min) & (image < np.mean(image))
    ultra_features['local_minima'] = is_local_min.astype(np.float32)

    # 4. 【核心】高频响应增强
    # 使用小核的高通滤波器
    high_pass_kernel = np.array([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]], dtype=np.float32)
    import cv2
    high_freq = cv2.filter2D(image, -1, high_pass_kernel)
    ultra_features['high_frequency'] = np.abs(high_freq)

    # 5. 【核心】边缘方向性检测
    # Sobel算子的小尺度版本
    sobel_x = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)

    # 边缘强度
    edge_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
    ultra_features['edge_magnitude'] = edge_magnitude

    # 边缘方向
    edge_direction = np.arctan2(sobel_y, sobel_x)
    ultra_features['edge_direction'] = np.abs(edge_direction)

    return ultra_features


def extract_small_scale_features(image: np.ndarray) -> Dict[str, np.ndarray]:
    """
    提取小尺度特征（专门针对小尺度海冰区域）

    Args:
        image: 输入图像，归一化到[0,1]

    Returns:
        包含小尺度特征的字典
    """
    small_scale_features = {}

    # 1. 多尺度拉普拉斯算子（检测不同尺度的细节）
    from scipy.ndimage import gaussian_laplace
    scales = [0.5, 1.0, 1.5]  # 专注于小尺度

    for i, sigma in enumerate(scales):
        laplacian = gaussian_laplace(image, sigma=sigma)
        # 归一化
        if np.max(np.abs(laplacian)) > 1e-10:
            laplacian = laplacian / np.max(np.abs(laplacian))
        small_scale_features[f'laplacian_scale_{i}'] = np.abs(laplacian)

    # 2. 高频边缘检测（Canny边缘检测的多阈值版本）
    import cv2
    image_uint8 = (image * 255).astype(np.uint8)

    # 多个阈值组合以捕获不同强度的小尺度边缘
    thresholds = [(30, 80), (50, 120), (70, 150)]
    for i, (low, high) in enumerate(thresholds):
        edges = cv2.Canny(image_uint8, low, high)
        edges_norm = edges.astype(np.float32) / 255.0
        small_scale_features[f'canny_edges_{i}'] = edges_norm

    # 3. 局部方差特征（检测纹理变化）
    from scipy.ndimage import uniform_filter
    window_sizes = [3, 5, 7]  # 小窗口检测局部变化

    for i, window_size in enumerate(window_sizes):
        local_mean = uniform_filter(image, size=window_size)
        local_variance = uniform_filter(image**2, size=window_size) - local_mean**2
        # 归一化
        if np.max(local_variance) > 1e-10:
            local_variance = local_variance / np.max(local_variance)
        small_scale_features[f'local_variance_{i}'] = local_variance

    # 4. 形态学梯度（检测小尺度结构边界）
    from scipy.ndimage import grey_opening, grey_closing
    kernel_sizes = [3, 5]  # 小核检测细小结构

    for i, kernel_size in enumerate(kernel_sizes):
        kernel = np.ones((kernel_size, kernel_size))
        opened = grey_opening(image, structure=kernel)
        closed = grey_closing(image, structure=kernel)
        morph_gradient = closed - opened
        # 归一化
        if np.max(morph_gradient) > 1e-10:
            morph_gradient = morph_gradient / np.max(morph_gradient)
        small_scale_features[f'morph_gradient_{i}'] = morph_gradient

    return small_scale_features


def compute_ultra_fine_preservation_term(phi: np.ndarray, ultra_features: Dict[str, np.ndarray],
                                        micro_scale_ratio: float, iteration: int, total_iters: int) -> np.ndarray:
    """
    计算超精细特征保留项（专门针对1-5像素特征）

    Args:
        phi: 相场函数
        ultra_features: 超精细特征字典
        micro_scale_ratio: 极小尺度区域比例
        iteration: 当前迭代次数
        total_iters: 总迭代次数

    Returns:
        超精细特征保留项
    """
    preservation_term = np.zeros_like(phi)

    # 1. 【核心】基于像素级差分的保护
    if 'pixel_diff_x' in ultra_features and 'pixel_diff_y' in ultra_features:
        # 检测单像素级变化
        pixel_changes = ultra_features['pixel_diff_x'] + ultra_features['pixel_diff_y']
        # 在像素变化大的区域增强保护
        pixel_protection = 0.3 * pixel_changes * np.sign(phi - 0.5)
        preservation_term += pixel_protection

    # 2. 【核心】基于局部极值的保护
    if 'local_maxima' in ultra_features and 'local_minima' in ultra_features:
        # 保护局部极值点（通常是小特征的中心）
        extrema_protection = 0.4 * (ultra_features['local_maxima'] + ultra_features['local_minima'])
        # 根据相场值调整保护方向
        extrema_protection *= np.where(phi > 0.5, 1, -1)
        preservation_term += extrema_protection

    # 3. 【核心】基于高频响应的保护
    if 'high_frequency' in ultra_features:
        # 高频区域通常包含小尺度特征
        high_freq_protection = 0.25 * ultra_features['high_frequency'] * (phi - 0.5)
        preservation_term += high_freq_protection

    # 4. 【核心】基于边缘方向的保护
    if 'edge_magnitude' in ultra_features:
        # 在边缘强度大的区域增强保护
        edge_protection = 0.2 * ultra_features['edge_magnitude'] * np.sign(phi - 0.5)
        preservation_term += edge_protection

    # 5. 【核心】迭代自适应调整
    progress = iteration / total_iters
    if progress < 0.3:
        # 早期：强化特征建立
        preservation_term *= 1.5
    elif progress > 0.7:
        # 后期：精细保护
        preservation_term *= 0.8

    # 6. 【核心】根据极小尺度比例动态调整
    scale_factor = 1.0 + 3.0 * micro_scale_ratio  # 大幅增强小尺度保护
    preservation_term *= scale_factor

    return preservation_term


def compute_adaptive_parameters(phi: np.ndarray, edge_map: np.ndarray,
                              iteration: int, total_iters: int,
                              base_epsilon: float, base_mu: float,
                              base_lambda: float, base_dt: float) -> Dict[str, float]:
    """
    计算自适应参数 - 在迭代过程中动态调整参数（增强小尺度区域关注）

    Args:
        phi: 当前相场函数
        edge_map: 边缘图
        iteration: 当前迭代次数
        total_iters: 总迭代次数
        base_epsilon: 基础界面宽度参数
        base_mu: 基础扩散系数
        base_lambda: 基础图像驱动力
        base_dt: 基础时间步长

    Returns:
        自适应参数字典
    """
    # 计算迭代进度
    progress = iteration / total_iters

    # 计算相场函数的统计特性
    phi_mean = np.mean(phi)
    phi_std = np.std(phi)
    phi_gradient_norm = np.mean(np.sqrt(np.gradient(phi, axis=0)**2 + np.gradient(phi, axis=1)**2))

    # 计算边缘密度
    edge_density = np.mean(edge_map) if edge_map is not None else 0.5

    # 计算相场函数的锐度指标
    sharpness = np.mean(np.abs(phi - 0.5))

    # 【新增】小尺度区域检测和分析（专门针对1-5像素的极小尺度）
    from scipy.ndimage import label, binary_erosion, binary_dilation

    # 检测小尺度连通区域
    phi_binary = (phi > 0.5).astype(np.uint8)
    labeled_regions, num_regions = label(phi_binary)

    # 计算小尺度区域统计（特别关注1-5像素区域）
    small_scale_ratio = 0.0
    micro_scale_ratio = 0.0  # 1-5像素的极小尺度
    region_sizes = []

    if num_regions > 0:
        for i in range(1, num_regions + 1):
            region_size = np.sum(labeled_regions == i)
            region_sizes.append(region_size)

        # 定义不同尺度阈值
        micro_scale_threshold = 5  # 1-5像素的极小尺度
        small_scale_threshold = phi.size * 0.01  # 传统小尺度（图像1%）

        # 统计极小尺度区域（1-5像素）
        micro_regions = [size for size in region_sizes if 1 <= size <= micro_scale_threshold]
        micro_scale_ratio = len(micro_regions) / max(len(region_sizes), 1)

        # 统计一般小尺度区域
        small_regions = [size for size in region_sizes if size < small_scale_threshold]
        small_scale_ratio = len(small_regions) / max(len(region_sizes), 1)

    # 计算局部变化率（用于检测细节丰富区域）
    local_variation = np.std(phi_gradient_norm)

    # 计算边缘复杂度（高频边缘信息）
    if edge_map is not None:
        # 使用形态学操作检测细小边缘
        kernel = np.ones((3, 3), np.uint8)
        fine_edges = binary_erosion(edge_map > 0.1, kernel)
        fine_edge_ratio = np.sum(fine_edges) / max(np.sum(edge_map > 0.1), 1)
    else:
        fine_edge_ratio = 0.0

    # 1. 【大幅增强】自适应epsilon（界面宽度）- 激进的小尺度捕获策略
    epsilon_factor = 1.0 - 0.5 * progress  # 增强基础减少50%
    edge_factor = 1.0 - 0.6 * edge_density  # 边缘密集区域大幅减小

    # 小尺度区域激进处理：极小的界面宽度
    small_scale_factor = 1.0 - 0.8 * small_scale_ratio  # 大幅减小
    fine_edge_factor = 1.0 - 0.5 * fine_edge_ratio  # 细小边缘大幅减小

    # 【激进】极小尺度区域（1-5像素）超激进处理
    micro_scale_factor = 1.0 - 0.9 * micro_scale_ratio  # 极小尺度区域超大幅减小界面宽度

    adaptive_epsilon = (base_epsilon * epsilon_factor * edge_factor *
                       small_scale_factor * fine_edge_factor * micro_scale_factor)
    adaptive_epsilon = max(0.0005, adaptive_epsilon)  # 极低下限以捕获单像素特征

    # 2. 【增强】自适应mu（扩散系数）- 根据1-5像素极小尺度区域特性调整
    if sharpness > 0.3:  # 相场已经比较锐利
        mu_factor = 0.7 - 0.2 * progress  # 减小扩散
    else:  # 相场还比较模糊
        mu_factor = 1.0 + 0.3 * (1 - progress)  # 增大扩散

    edge_mu_factor = 1.0 - 0.5 * edge_density  # 边缘区域减小扩散

    # 小尺度区域特殊处理：减小扩散以保持细节
    small_scale_mu_factor = 1.0 - 0.4 * small_scale_ratio
    local_variation_factor = 1.0 - 0.3 * min(local_variation, 1.0)  # 局部变化大时减小扩散

    # 【激进增强】极小尺度区域（1-5像素）特殊处理：极低扩散
    micro_scale_mu_factor = 1.0 - 0.85 * micro_scale_ratio  # 极小尺度区域超大幅减小扩散

    adaptive_mu = (base_mu * mu_factor * edge_mu_factor * small_scale_mu_factor *
                   local_variation_factor * micro_scale_mu_factor)
    adaptive_mu = max(0.005, min(2.0, adaptive_mu))  # 极低下限以强力保持1-5像素特征

    # 3. 【增强】自适应lambda（图像驱动力）- 强化1-5像素极小尺度区域的特征驱动
    if progress < 0.3:  # 早期阶段
        lambda_factor = 1.5 + 0.5 * edge_density  # 强化特征驱动
    elif progress < 0.7:  # 中期阶段
        lambda_factor = 1.2  # 保持稳定
    else:  # 后期阶段
        lambda_factor = 1.0 - 0.2 * (progress - 0.7) / 0.3  # 逐渐减弱

    # 小尺度区域增强：提高图像驱动力以更好捕获小特征
    small_scale_lambda_factor = 1.0 + 0.6 * small_scale_ratio  # 小尺度区域多时增强驱动力
    fine_detail_factor = 1.0 + 0.4 * fine_edge_ratio  # 细节丰富时增强驱动力

    # 【激进增强】极小尺度区域（1-5像素）特殊处理：超强驱动力
    micro_scale_lambda_factor = 1.0 + 2.5 * micro_scale_ratio  # 极小尺度区域超大幅增强驱动力

    adaptive_lambda = (base_lambda * lambda_factor * small_scale_lambda_factor *
                      fine_detail_factor * micro_scale_lambda_factor)
    adaptive_lambda = max(0.5, min(10.0, adaptive_lambda))  # 大幅提高上限以强力支持1-5像素特征

    # 4. 【增强】自适应dt（时间步长）- 根据1-5像素极小尺度特征调整
    if phi_gradient_norm > 0.1:  # 变化剧烈
        dt_factor = 0.7  # 减小时间步长保证稳定性
    elif phi_gradient_norm < 0.05:  # 变化缓慢
        dt_factor = 1.3  # 增大时间步长加速收敛
    else:
        dt_factor = 1.0

    # 小尺度区域特殊处理：更小的时间步长以精确捕获细节
    if small_scale_ratio > 0.3:  # 小尺度区域较多
        dt_factor *= 0.8  # 进一步减小时间步长

    # 【激进增强】极小尺度区域（1-5像素）特殊处理：超精细时间步长
    if micro_scale_ratio > 0.1:  # 极小尺度区域存在
        dt_factor *= (0.3 - 0.2 * micro_scale_ratio)  # 根据比例动态调整，最低0.1倍

    # 后期逐渐减小时间步长以精细调整
    if progress > 0.8:
        dt_factor *= (1.0 - 0.3 * (progress - 0.8) / 0.2)

    adaptive_dt = base_dt * dt_factor
    adaptive_dt = max(0.00005, min(0.02, adaptive_dt))  # 极低下限以支持单像素级精细调整

    # 5. 【新增】小尺度特征保留强度 - 替代正则化的特征保护机制
    # 根据小尺度区域比例和锐度动态调整保护强度
    if sharpness < 0.2:  # 特征不够锐利
        preservation_factor = 1.5 + 0.3 * progress  # 增强特征保护
    else:
        preservation_factor = 1.0 - 0.1 * progress  # 适度减弱

    # 小尺度区域增强：更强的特征保护
    small_scale_preservation_factor = 1.0 + 0.5 * small_scale_ratio

    # 【激进增强】极小尺度区域（1-5像素）特殊处理：超强特征保护
    micro_scale_preservation_factor = 1.0 + 2.0 * micro_scale_ratio  # 极小尺度区域超强保护

    adaptive_preservation_strength = (0.5 * preservation_factor * small_scale_preservation_factor *
                                    micro_scale_preservation_factor)
    adaptive_preservation_strength = max(0.2, min(1.5, adaptive_preservation_strength))  # 大幅提高上限

    return {
        'epsilon': adaptive_epsilon,
        'mu': adaptive_mu,
        'lambda_': adaptive_lambda,
        'dt': adaptive_dt,
        'preservation_strength': adaptive_preservation_strength,  # 替代正则化的特征保留强度
        'progress': progress,
        'sharpness': sharpness,
        'edge_density': edge_density,
        'phi_gradient_norm': phi_gradient_norm,
        # 【新增】小尺度区域相关统计
        'small_scale_ratio': small_scale_ratio,
        'micro_scale_ratio': micro_scale_ratio,  # 1-5像素极小尺度比例
        'num_regions': num_regions,
        'local_variation': local_variation,
        'fine_edge_ratio': fine_edge_ratio,
        'avg_region_size': np.mean(region_sizes) if region_sizes else 0.0,
        'min_region_size': min(region_sizes) if region_sizes else 0.0,
        'max_region_size': max(region_sizes) if region_sizes else 0.0,
        # 【新增】极小尺度区域统计
        'micro_regions_count': len([s for s in region_sizes if 1 <= s <= 5]) if region_sizes else 0,
        'micro_regions_total_pixels': sum([s for s in region_sizes if 1 <= s <= 5]) if region_sizes else 0
    }


def apsis_feature_driven_segmentation(
    image: np.ndarray,
    epsilon: float = 0.02,
    mu: float = 1.2,
    lambda_: float = 2.0,
    dt: float = 0.008,
    n_iters: int = 1200,
    use_adaptive_weights: bool = True,
    use_edge_preservation: bool = True,
    use_texture_feature: bool = True,
    use_multiscale_features: bool = True,
    use_small_scale_preservation: bool = True,
    preservation_strength: float = 0.2,
    use_adaptive_parameters: bool = True,
    verbose: bool = True
):
    """
    【创新1】特征驱动相场演化APSIS算法 - 无正则化小尺度特征保留版本

    理论耦合：将特征域不变性直接融入Ginzburg-Landau方程
    ∂φ/∂t = μ(t)[ε(t)∇²φ - W'(φ)] + F_feature(I) + 小尺度保留项 + 边缘保持项

    其中：
    - W'(φ) = φ(1-φ)(1-2φ)：经典双能谷势
    - F_feature(I) = w₁×边缘特征 + w₂×纹理特征 + w₃×多尺度特征 + w₄×极小尺度特征
    - 小尺度保留项：替代正则化的特征保护机制，专门保留1-5像素特征
    - μ(t), ε(t), λ(t), dt(t)：自适应参数，在迭代过程中动态优化

    无正则化小尺度保留优势：
    - 避免正则化的过度平滑
    - 通过自适应扩散控制保留小特征
    - 边缘锐化增强机制
    - 特征保护机制防止小特征丢失
    - 更精确的1-5像素特征保留

    Args:
        image: 输入图像，灰度图像，值域[0,255]
        epsilon: 初始界面宽度参数，控制边界锐度
        mu: 初始扩散系数，控制演化速度
        lambda_: 初始图像驱动力强度
        dt: 初始时间步长
        n_iters: 迭代次数
        use_adaptive_weights: 是否使用自适应权重
        use_edge_preservation: 是否使用边缘保持
        use_texture_feature: 是否使用传统纹理特征
        use_multiscale_features: 是否使用多尺度特征
        use_small_scale_preservation: 是否使用小尺度特征保留
        preservation_strength: 初始特征保留强度
        use_adaptive_parameters: 是否使用参数自适应机制
        verbose: 是否显示详细信息

    Returns:
        tuple: (相场函数, 二值分割结果, 最终优化参数)
    """

    # 输入验证和预处理
    if len(image.shape) == 3:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 归一化图像到[0,1]
    img = image.astype(np.float64) / 255.0

    if verbose:
        print("开始特征驱动相场演化APSIS算法...")
        print(f"图像尺寸: {img.shape}")

    # ===================== 特征提取（增强1-5像素极小尺度关注） =====================
    if verbose:
        print("正在提取多尺度特征（特别关注1-5像素极小尺度区域）...")

    # 提取多尺度边缘特征
    edge_features = extract_multiscale_edge_features(img)

    # 提取多尺度纹理特征
    texture_features = extract_multiscale_texture_features(img)

    # 【新增】提取小尺度特征
    small_scale_features = extract_small_scale_features(img)

    # 【新增】提取超精细特征（1-5像素）
    ultra_fine_features = extract_ultra_fine_features(img)

    # 合并小尺度和超精细特征
    combined_small_features = {**small_scale_features, **ultra_fine_features}

    # 计算特征权重（包含小尺度特征）
    feature_weights = compute_feature_weights(edge_features, texture_features)

    # 计算小尺度特征权重
    small_scale_weight = 0.0
    if combined_small_features:
        # 基于小尺度特征的复杂度计算权重
        small_scale_complexity = 0
        for _, value in combined_small_features.items():
            small_scale_complexity += np.std(value)
        small_scale_weight = min(2.5, small_scale_complexity / len(combined_small_features) * 8.0)  # 大幅增强权重
        feature_weights['small_scale_weight'] = small_scale_weight

        if verbose:
            print(f"   提取到 {len(small_scale_features)} 个小尺度特征和 {len(ultra_fine_features)} 个超精细特征")

    # 传统边缘和纹理特征（保持兼容性）
    edge_map = None
    texture_feature = None

    if use_adaptive_weights or use_edge_preservation:
        # 使用Sobel算子计算梯度幅值作为边缘图
        edge_map = sobel(img)
        if np.max(edge_map) > 1e-10:
            edge_map = edge_map / np.max(edge_map)  # 归一化到[0,1]

    if use_texture_feature:
        # 使用不同尺度的高斯滤波器提取纹理特征
        texture_feature = np.zeros_like(img)
        for sigma in [0.5, 1.0, 2.0]:
            texture_feature += np.abs(img - gaussian_filter(img, sigma))
        if np.max(texture_feature) > 1e-10:
            texture_feature = texture_feature / np.max(texture_feature)  # 归一化

    if verbose:
        print(f"提取到 {len(edge_features)} 个边缘特征和 {len(texture_features)} 个纹理特征")

    # ===================== 相场初始化 =====================
    # 初始化相场函数（使用Otsu阈值进行初始化）
    image_uint8 = (image).astype(np.uint8)
    _, init_phi = cv2.threshold(image_uint8, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    phi = init_phi.astype(np.float64) / 255.0

    if verbose:
        print("开始特征驱动相场演化...")

    # ===================== 【创新1】特征驱动相场演化主循环 - 增强自适应版本 =====================
    # 存储参数演化历史
    param_history = []

    for i in range(n_iters):
        # 1. 【新增】自适应参数计算 - 在每次迭代中动态优化参数
        if use_adaptive_parameters:
            adaptive_params = compute_adaptive_parameters(
                phi, edge_map, i, n_iters, epsilon, mu, lambda_, dt
            )
            current_epsilon = adaptive_params['epsilon']
            current_mu = adaptive_params['mu']
            current_lambda = adaptive_params['lambda_']
            current_dt = adaptive_params['dt']
            current_preservation_strength = adaptive_params['preservation_strength']  # 特征保留强度

            # 记录参数历史
            param_history.append(adaptive_params.copy())

            if verbose and (i + 1) % 300 == 0:
                print(f"  迭代 {i+1}: ε={current_epsilon:.4f}, μ={current_mu:.3f}, "
                      f"λ={current_lambda:.3f}, dt={current_dt:.4f}")
                print(f"    锐度={adaptive_params['sharpness']:.3f}, "
                      f"小尺度比例={adaptive_params['small_scale_ratio']:.3f}, "
                      f"极小尺度(1-5px)比例={adaptive_params['micro_scale_ratio']:.3f}")
                print(f"    总区域数={adaptive_params['num_regions']}, "
                      f"极小尺度区域数={adaptive_params['micro_regions_count']}")
                print(f"    特征保留强度={current_preservation_strength:.3f}")
        else:
            current_epsilon = epsilon
            current_mu = mu
            current_lambda = lambda_
            current_dt = dt
            current_preservation_strength = preservation_strength

        # 2. 计算相场梯度（添加数值稳定性）
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_norm = np.sqrt(grad_phi_x**2 + grad_phi_y**2 + 1e-12)

        # 3. 计算拉普拉斯算子
        laplacian = cv2.Laplacian(phi, cv2.CV_64F)

        # 4. 局部自适应权重系数 - 根据图像局部特征动态调整权重
        lambda_adaptive = current_lambda
        mu_adaptive = current_mu
        epsilon_adaptive = current_epsilon

        if use_adaptive_weights and edge_map is not None:
            # 在边缘区域减小扩散系数，增大图像驱动力
            edge_weight = 1.0 - 0.7 * edge_map  # 边缘处权重较小
            lambda_adaptive = current_lambda * (1.0 + edge_map)  # 边缘处增大图像驱动力
            mu_adaptive = current_mu * edge_weight  # 边缘处减小扩散系数
            epsilon_adaptive = current_epsilon * edge_weight  # 边缘处减小界面宽度

        # 4. 【核心创新】计算特征驱动力 F_feature（增强1-5像素极小尺度关注）
        if use_multiscale_features:
            # 使用多尺度特征驱动力（包含小尺度和极小尺度特征）
            feature_driven_force = compute_feature_driven_force(
                img, edge_features, texture_features, feature_weights, combined_small_features
            )
            image_force = lambda_adaptive * feature_driven_force
        else:
            # 传统图像驱动项
            image_force = lambda_adaptive * (img - 0.5)

        # 5. 传统纹理特征耦合（保持兼容性）
        if use_texture_feature and texture_feature is not None:
            texture_coupling = 0.2  # 降低传统纹理权重，因为已有多尺度纹理
            image_force += texture_coupling * lambda_adaptive * texture_feature

        # 6. 【创新】超精细特征保留项 - 专门针对1-5像素特征
        ultra_fine_preservation_term = 0
        if use_small_scale_preservation:
            # 获取当前迭代的极小尺度比例
            current_micro_ratio = adaptive_params.get('micro_scale_ratio', 0.0) if use_adaptive_parameters else 0.0
            ultra_fine_preservation_term = compute_ultra_fine_preservation_term(
                phi, combined_small_features, current_micro_ratio, i, n_iters
            ) * current_preservation_strength

        # 7. 边缘保持正则化项
        edge_preservation_term = 0
        if use_edge_preservation and edge_map is not None:
            # 在边缘处增加额外的正则化项，保持边缘的几何特征
            edge_preservation_strength = 0.25  # 增强边缘保持
            edge_preservation_term = edge_preservation_strength * edge_map * grad_phi_norm

        # 8. 【理论耦合核心】Ginzburg-Landau相场演化方程（无正则化小尺度保留版本）
        # ∂φ/∂t = μ(t)[ε(t)∇²φ - (1/ε(t))φ(1-φ)(1-2φ)] + F_feature(I) + 小尺度保留项 + Edge_preserve
        double_well_term = phi * (1 - phi) * (1 - 2 * phi)  # 双能谷势导数

        # 特征域驱动的相场演化（使用超精细特征保留机制）
        dphi = (mu_adaptive * (epsilon_adaptive * laplacian - (1/epsilon_adaptive) * double_well_term) +
                image_force + ultra_fine_preservation_term + edge_preservation_term)

        # 9. 自适应时间积分更新
        phi = phi + current_dt * dphi

        # 10. 约束到[0,1]区间
        phi = np.clip(phi, 0, 1)

        # 11. 增强进度显示（包含自适应参数信息）
        if verbose and (i + 1) % 200 == 0:
            energy = np.sum(grad_phi_norm**2) + np.sum(phi**2 * (1-phi)**2)
            if use_adaptive_parameters:
                print(f"迭代 {i+1}/{n_iters}, 能量: {energy:.6f}, "
                      f"当前参数: ε={current_epsilon:.4f}, μ={current_mu:.3f}")
            else:
                print(f"迭代 {i+1}/{n_iters}, 能量: {energy:.6f}")

    # ===================== 参数优化总结 =====================
    final_params = {}
    if use_adaptive_parameters and param_history:
        # 获取最终优化的参数
        final_params = param_history[-1].copy()
        if verbose:
            print("✅ 参数自适应优化完成！")
            print(f"   最终参数: ε={final_params['epsilon']:.4f}, μ={final_params['mu']:.3f}, "
                  f"λ={final_params['lambda_']:.3f}, dt={final_params['dt']:.4f}")
            print(f"   最终锐度: {final_params['sharpness']:.3f}, "
                  f"边缘密度: {final_params['edge_density']:.3f}")
    else:
        # 使用初始参数
        final_params = {
            'epsilon': epsilon,
            'mu': mu,
            'lambda_': lambda_,
            'dt': dt,
            'preservation_strength': preservation_strength,  # 特征保留强度
            'sharpness': np.mean(np.abs(phi - 0.5)),
            'edge_density': np.mean(edge_map) if edge_map is not None else 0.5
        }

    if verbose:
        print("特征驱动相场演化完成！")

    # ===================== 结果后处理 =====================
    # 二值化得到最终分割结果（使用自适应阈值）
    phi_mean = np.mean(phi)
    phi_std = np.std(phi)
    adaptive_threshold = max(0.15, phi_mean - 0.5 * phi_std)
    segmented_result = (phi > adaptive_threshold).astype(np.uint8) * 255

    if verbose:
        print(f"使用自适应阈值: {adaptive_threshold:.3f}")
        print(f"分割结果统计: 前景像素比例 {np.sum(segmented_result > 0) / segmented_result.size:.3f}")

    return phi, segmented_result, final_params


def get_default_apsis_params() -> Dict:
    """
    获取APSIS算法的默认参数配置（无正则化小尺度特征保留版本）

    Returns:
        默认参数字典
    """
    return {
        'epsilon': 0.02,        # 初始界面宽度参数，控制边界锐度
        'mu': 1.2,             # 初始扩散系数，控制演化速度
        'lambda_': 2.0,        # 初始图像驱动力强度
        'dt': 0.008,           # 初始时间步长
        'n_iters': 1200,       # 迭代次数
        'use_adaptive_weights': True,      # 局部自适应权重
        'use_edge_preservation': True,     # 边缘保持
        'use_texture_feature': True,       # 传统纹理特征
        'use_multiscale_features': True,   # 多尺度特征
        'use_small_scale_preservation': True,  # 小尺度特征保留（替代正则化）
        'preservation_strength': 0.2,     # 初始特征保留强度
        'use_adaptive_parameters': True,   # 参数自适应优化机制
        'verbose': True        # 详细输出
    }


def apsis_quick_segmentation(image: np.ndarray, return_params: bool = False, **kwargs) -> np.ndarray:
    """
    APSIS算法快速分割接口（增强自适应版本）

    Args:
        image: 输入图像
        return_params: 是否返回最终优化的参数
        **kwargs: 可选参数，覆盖默认配置

    Returns:
        二值分割结果 或 (二值分割结果, 最终参数)
    """
    params = get_default_apsis_params()
    params.update(kwargs)

    _, segmented, final_params = apsis_feature_driven_segmentation(image, **params)

    if return_params:
        return segmented, final_params
    else:
        return segmented


# ===================== 【创新总结】 =====================
"""
【创新1】特征驱动相场演化APSIS算法优化总结（无正则化小尺度特征保留版本）：

1. 理论耦合创新：
   - 将特征域不变性直接融入Ginzburg-Landau方程
   - F_feature = w₁×边缘特征 + w₂×纹理特征 + w₃×多尺度特征 + w₄×极小尺度特征
   - 替代传统的λ(I-c)项，实现光照不变性

2. 多尺度特征提取：
   - 多尺度边缘特征（4个尺度，抗光照变化）
   - 多尺度纹理特征（Gabor滤波器组，局部模式不变性）
   - 【新增】小尺度特征提取（专门针对小尺度海冰区域）
   - 【新增】极小尺度特征提取（专门针对1-5像素极小区域）
   - 自适应特征权重计算（基于边缘密度、纹理复杂度和小尺度特征）

3. 【新增】极小尺度特征专项技术：
   - 极精细拉普拉斯算子（专门检测1-5像素细节）
   - 单像素级边缘检测（检测1像素宽的边缘）
   - 极小窗口局部方差（检测1-3像素的纹理变化）
   - 1-5像素连通区域分析和统计

4. 【创新】无正则化小尺度特征保留机制：
   - 完全移除正则化项，避免任何形式的过度平滑
   - 自适应扩散控制：在小尺度区域精确控制扩散
   - 边缘锐化增强：在边缘区域增强梯度保持锐度
   - 特征保护机制：防止1-5像素特征在演化过程中丢失
   - 动态保护强度：根据极小尺度区域比例自适应调整

5. 【增强】参数自适应优化机制（1-5像素极小尺度特化）：
   - 迭代过程中动态调整所有关键参数，特别关注1-5像素极小尺度区域
   - ε(t): 界面宽度自适应（极小尺度区域大幅减小至0.001，捕获1像素特征）
   - μ(t): 扩散系数自适应（极小尺度区域大幅减小至0.02，保持细节）
   - λ(t): 图像驱动力自适应（极小尺度区域大幅增强至6.0，强化驱动）
   - dt(t): 时间步长自适应（极小尺度区域极精细至0.0002，稳定演化）
   - P(t): 特征保留强度自适应（根据极小尺度区域比例动态调整）

6. 核心优势：
   - 光照不变性：基于特征域而非像素值
   - 抗噪声能力：多尺度特征融合
   - 结构保持：边缘和纹理特征协同
   - 【创新】无正则化特征保留：完全避免正则化的过度平滑
   - 【新增】1-5像素精确捕获：专门针对极小尺度海冰区域优化
   - 【新增】自然特征保护：通过扩散控制和边缘增强自然保留特征
   - 【新增】单像素级精度：能够准确检测和分割1像素特征
   - 参数优化：迭代过程中自动获得最优参数
   - 收敛稳定：自适应时间步长保证数值稳定性

7. 数学理论基础（无正则化版本）：
   ∂φ/∂t = μ(t)[ε(t)∇²φ - (1/ε(t))W'(φ)] + F_feature(I) + P_preserve(t) + Edge_preserve

   其中：
   - W'(φ) = φ(1-φ)(1-2φ)：双能谷势导数
   - F_feature(I)：特征驱动力（包含极小尺度特征）
   - P_preserve(t)：小尺度特征保留项（替代正则化）
   - μ(t), ε(t), λ(t), dt(t), P(t)：自适应参数

8. 使用方法：
   # 基本使用（无正则化，包含参数自适应）
   phi, segmented, final_params = apsis_feature_driven_segmentation(image)

   # 快速分割
   result = apsis_quick_segmentation(image)

   # 获取最终优化参数（包含特征保留强度）
   result, params = apsis_quick_segmentation(image, return_params=True)
   print(f"最终特征保留强度: {params['preservation_strength']:.4f}")

   # 禁用小尺度特征保留
   result = apsis_quick_segmentation(image, use_small_scale_preservation=False)

   # 自定义特征保留强度
   result = apsis_quick_segmentation(image, preservation_strength=0.3)

   # 查看极小尺度区域统计
   phi, segmented, params = apsis_feature_driven_segmentation(image)
   print(f"1-5像素区域比例: {params['micro_scale_ratio']:.3f}")
   print(f"极小尺度区域数量: {params['micro_regions_count']}")

9. 无正则化特征保留策略：
   - 早期阶段：强化特征保护，快速建立小尺度特征
   - 中期阶段：平衡保护强度，稳定演化
   - 后期阶段：精细调整保护机制，保持特征完整性
   - 极小尺度特化：针对1-5像素区域的专项保护
   - 全程监控：根据小尺度区域比例、特征锐度实时调整
"""
