"""
RRT vs RRT* 算法对比分析
"""
import numpy as np
import matplotlib.pyplot as plt
import time
from RRT_PathPlanning import RRTPathPlanner, RRTStarPathPlanner

def create_test_environments():
    """创建不同复杂度的测试环境"""
    environments = {}
    
    # 简单环境
    simple_grid = np.zeros((30, 30))
    simple_grid[10:20, 15] = 1  # 简单障碍物
    environments['简单环境'] = {
        'grid': simple_grid,
        'start': (5, 5),
        'goal': (25, 25),
        'description': '30x30网格，简单障碍物'
    }
    
    # 中等环境
    medium_grid = np.zeros((40, 40))
    medium_grid[10:30, 15] = 1  # 垂直墙
    medium_grid[20, 10:30] = 1  # 水平墙
    medium_grid[20, 13:17] = 0  # 在墙中间留个缺口
    medium_grid[5:10, 5:10] = 1  # 小块障碍
    environments['中等环境'] = {
        'grid': medium_grid,
        'start': (5, 5),
        'goal': (35, 35),
        'description': '40x40网格，中等复杂度'
    }
    
    # 复杂环境
    complex_grid = np.zeros((50, 50))
    # 创建迷宫式障碍物
    complex_grid[10:40, 20] = 1
    complex_grid[10:40, 30] = 1
    complex_grid[15, 10:40] = 1
    complex_grid[35, 10:40] = 1
    complex_grid[25, 20:30] = 1
    # 留出通道
    complex_grid[25, 18:22] = 0
    complex_grid[25, 28:32] = 0
    
    environments['复杂环境'] = {
        'grid': complex_grid,
        'start': (5, 5),
        'goal': (45, 45),
        'description': '50x50网格，复杂迷宫'
    }
    
    return environments

def benchmark_rrt_algorithms(env_data, num_runs=3):
    """测试RRT和RRT*算法性能"""
    grid = env_data['grid']
    start = env_data['start']
    goal = env_data['goal']
    
    print(f"测试环境: {env_data['description']}")
    print(f"起点: {start}, 终点: {goal}")
    
    # 算法配置
    algorithms = {
        'RRT': {
            'class': RRTPathPlanner,
            'params': {'max_iter': 1000, 'step_size': 3.0, 'goal_sample_rate': 0.1}
        },
        'RRT*': {
            'class': RRTStarPathPlanner,
            'params': {'max_iter': 1000, 'step_size': 3.0, 'goal_sample_rate': 0.1, 'search_radius': 8.0}
        }
    }
    
    results = {}
    
    for alg_name, config in algorithms.items():
        print(f"\n测试 {alg_name}...")
        
        alg_results = []
        best_path = None
        
        for run in range(num_runs):
            print(f"  运行 {run + 1}/{num_runs}")
            
            start_time = time.time()
            
            try:
                planner = config['class'](start, goal, grid, **config['params'])
                path = planner.plan()
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                if path:
                    path_length = len(path)
                    
                    # 计算路径总距离
                    total_distance = 0
                    for i in range(len(path) - 1):
                        p1, p2 = path[i], path[i+1]
                        total_distance += np.linalg.norm(np.array(p2) - np.array(p1))
                    
                    result = {
                        'success': True,
                        'time': execution_time,
                        'path_length': path_length,
                        'total_distance': total_distance,
                        'iterations': planner.iterations_used
                    }
                    
                    if best_path is None or path_length < len(best_path):
                        best_path = path
                    
                    print(f"    ✅ 成功: {execution_time:.2f}s, 长度: {path_length}, 距离: {total_distance:.2f}")
                else:
                    result = {
                        'success': False,
                        'time': execution_time,
                        'path_length': 0,
                        'total_distance': float('inf'),
                        'iterations': planner.iterations_used
                    }
                    print(f"    ❌ 失败: {execution_time:.2f}s")
                
                alg_results.append(result)
                
            except Exception as e:
                print(f"    💥 错误: {e}")
                alg_results.append({
                    'success': False,
                    'time': float('inf'),
                    'path_length': 0,
                    'total_distance': float('inf'),
                    'iterations': 0
                })
        
        results[alg_name] = {
            'runs': alg_results,
            'best_path': best_path
        }
    
    return results

def analyze_results(results):
    """分析结果"""
    analysis = {}
    
    for alg_name, alg_data in results.items():
        runs = alg_data['runs']
        successful_runs = [r for r in runs if r['success']]
        
        if successful_runs:
            analysis[alg_name] = {
                'success_rate': len(successful_runs) / len(runs),
                'avg_time': np.mean([r['time'] for r in successful_runs]),
                'avg_path_length': np.mean([r['path_length'] for r in successful_runs]),
                'avg_distance': np.mean([r['total_distance'] for r in successful_runs]),
                'avg_iterations': np.mean([r['iterations'] for r in successful_runs]),
                'std_time': np.std([r['time'] for r in successful_runs]),
                'std_path_length': np.std([r['path_length'] for r in successful_runs]),
                'best_path': alg_data['best_path']
            }
        else:
            analysis[alg_name] = {
                'success_rate': 0,
                'avg_time': float('inf'),
                'avg_path_length': 0,
                'avg_distance': float('inf'),
                'avg_iterations': 0,
                'std_time': 0,
                'std_path_length': 0,
                'best_path': None
            }
    
    return analysis

def visualize_comparison(env_data, analysis):
    """可视化对比结果"""
    grid = env_data['grid']
    start = env_data['start']
    goal = env_data['goal']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. RRT路径
    ax = axes[0, 0]
    ax.imshow(grid, cmap='gray_r', origin='upper', alpha=0.7)
    
    if analysis['RRT']['best_path']:
        path = np.array(analysis['RRT']['best_path'])
        ax.plot(path[:, 1], path[:, 0], 'g-', linewidth=2, label='RRT Path')
        ax.plot(path[:, 1], path[:, 0], 'go', markersize=3)
    
    ax.plot(start[1], start[0], 'bo', markersize=10, label='Start')
    ax.plot(goal[1], goal[0], 'ro', markersize=10, label='Goal')
    ax.set_title(f'RRT\n成功率: {analysis["RRT"]["success_rate"]*100:.0f}%')
    ax.legend()
    ax.axis('off')
    
    # 2. RRT*路径
    ax = axes[0, 1]
    ax.imshow(grid, cmap='gray_r', origin='upper', alpha=0.7)
    
    if analysis['RRT*']['best_path']:
        path = np.array(analysis['RRT*']['best_path'])
        ax.plot(path[:, 1], path[:, 0], 'm-', linewidth=2, label='RRT* Path')
        ax.plot(path[:, 1], path[:, 0], 'mo', markersize=3)
    
    ax.plot(start[1], start[0], 'bo', markersize=10, label='Start')
    ax.plot(goal[1], goal[0], 'ro', markersize=10, label='Goal')
    ax.set_title(f'RRT*\n成功率: {analysis["RRT*"]["success_rate"]*100:.0f}%')
    ax.legend()
    ax.axis('off')
    
    # 3. 路径对比
    ax = axes[0, 2]
    ax.imshow(grid, cmap='gray_r', origin='upper', alpha=0.7)
    
    if analysis['RRT']['best_path']:
        path = np.array(analysis['RRT']['best_path'])
        ax.plot(path[:, 1], path[:, 0], 'g-', linewidth=2, label='RRT', alpha=0.7)
    
    if analysis['RRT*']['best_path']:
        path = np.array(analysis['RRT*']['best_path'])
        ax.plot(path[:, 1], path[:, 0], 'm-', linewidth=2, label='RRT*', alpha=0.7)
    
    ax.plot(start[1], start[0], 'bo', markersize=10, label='Start')
    ax.plot(goal[1], goal[0], 'ro', markersize=10, label='Goal')
    ax.set_title('路径对比')
    ax.legend()
    ax.axis('off')
    
    # 4. 执行时间对比
    ax = axes[1, 0]
    alg_names = ['RRT', 'RRT*']
    times = [analysis[alg]['avg_time'] for alg in alg_names]
    time_stds = [analysis[alg]['std_time'] for alg in alg_names]
    
    bars = ax.bar(alg_names, times, yerr=time_stds, capsize=5, 
                  color=['green', 'magenta'], alpha=0.7)
    ax.set_ylabel('平均执行时间 (秒)')
    ax.set_title('执行时间对比')
    ax.grid(True, alpha=0.3)
    
    for bar, time_val in zip(bars, times):
        if time_val != float('inf'):
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.1,
                   f'{time_val:.2f}s', ha='center', va='bottom')
    
    # 5. 路径长度对比
    ax = axes[1, 1]
    path_lengths = [analysis[alg]['avg_path_length'] for alg in alg_names]
    length_stds = [analysis[alg]['std_path_length'] for alg in alg_names]
    
    bars = ax.bar(alg_names, path_lengths, yerr=length_stds, capsize=5,
                  color=['green', 'magenta'], alpha=0.7)
    ax.set_ylabel('平均路径长度')
    ax.set_title('路径长度对比')
    ax.grid(True, alpha=0.3)
    
    for bar, length in zip(bars, path_lengths):
        if length > 0:
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
                   f'{length:.0f}', ha='center', va='bottom')
    
    # 6. 综合性能表
    ax = axes[1, 2]
    ax.axis('off')
    
    table_data = []
    for alg_name in alg_names:
        stats = analysis[alg_name]
        table_data.append([
            alg_name,
            f"{stats['success_rate']*100:.0f}%",
            f"{stats['avg_time']:.2f}±{stats['std_time']:.2f}",
            f"{stats['avg_path_length']:.0f}±{stats['std_path_length']:.0f}",
            f"{stats['avg_iterations']:.0f}"
        ])
    
    table = ax.table(cellText=table_data,
                    colLabels=['算法', '成功率', '时间(s)', '路径长度', '迭代次数'],
                    cellLoc='center',
                    loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data[0])):
        table[(0, i)].set_facecolor('#4ECDC4')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    ax.set_title('性能统计表', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.suptitle(f'RRT vs RRT* 对比 - {env_data["description"]}', fontsize=16, y=0.98)
    plt.show()

def main():
    """主函数"""
    print("🔬 RRT vs RRT* 算法性能对比")
    print("=" * 50)
    
    # 创建测试环境
    environments = create_test_environments()
    
    for env_name, env_data in environments.items():
        print(f"\n📍 测试环境: {env_name}")
        print("-" * 30)
        
        # 运行对比测试
        results = benchmark_rrt_algorithms(env_data, num_runs=3)
        
        # 分析结果
        analysis = analyze_results(results)
        
        # 可视化对比
        visualize_comparison(env_data, analysis)
        
        # 打印总结
        print(f"\n📊 {env_name} 总结:")
        for alg_name in ['RRT', 'RRT*']:
            stats = analysis[alg_name]
            print(f"  {alg_name}:")
            print(f"    成功率: {stats['success_rate']*100:.1f}%")
            print(f"    平均时间: {stats['avg_time']:.2f}±{stats['std_time']:.2f}秒")
            print(f"    平均路径长度: {stats['avg_path_length']:.1f}±{stats['std_path_length']:.1f}")
        
        # 比较分析
        if analysis['RRT']['success_rate'] > 0 and analysis['RRT*']['success_rate'] > 0:
            time_ratio = analysis['RRT*']['avg_time'] / analysis['RRT']['avg_time']
            length_ratio = analysis['RRT']['avg_path_length'] / analysis['RRT*']['avg_path_length']
            
            print(f"\n💡 对比分析:")
            print(f"  时间比较: RRT* {'更慢' if time_ratio > 1 else '更快'} {abs(time_ratio-1)*100:.1f}%")
            print(f"  路径质量: RRT* {'更优' if length_ratio > 1 else '更差'} {abs(length_ratio-1)*100:.1f}%")
    
    print(f"\n🎉 对比测试完成！")
    print(f"\n📝 总结:")
    print(f"  RRT: 快速探索，适合实时应用")
    print(f"  RRT*: 路径优化，适合质量要求高的场景")

if __name__ == "__main__":
    main()
