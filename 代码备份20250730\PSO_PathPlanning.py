import numpy as np
import matplotlib.pyplot as plt

class Particle:
    def __init__(self, path_points, grid_map):
        self.position = path_points  # 路径点序列
        self.velocity = np.zeros_like(path_points)  # 速度
        self.best_position = np.copy(path_points)  # 个体最优位置
        self.best_fitness = float('inf')  # 个体最优适应度
        self.current_fitness = float('inf')  # 当前适应度
        self.grid_map = grid_map

class PSOPathPlanner:
    def __init__(self, start, goal, grid_map, n_particles=50, max_iter=100, w=0.7, c1=1.4, c2=1.4):
        self.start = np.array(start)
        self.goal = np.array(goal)
        self.grid_map = np.array(grid_map)
        self.map_size = self.grid_map.shape
        self.n_particles = n_particles
        self.max_iter = max_iter
        self.w = w  # 惯性权重
        self.c1 = c1  # 个体学习因子
        self.c2 = c2  # 社会学习因子
        self.n_points = 10  # 路径点数量
        self.particles = []
        self.global_best_position = None
        self.global_best_fitness = float('inf')
        self.obstacles = self._extract_obstacles()
        self.initialize_particles()

    def _extract_obstacles(self):
        """提取障碍物位置"""
        obstacles = []
        for i in range(self.grid_map.shape[0]):
            for j in range(self.grid_map.shape[1]):
                if self.grid_map[i, j] == 1:
                    obstacles.append((j, i))  # (x, y)格式
        return obstacles

    def initialize_particles(self):
        for _ in range(self.n_particles):
            # 生成随机路径点
            path_points = np.zeros((self.n_points, 2))
            path_points[0] = self.start
            path_points[-1] = self.goal
            
            # 在起点和终点之间生成随机中间点
            for i in range(1, self.n_points-1):
                valid_point = False
                attempts = 0
                while not valid_point and attempts < 100:  # 限制尝试次数
                    x = np.random.uniform(0, self.map_size[1]-1)  # 注意坐标系
                    y = np.random.uniform(0, self.map_size[0]-1)
                    if not self._check_collision((x, y)):
                        path_points[i] = [x, y]
                        valid_point = True
                    attempts += 1

                # 如果找不到有效点，使用线性插值
                if not valid_point:
                    t = i / (self.n_points - 1)
                    path_points[i] = self.start * (1 - t) + self.goal * t
            
            particle = Particle(path_points, self.grid_map)
            self.evaluate_fitness(particle)
            self.particles.append(particle)
            
            if particle.current_fitness < self.global_best_fitness:
                self.global_best_fitness = particle.current_fitness
                self.global_best_position = np.copy(particle.position)

    def evaluate_fitness(self, particle):
        # 计算路径长度
        path_length = 0
        for i in range(len(particle.position)-1):
            path_length += np.linalg.norm(particle.position[i+1] - particle.position[i])
        
        # 计算碰撞惩罚
        collision_penalty = 0
        for i in range(len(particle.position)-1):
            if self._check_collision_line(particle.position[i], particle.position[i+1]):
                collision_penalty += 1000
        
        # 总适应度 = 路径长度 + 碰撞惩罚
        fitness = path_length + collision_penalty
        particle.current_fitness = fitness
        
        if fitness < particle.best_fitness:
            particle.best_fitness = fitness
            particle.best_position = np.copy(particle.position)

    def _check_collision(self, point):
        """检查点是否与障碍物碰撞"""
        x, y = int(point[0]), int(point[1])
        # 检查边界
        if x < 0 or x >= self.grid_map.shape[1] or y < 0 or y >= self.grid_map.shape[0]:
            return True
        # 检查障碍物
        return self.grid_map[y, x] == 1

    def _check_collision_line(self, start_point, end_point):
        # 检查两点之间的线段是否与障碍物相交
        num_checks = int(np.linalg.norm(end_point - start_point) * 2)
        for i in range(num_checks + 1):
            t = i / max(1, num_checks)
            point = start_point * (1 - t) + end_point * t
            if self._check_collision(point):
                return True
        return False

    def optimize(self):
        for iteration in range(self.max_iter):
            for particle in self.particles:
                # 更新速度
                r1, r2 = np.random.random(2)
                particle.velocity = (self.w * particle.velocity +
                                   self.c1 * r1 * (particle.best_position - particle.position) +
                                   self.c2 * r2 * (self.global_best_position - particle.position))
                
                # 更新位置（保持起点和终点不变）
                particle.position[1:-1] += particle.velocity[1:-1]
                
                # 确保路径点在地图范围内
                particle.position = np.clip(particle.position, [0, 0], [self.map_size[1]-1, self.map_size[0]-1])
                
                # 评估新位置
                self.evaluate_fitness(particle)
                
                # 更新全局最优
                if particle.current_fitness < self.global_best_fitness:
                    self.global_best_fitness = particle.current_fitness
                    self.global_best_position = np.copy(particle.position)
            
            if iteration % 10 == 0:
                print(f"迭代 {iteration}, 当前最优适应度: {self.global_best_fitness}")

        return self.global_best_position

    def get_path_as_points(self):
        """将PSO路径转换为点序列格式，与其他算法兼容"""
        if self.global_best_position is None:
            return None

        # 将连续路径点转换为离散网格点
        path_points = []
        for i in range(len(self.global_best_position) - 1):
            start_point = self.global_best_position[i]
            end_point = self.global_best_position[i + 1]

            # 在两点之间插值生成路径点
            distance = np.linalg.norm(end_point - start_point)
            num_points = max(2, int(distance * 2))  # 根据距离确定插值点数

            for j in range(num_points):
                t = j / (num_points - 1) if num_points > 1 else 0
                point = start_point * (1 - t) + end_point * t
                # 转换为整数坐标 (y, x) 格式以匹配其他算法
                grid_point = (int(round(point[1])), int(round(point[0])))
                if grid_point not in path_points:
                    path_points.append(grid_point)

        return path_points

def main():
    # 示例网格地图
    grid_map = [
        [0, 0, 0, 0, 0],
        [0, 1, 1, 1, 0],
        [0, 0, 0, 1, 1],
        [0, 1, 0, 0, 0],
        [0, 0, 0, 0, 0]
    ]
    
    start = (0, 0)
    goal = (4, 4)
    
    # 创建PSO路径规划器实例
    pso_planner = PSOPathPlanner(start, goal, grid_map)
    
    # 运行优化
    best_path = pso_planner.optimize()
    
    # 可视化结果
    plt.figure(figsize=(8, 8))
    
    # 绘制障碍物
    for obstacle in pso_planner.obstacles:
        plt.fill([obstacle[0], obstacle[2], obstacle[2], obstacle[0]],
                 [obstacle[1], obstacle[1], obstacle[3], obstacle[3]], 'red', alpha=0.3)
    
    # 绘制最优路径
    plt.plot(best_path[:, 0], best_path[:, 1], 'b-', linewidth=2, label='Path')
    plt.plot(start[0], start[1], 'go', label='Start')
    plt.plot(goal[0], goal[1], 'ro', label='Goal')
    
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('PSO 路径规划')
    plt.show()

if __name__ == "__main__":
    main()