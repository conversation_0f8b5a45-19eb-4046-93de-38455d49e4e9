import cv2
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from skimage.segmentation import slic, random_walker
from skimage.filters import gaussian, threshold_otsu
from skimage.measure import label, regionprops_table
import pandas as pd
from skimage.measure import regionprops
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler
from scipy.ndimage import binary_dilation
import matplotlib.pyplot as plt
import math
import numpy as np
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
from skimage.filters import gabor_kernel
from scipy import ndimage as ndi
import os
from sklearn.cluster import MeanShift, DBSCAN
# ===================== Step 0: 读取图像 =====================
image_path = "C:/Users/<USER>/Desktop/ICE/sea_ice/sea_ice_593.jpg"
# 检查图像路径是否存在
if not os.path.exists(image_path):
    raise FileNotFoundError(f"图像路径 '{image_path}' 不存在，请检查路径是否正确！")
# 读取输入图像
image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
# 检查图像是否成功读取
if image is None:
    raise ValueError(f"无法读取图像 '{image_path}'，请检查文件是否损坏或格式是否支持！")
######=====================A.图像预处理 =====================####### 
# ===================== Step 1: 转为灰度图像 =====================
# 转换为灰度图像，简化计算，突出亮度差异，适用于大多数分割算法
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
# ===================== Step 2: 减少反光 =====================
#光照反光抑制与对比度增强：  为了减轻反光干扰，并增强冰水之间的视觉差异
#同态滤波 (Homomorphic Filtering)： 可以将图像的照度分量和反射分量分离，
#减弱照度分量的影响，从而减少光照不均匀和反光现象。
def homomorphic_filter(img, gamma_h=1.5, gamma_l=0.5, d0=10, c=1):
    """
    同态滤波器
    Args:
        img: 灰度图像
        gamma_h: 高频增益
        gamma_l: 低频增益
        d0: 截止频率
        c: 锐化系数
    Returns:
        滤波后的图像
    """
    rows, cols = img.shape
    img_log = np.log1p(np.array(img, dtype="float") / 255) # 转换为对数域
    # 构建滤波器
    H = np.zeros((rows, cols), np.float32)
    for i in range(rows):
        for j in range(cols):
            H[i, j] = (gamma_h - gamma_l) * (1 - np.exp(-c * ((i**2 + j**2) / d0**2))) + gamma_l
    # 频域变换
    img_fft = np.fft.fft2(img_log)
    img_fft_shifted = np.fft.fftshift(img_fft)
    # 滤波
    img_fft_filtered = img_fft_shifted * H
    img_fft_inverse_shifted = np.fft.ifftshift(img_fft_filtered)
    img_filtered = np.fft.ifft2(img_fft_inverse_shifted)
    # 指数变换，恢复到图像灰度范围
    img_exp = np.expm1(np.real(img_filtered))
    img_output = np.array(np.clip(img_exp * 255 + 0.5, 0, 255), dtype=np.uint8)
    return img_output
homo_image = homomorphic_filter(gray_image) # 对增强对比度后的图像进行同态滤波
# ===================== Step 3: 图像平滑处理（滤波降噪） =====================
# 高斯滤波平滑图像，减少高频噪声，保持边缘信息
smoothed_image = gaussian(homo_image, sigma=0.5)
# 中值滤波进一步去除椒盐噪声
median_image = cv2.medianBlur((smoothed_image * 255).astype(np.uint8), 5)
# ===================== Step 4: 增强对比度 =====================
# 使用直方图均衡化增强对比度，突出图像细节
# enhanced_image = cv2.equalizeHist(homo_image)
# 自适应直方图均衡化（CLAHE），避免过度增强，适用于局部对比度提升
clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
enhanced_image = clahe.apply(homo_image)
# ===================== Step 4: 边缘增强与纹理特征提取 =====================
# Canny 边缘检测，提取海冰与水体的边缘特征
edges = cv2.Canny(enhanced_image, threshold1=50, threshold2=150)
# 使用拉普拉斯滤波器进一步增强边缘，突出细节变化
# laplacian_edges = cv2.Laplacian(enhanced_image, cv2.CV_64F)

# 定义 Gabor 滤波器参数
frequencies = [0.1, 0.5, 1.0, ]  # 不同频率
thetas = [0, np.pi/4, np.pi/2, 3*np.pi/4]  # 不同方向

# 定义图像金字塔参数
num_levels = 3  # 金字塔层数

# 构建图像金字塔
pyramid = [enhanced_image]
for i in range(1, num_levels):
    downsampled = cv2.pyrDown(pyramid[i - 1])
    pyramid.append(downsampled)

# 存储 Gabor 滤波结果
gabor_responses = []

# 预计算 Gabor 滤波器内核
kernels = {}
for freq in frequencies:
    for theta in thetas:
        kernel = cv2.getGaborKernel((21, 21), sigma=5, theta=theta, lambd=1/freq, gamma=1, psi=0, ktype=cv2.CV_32F)
        kernel /= np.sqrt((kernel * kernel).sum())
        kernels[(freq, theta)] = kernel

# 对每个金字塔层级的图像进行 Gabor 滤波
plt.figure(figsize=(20, 12),dpi=500)
plot_index = 1
for level, img in enumerate(pyramid):
    for freq in frequencies:
        for theta in thetas:
            # 获取预计算的 Gabor 滤波器内核
            kernel = kernels[(freq, theta)]
            # 应用 Gabor 滤波器
            filtered = cv2.filter2D(img, cv2.CV_8UC3, kernel)
            # 计算幅度响应
            magnitude = np.abs(filtered)
            # 存储结果
            gabor_responses.append(magnitude)
            # 显示结果
            plt.subplot(num_levels, len(frequencies) * len(thetas), plot_index)
            plt.imshow(magnitude, cmap='gray')
           # plt.title(f"Level={level}, Freq={freq:.1f}, Theta={theta:.2f}")
            plt.axis('off')
            plot_index += 1
plt.tight_layout()
plt.show()

# 将所有 Gabor 响应拼接成一个特征矩阵
features = []
original_image_shape = pyramid[0].shape[:2] # 获取原始图像尺寸，假设 pyramid[0] 是原始图像或增强后的图像
for level_response in gabor_responses:
    # 确保所有响应图像尺寸一致，或者调整到原始图像尺寸 (例如使用 resize)
    resized_response = cv2.resize(level_response, original_image_shape[::-1], interpolation=cv2.INTER_LINEAR)
    if len(features) == 0:
        features = resized_response.reshape((-1, 1)) # 初始化特征，假设第一个响应作为初始特征
    else:
        features = np.concatenate((features, resized_response.reshape((-1, 1))), axis=1) # 将新的响应添加到特征向量
        
from sklearn.decomposition import PCA
pca = PCA(n_components=2)
features= pca.fit_transform(features)

feature_image= np.concatenate((enhanced_image.reshape(-1,1), features), axis=1) # 拼接

# ===================== Step 5: 可视化结果 =====================
# 显示原始图像和处理后的图像
plt.figure(figsize=(12, 9),dpi=500)
plt.subplot(3, 3, 1)
plt.title("Original Image")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.axis('off')
plt.subplot(3, 3, 2)
plt.title("Grayscale Image")
plt.imshow(gray_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 3)
plt.title("homo_image")
plt.imshow(homo_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 4)
plt.title("Smoothed Image")
plt.imshow(median_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 5)
plt.title("median_image")
plt.imshow(median_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 6)
plt.title("Contrast Enhanced")
plt.imshow(enhanced_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 7)
plt.title("Canny Edges")
plt.imshow(edges, cmap='gray')
plt.axis('off')
plt.tight_layout()
plt.show()
plt.subplot(3, 3, 7)
plt.title("Canny Edges")
plt.imshow(magnitude, cmap='gray')
plt.axis('off')
plt.tight_layout()
plt.show()

# ===================== 3D 可视化灰度图像 =====================
h, w = gray_image.shape
x = np.linspace(0, w, w)
y = np.linspace(0, h, h)
x, y = np.meshgrid(x, y)  # 生成网格坐标
z = gray_image  # 将灰度值作为 z 高度
z = enhanced_image  # 将灰度值作为 z 高度
# 创建 3D 绘图
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')
# 绘制曲面
surface = ax.plot_surface(x, y, z, cmap="gray", edgecolor='none')
# 添加颜色条
fig.colorbar(surface, ax=ax, shrink=0.5, aspect=10)
# 设置标题和轴标签
ax.set_title("3D Visualization of Grayscale Image")
ax.set_xlabel("X-axis")
ax.set_ylabel("Y-axis")
ax.set_zlabel("Pixel Intensity")
plt.show()


# --------------------- 图像分割方法 ---------------------
image_seg = enhanced_image
#image_data=enhanced_image.reshape((-1, 1))

# 方法 1: 应用 Otsu 阈值分割
_, otsu_thresh = cv2.threshold((image_seg * 255).astype(np.uint8), 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)


# 方法 3: 阈值分割（固定阈值）
_, fixed_thresh = cv2.threshold((image_seg * 255).astype(np.uint8), 127, 255, cv2.THRESH_BINARY)

# 方法 4：自适应阈值
adaptive_thresh = cv2.adaptiveThreshold((image_seg * 255).astype(np.uint8), 255,
                                        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                        cv2.THRESH_BINARY, 11, 2)

# 方法 4: 随机游走方法
markers = np.zeros_like(image_seg, dtype=np.int32)
markers[image_seg < 0.4] = 1  # 调整阈值以适应归一化图像
markers[image_seg > 0.6] = 2  # 调整阈值以适应归一化图像
labels_rw = random_walker(image_seg, markers, beta=10, mode='bf')

# 方法5：分水岭算法
_, thresh = cv2.threshold(image_seg, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
dist_transform = cv2.distanceTransform(thresh, cv2.DIST_L2, 5)
_, sure_fg = cv2.threshold(dist_transform, 0.5*dist_transform.max(), 255, 0)
sure_fg = np.uint8(sure_fg)
unknown = cv2.subtract(thresh, sure_fg)
_, markers = cv2.connectedComponents(sure_fg)
markers += 1
markers[unknown==255] = 0
markers = cv2.watershed(cv2.merge([img]*3), markers)
watershed_seg = np.where(markers == 1, 0, 255).astype(np.uint8)

# 方法 6：边缘检测+填充分割
edges = cv2.Canny(image_seg, 50, 150)
contours, _ = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
mask = np.zeros_like(image_seg)
edges_seg = cv2.drawContours(mask, contours, -1, 255, thickness=cv2.FILLED)

# 方法 7: 应用 K-Means 聚类分割
pixel_values = np.float32(feature_image.reshape((-1, 1)))
criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 100, 0.2)
k = 2
_, labels, centers = cv2.kmeans(pixel_values, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
segmented_image_kmeans = labels.reshape(enhanced_image.shape)

# 方法8： 深度学习分割 (Deep Learning Segmentation)
# 提示：深度学习分割通常需要预训练模型和复杂的环境配置。
# 这里仅作为概念展示，实际应用需要使用如 TensorFlow, PyTorch 等框架加载模型并进行推理。
# 例如使用预训练的 Mask R-CNN, U-Net 等模型进行海冰/水分割。
# deep_learning_seg = ...  #  需要加载模型并进行预测的代码，此处省略具体实现

# 创建一个包含所有子图的布局
fig, axes = plt.subplots(3, 3, figsize=(18, 18), dpi=500)
# 图像标题与对应的处理方法
images = [
    (cv2.cvtColor(image, cv2.COLOR_BGR2RGB), "Original Image"),
    (otsu_thresh, "Otsu Thresholding"),
    (fixed_thresh, "Fixed Threshold"),
    (adaptive_thresh, "Adaptive Threshold"),
    (labels_rw, "Random Walker"),
    (watershed_seg, "Watershed"),
    (segmented_image_kmeans, "K-Means")
]

# 遍历子图并展示各个图像
for i, (img, title) in enumerate(images):
    ax = axes[i // 3, i % 3]  # 计算位置
    ax.imshow(img, cmap='gray' if i != 6 else 'gray')  # K-means 使用 jet 色图
    ax.set_title(title)
    ax.axis('off')

# 调整布局，使各个图像之间的间距合适
plt.tight_layout()
plt.show()

# ===================== 可视化随机游走分割结果 =====================
plt.figure(figsize=(15, 10))
# 原始灰度图像
plt.subplot(1, 3, 1)
plt.imshow(gray_image, cmap='gray')
plt.title("Original Grayscale Image")
plt.axis("off")
# 标记结果
plt.subplot(1, 3, 2)
plt.imshow(markers, cmap='jet')
plt.title("Markers")
plt.axis("off")
# 随机游走分割结果
plt.subplot(1, 3, 3)
plt.imshow(labels_rw, cmap='gray')
plt.title("Segmented Image (Random Walker)")
plt.axis("off")
plt.tight_layout()
plt.show()
# =====================高级统计分析功能=====================
#0.选择图片分割后的结果
ice_image=fixed_thresh

from scipy import ndimage
import matplotlib.patches as patches
#使用分割得到的标签图，每个连通区域可以视为一块独立的海冰。
# 识别连通区域,进行单一的颜色识别
labeled_ice, num_features = ndimage.label(ice_image)
# 可视化每块海冰(用颜色区分)
plt.figure(figsize=(10, 8))
plt.imshow(labeled_ice, cmap='nipy_spectral')
plt.title(f"Identified Sea Ice Blocks: {num_features}")
plt.axis("off")
plt.show()

# 可视化每块海冰(用方框区分)
fig, ax = plt.subplots(figsize=(10, 8))
ax.imshow(labeled_ice, cmap='nipy_spectral')
ax.set_title(f"Identified Sea Ice Blocks: {num_features}")
ax.axis("off")
# 识别每个连通区域的边界框
objects = ndimage.find_objects(labeled_ice)
# 绘制红色方框标记每块海冰
for obj_slice in objects:
    if obj_slice is not None:
        y_start, y_stop = obj_slice[0].start, obj_slice[0].stop
        x_start, x_stop = obj_slice[1].start, obj_slice[1].stop
        width = x_stop - x_start
        height = y_stop - y_start
        # 添加红色方框
        rect = patches.Rectangle((x_start, y_start), width, height, 
                                 linewidth=2, edgecolor='red', facecolor='none')
        ax.add_patch(rect)
plt.show()

#1.1 海冰的膨胀安全距离
#定义膨胀操作以模拟 "充气" 海冰，保证安全距离
safety_margin = 1  # 安全边距
inflated_ice = binary_dilation(fixed_thresh, iterations=safety_margin)# 膨胀
#创建加权图像，初步为空白区域（0值）和冰厚区域（根据 gray_image 加权）
weighted_image = np.copy(gray_image)
#设置膨胀区域为障碍物，其他空白区域根据冰厚值加权
weighted_image[inflated_ice == 1] = 1  # 赋予1
weighted_image[inflated_ice == 0] = 0  # 赋予0
# 可视化
plt.figure(figsize=(8, 6))
plt.imshow(weighted_image, cmap='jet', origin='upper')
plt.axis('off')
plt.show()

#1.2 海冰的膨胀后的栅格定义
# 连通区域标记
labeled_inflated, num_inflated = ndimage.label(weighted_image)

# 方案1：矩形包围框及掩膜处理
rect_mask = np.zeros_like(ice_image)
fig, ax = plt.subplots(figsize=(10, 8))
ax.imshow(ice_image, cmap='gray')
ax.set_title(f"Inflated Sea Ice with Rectangular Margins: {num_inflated}")
ax.axis("off")

objects_inflated = ndimage.find_objects(labeled_inflated)
for obj_slice in objects_inflated:
    if obj_slice:
        y_start, y_stop = obj_slice[0].start, obj_slice[0].stop
        x_start, x_stop = obj_slice[1].start, obj_slice[1].stop
        rect = patches.Rectangle((x_start, y_start), 
                                x_stop - x_start, 
                                y_stop - y_start,
                                linewidth=1, edgecolor='cyan', 
                                facecolor='none', linestyle='--')
        ax.add_patch(rect)
        # 赋值为1
        rect_mask[y_start:y_stop, x_start:x_stop] = 1
plt.show()
# 掩膜可视化
plt.figure(figsize=(8, 6))
plt.imshow(rect_mask, cmap='gray', origin='upper')
plt.axis('off')
plt.show()

# 方案2：圆形包围框及掩膜处理
circle_mask = np.zeros_like(ice_image)
fig, ax = plt.subplots(figsize=(10, 8))
ax.imshow(ice_image, cmap='gray')
ax.set_title(f"Inflated Sea Ice with Circular Margins: {num_inflated}")
ax.axis("off")
for label in range(1, num_inflated + 1):
    mask = labeled_inflated == label
    coords = np.argwhere(mask)
    y_center, x_center = coords.mean(axis=0)
    distances = np.sqrt((coords[:,0] - y_center)**2 + (coords[:,1] - x_center)**2)
    radius = np.max(distances)
    circle = patches.Circle((x_center, y_center), radius,
                            linewidth=1, edgecolor='magenta',
                            facecolor='none', linestyle='--')
    ax.add_patch(circle)
    # 赋值为1
    for y in range(ice_image.shape[0]):
        for x in range(ice_image.shape[1]):
            if (x - x_center)**2 + (y - y_center)**2 <= radius**2:
                circle_mask[y, x] = 1
plt.show()

# 掩膜可视化
plt.figure(figsize=(8, 6))
plt.imshow(circle_mask, cmap='gray', origin='upper')
plt.axis('off')
plt.show()


#2.1 海冰厚度的估计（基于灰度特征）
#厚度估算改进： 如果有真实厚度数据，可以通过回归模型建立更精准的灰度-厚度映射。
#假设灰度值与厚度呈一定的物理关系，可以通过以下方式估算：
# 假设 gray_image 是你的冰厚图像
scaler = MinMaxScaler()
# 将 gray_image 扁平化为一维数组，MinMaxScaler 要求二维数据
gray_image_flattened = gray_image.flatten().reshape(-1, 1)
# 对数据进行归一化
normalized_gray_image_flattened = scaler.fit_transform(gray_image_flattened)
# 将归一化后的图像重新调整为原来的形状
normalized_image = normalized_gray_image_flattened.reshape(gray_image.shape)
thickness_image=normalized_image#冰厚结果
plt.figure(figsize=(8, 6))
plt.imshow(thickness_image, cmap='jet', origin='upper')
plt.axis('off')
plt.show()

# 计算每块海冰的平均灰度值（作为厚度的近似）
thickness_list = ndimage.mean(thickness_image, labels=labeled_ice, index=range(1, num_features + 1))
# 输出厚度信息
for i, thickness in enumerate(thickness_list, 1):
    print(f"Sea Ice Block {i}: Estimated Thickness = {thickness:.2f}")
    
#2.2 图片的冰区覆盖率
ice_area = np.sum(ice_image > 0)  # 冰层区域的面积
total_area = ice_image.size  # 图像的总面积
coverage = (ice_area / total_area) * 100  # 覆盖率（百分比）
print(f"Ice Coverage: {coverage:.2f}%")


#2.3 冰层覆盖率图片的冰区密集度
# 假设图像被划分为网格，例如每个网格为10x10像素
# 网格参数
grid_size = 20
grid_rows = ice_image.shape[0] // grid_size
grid_cols = ice_image.shape[1] // grid_size
# 计算密度图
density_map = np.zeros((grid_rows, grid_cols))
for row in range(grid_rows):
    for col in range(grid_cols):
        grid_area = ice_image[row * grid_size:(row + 1) * grid_size, col * grid_size:(col + 1) * grid_size]
        ice_in_grid = np.sum(grid_area > 0)
        total_grid_area = grid_area.size
        density_map[row, col] = (ice_in_grid / total_grid_area) * 100  # 覆盖率百分比
# 可视化冰层分布密度
plt.figure(figsize=(8, 6))
plt.imshow(density_map, cmap='jet', interpolation='nearest')
plt.colorbar(label='Ice Coverage (%)')
plt.title('Ice Distribution Density')
plt.axis('off')
plt.show()
# 修正插值部分
# 计算正确的缩放因子
zoom_factor_row = ice_image.shape[0] / density_map.shape[0]
zoom_factor_col = ice_image.shape[1] / density_map.shape[1]
# 使用双线性插值（order=1）
density_map_resized = zoom(density_map, (zoom_factor_row, zoom_factor_col), order=1)
# 检查新数组的形状
print("新网格大小：", density_map_resized.shape)
# 可视化原始与插值后的网格
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.imshow(density_map, cmap='viridis', interpolation='none')
plt.title("Mesh1")
plt.colorbar()
plt.subplot(1, 2, 2)
plt.imshow(density_map_resized, cmap='viridis', interpolation='none')
plt.title("Mesh2")
plt.colorbar()
plt.show()

# 3. 海冰大小的统计（面积和形状特征）
# 可以通过计算每块海冰的像素数量来估算面积：
# 计算每块海冰的面积（像素数）
area_list = ndimage.sum(np.ones_like(gray_image), labels=labeled_ice, index=range(1, num_features + 1))

# 输出面积信息
for i, area in enumerate(area_list, 1):
    print(f"Sea Ice Block {i}: Area = {area} pixels")
# 计算形状特征
props = regionprops(labeled_ice, intensity_image=gray_image)
for i, prop in enumerate(props, 1):
    print(f"Sea Ice Block {i}: Area = {prop.area}, Perimeter = {prop.perimeter}, Eccentricity = {prop.eccentricity}")

# 厚度分布
plt.figure(figsize=(8, 4))
sns.histplot(thickness_list, kde=True)
plt.title("Distribution of Sea Ice Thickness")
plt.xlabel("Estimated Thickness")
plt.ylabel("Frequency")
plt.show()

# 面积分布
plt.figure(figsize=(8, 4))
sns.histplot(area_list, kde=True, color="orange")
plt.title("Distribution of Sea Ice Area")
plt.xlabel("Area (pixels)")
plt.ylabel("Frequency")
plt.show()

# 4. 单一海冰的特征
#关联labeled_ice和厚度，形状特征，可以选择绘制某个海冰到一个图中，并展示相关信息进行绘制
# 假设你已经有了 labeled_ice 和 thickness_list、props 等信息
# 选择你想要显示的海冰块，例如选择海冰块1
target_block = 20
# 创建一个掩膜，仅保留目标海冰块
target_mask = labeled_ice == target_block
# 可视化该目标海冰块（白色）
plt.figure(figsize=(10, 8))
plt.imshow(target_mask, cmap='gray')
plt.title(f"Sea Ice Block {target_block}: Thickness = {thickness_list[target_block - 1]:.2f}")
plt.axis("off")
plt.show()
# 输出该目标海冰块的相关形状特征
target_prop = props[target_block - 1]
print(f"Sea Ice Block {target_block}:")
print(f"Estimated Thickness = {thickness_list[target_block - 1]:.2f}")
print(f"Area = {target_prop.area} pixels")
print(f"Perimeter = {target_prop.perimeter:.2f}")
print(f"Eccentricity = {target_prop.eccentricity:.2f}")

# ===================== 路径规划功能 =====================
import gym
from gym import spaces
import numpy as np
import heapq
import math
import sys
import matplotlib.pyplot as plt
from typing import Any, List, Tuple, Dict, Optional
# =============================================================================
# 分类 A: 基于图的搜索算法 (Graph-Based Search Algorithms)
# =============================================================================

# 1. Dijkstra 算法（DijkstraClassic）
# 描述：
#   - 基于广度优先搜索，逐步扩展节点，计算起点到所有节点的最短路径。
# 优点：
#   - 全局最优：在边权非负的图中保证找到最优路径。
#   - 完整性：只要存在一条可达路径，算法必能找到。
# 缺点：
#   - 计算量大：在大规模图或高分辨率网格中，搜索节点数激增，计算时间较长。
#   - 无启发信息：对目标信息没有利用，搜索效率相对较低。
from DijkstraClassic import DijkstraClassic

# 2. A* 算法（AstarClassic）
# 描述：
#   - 在 Dijkstra 的基础上引入启发式函数 h(n)，使搜索过程朝向目标方向引导。
# 优点：
#   - 高效性：合理的启发函数可大幅缩小搜索空间，提高搜索速度。
#   - 最优性和完整性：当启发函数满足一致性条件时，能保证全局最优解。
# 缺点：
#   - 启发函数依赖：算法性能受启发函数设计影响较大，不当设计可能影响效果。
#   - 内存消耗大：需保存开放列表和闭合列表，处理高分辨率问题时内存开销较高。
from AstarClassic import AstarClassic

# 3. 最佳优先搜索（Best-First Search）（Best_FirstClassic）
# 描述：
#   - 利用启发函数评估节点的重要性，优先扩展最有希望到达目标的节点。
# 优点：
#   - 搜索速度快：若启发函数设计合理，能迅速指引搜索方向。
# 缺点：
#   - 最优性不保证：可能因启发函数的局部最优性而错失全局最优路径。
#   - 强依赖启发信息：启发函数设计不当时，性能大打折扣。
from Best_FirstClassic import Best_FirstClassic

# 4. 宽度优先搜索 (BFS)
# 描述：
#   - 逐层展开搜索，不考虑移动代价，从而保证搜索到的路径步数最少。
# 优点：
#   - 保证最少步数：能找到步数最少的路径（适用于所有移动代价相同的情况）。
#   - 算法简单且完整：实现简单，只要路径存在必定能找到。
# 缺点：
#   - 忽略移动代价：不适用于代价不统一的场景，不能保证整体代价最小。
from BFS import BFS

# 5. 迭代加深 A*（IDA*）（IDAStarClassic）
# 描述：
#   - 结合迭代加深和 A* 的思想，通过不断提高 f = g + h 的阈值来搜索路径。
# 优点：
#   - 内存效率高：相比标准 A*，IDA* 不需存储庞大的开放列表，适合内存受限场景。
# 缺点：
#   - 重复计算：可能多次重复搜索同一节点，导致计算量增加。
#   - 对阈值敏感：搜索效率和成功率依赖于阈值设定策略。
from IDA import IDAStarClassic

# 6. Theta* 算法（ThetastarClassic）
# 描述：
#   - 类似于 A* 算法，但通过视线检测实现路径平滑，适合网格障碍物环境。
# 优点：
#   - 平滑路径：能够生成较为直接和连续的路径。
#   - 保留 A* 的优势：在满足一定条件下依然能保证最优性。
# 缺点：
#   - 算法复杂：实现上比标准 A* 更复杂，尤其在处理复杂障碍时。
#   - 计算开销：在障碍密集区域，视线检测可能导致额外计算负担。
from ThetastarClassic import ThetastarClassic

from LPAstar import LPAstar
# =============================================================================
# 分类 B: 采样（随机）基方法 (Sampling-Based Methods)
# =============================================================================

# 1. 快速探索随机树（RRT）
# 描述：
#   - 从起点开始，通过在连续空间中随机采样并扩展树来快速探索整个搜索空间。
# 优点：
#   - 探索效率高：适合高维空间和复杂约束问题，能快速覆盖大部分自由空间。
#   - 实现简单：算法直观，编码实现较为容易。
# 缺点：
#   - 路径不最优：生成的路径通常较长且转弯多，需要后续平滑处理。
#   - 结果依赖随机性：每次运行结果可能有所不同。
from RRTstar import RRTstar
from RRT import RRT
#RRT 和概率路线图方法 （PRM） 是运动规划中常用的两种算法
# =============================================================================
# 分类 C: 仿生智能算法 (Bio-Inspired Algorithms)
# =============================================================================

# 1. 蚁群算法（Ant Colony Optimization，ACO）
# 描述：
#   - 模拟蚂蚁觅食行为，通过信息素的正反馈机制在图上搜索路径。
# 优点：
#   - 鲁棒性强：能在复杂、多变的环境中找到较优路径，具备自适应能力。
#   - 并行处理：算法具备天然并行特性，可通过并行计算加速搜索。
# 缺点：
#   - 计算密集：计算量大，收敛速度较慢。
#   - 易陷局部最优：参数选择不当可能导致收敛到次优解，需要精心调参。
from ACOPathPlanner import ACOPathPlanner
# =============================================================================
# 分类 D: 基于势场的方法 (Potential Field Methods)
# =============================================================================
# 1. 势场规划方法（PotentialFieldPlanner）
# 描述：
#   - 利用人工势场模型，将目标设为吸引源、障碍物设为排斥源，通过势场梯度引导移动。
# 优点：
#   - 直观简单：算法易于理解和实现，实时性好。
#   - 计算效率高：在许多实际应用中能迅速计算出合适路径。
# 缺点：
#   - 局部极小值问题：容易陷入局部极小值，导致无法达到目标。
#   - 参数敏感：需要精细调节吸引和排斥参数以避免性能下降。
from PotentialFieldPlanner import PotentialFieldPlanner
# =============================================================================
def interpolate_path(path):
    """
    对路径进行插值，使其包含所有点与点之间的连线路径上的点
    使用 Bresenham 线算法来获取每条边上的所有整数坐标点
    """
    interpolated_path = []
    for i in range(len(path) - 1):
        rr, cc = line(path[i][0], path[i][1], path[i+1][0], path[i+1][1])  # 获取线段上的所有整数点
        interpolated_path.extend(zip(rr, cc))  # 添加到路径列表中
    return interpolated_path

# 指标计算函数
def compute_path_length(path):
    """
    计算路径长度（即路过的像素点数）
    """
    interpolated_path = interpolate_path(path)
    return len(interpolated_path) if interpolated_path else 0

def compute_turn_count(path):
    turn_count = 0
    if len(path) < 3:
        return 0
    prev_move = (path[1][0] - path[0][0], path[1][1] - path[0][1])
    for i in range(1, len(path)-1):
        curr_move = (path[i+1][0] - path[i][0], path[i+1][1] - path[i][1])
        if curr_move != prev_move:
            turn_count += 1
        prev_move = curr_move
    return turn_count

def compute_smoothness(path):
    """
    计算路径的平滑度，基于路径中相邻向量之间的夹角。
    平滑度 = 所有转角度数的总和。
    参数：
        path (list of tuple): 路径点的坐标列表，如 [(x1, y1), (x2, y2), ...]

    返回：
        float: 平滑度，角度总和（单位：度）
    """
    smoothness = 0
    if len(path) < 3:
        return 0  # 不足以形成转弯
    def vector(p1, p2):
        return (p2[0] - p1[0], p2[1] - p1[1])
    def angle_between(v1, v2):
        # 计算两个向量之间的夹角（单位：度）
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]
        magnitude_v1 = math.hypot(v1[0], v1[1])
        magnitude_v2 = math.hypot(v2[0], v2[1])
        if magnitude_v1 == 0 or magnitude_v2 == 0:
            return 0  # 避免除以零     
        # 计算余弦值，防止浮点数误差导致超出[-1, 1]范围
        cos_theta = max(min(dot_product / (magnitude_v1 * magnitude_v2), 1), -1)
        angle_rad = math.acos(cos_theta)
        return math.degrees(angle_rad)
    for i in range(1, len(path) - 1):
        v1 = vector(path[i - 1], path[i])
        v2 = vector(path[i], path[i + 1])
        smoothness += angle_between(v1, v2)
    return smoothness
from skimage.draw import line
def compute_feasibility(path, thickness_image):
    """
    计算路径的可行性，考虑路径点和路径连线上的所有点
    """
    interpolated_path = interpolate_path(path)  # 生成完整路径点
    return sum(thickness_image[pos[0], pos[1]] for pos in interpolated_path) if interpolated_path else 0

def compute_risk(path, density_map_resized):
    """
    计算路径的风险，考虑路径点和路径连线上的所有点
    """
    interpolated_path = interpolate_path(path)  # 生成完整路径点
    return sum(density_map_resized[pos[0], pos[1]] for pos in interpolated_path) if interpolated_path else 0
# =============================================================================
# 绘制路径函数
def plot_path(grid, path, start, end, title="A* Pathfinding"):
    """
    绘制路径搜索结果

    参数：
      grid: 2D 数组或 numpy.array，表示网格地图。
      path: 路径列表，每个元素为 (x, y) 坐标。如果路径为空则不绘制路径。
      start: 起点坐标 (x, y)。
      end: 终点坐标 (x, y)。
      title: 图形标题（可选）。
    """
    plt.figure(figsize=(5, 5))
    # 显示网格图像，使用 origin='upper' 确保原点在左上角，与数组下标一致
    plt.imshow(grid, cmap='gray', origin='upper')
    # 如果有路径数据，则绘制路径（红色线条）
    if path:
        # 提取路径的 x 和 y 坐标
        path_x = [p[0] for p in path]
        path_y = [p[1] for p in path]
        plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
    # 绘制起点和终点
    plt.scatter(start[0], start[1], color='green', s=100, marker='o', label='Start')
    plt.scatter(end[0], end[1], color='blue', s=100, marker='x', label='End')
    plt.title(title)
    plt.legend()
    plt.grid(True)
    plt.show()
    
 # 在调用算法前添加环境检查
def validate_environment(ice_image, start, goal):
    # 检查起点终点是否可达
    if ice_image[start] != 0 or ice_image[goal] != 0:
        raise ValueError("起点或终点位于障碍物上")
    
    # 简单连通性检查
    tmp_env = ice_image.copy()
    queue = [start]
    tmp_env[start] = 2  # 标记已访问
    while queue:
        x, y = queue.pop(0)
        for dx, dy in [(-1,0),(1,0),(0,-1),(0,1)]:
            nx, ny = x+dx, y+dy
            if 0<=nx<tmp_env.shape[0] and 0<=ny<tmp_env.shape[1]:
                if tmp_env[nx, ny] == 0:
                    if (nx, ny) == goal:
                        return True
                    tmp_env[nx, ny] = 2
                    queue.append((nx, ny))
    return False 
# =============================================================================
# 参数设置：各项指标的权重（新目标函数版本）
weights = {
    'length': 5.0,    # 每步移动消耗
    'turn': 1.0,      # 转弯惩罚
    'smooth': 1.0,    # 平滑性惩罚（转弯附加惩罚）
    'feasible': 1.0,  # 冰厚安全性惩罚（冰越厚惩罚越高）
    'risk': 1.0       # 海冰风险惩罚（风险越高惩罚越高）
}
# 测试主函数
path_image=ice_image#原始
path_image=weighted_image#膨胀
path_image=rect_mask#方框掩膜
#path_image=circle_mask#圆形掩膜

# 设置起点和终点（这里起点设为右下角，终点设为左上角）
start = (path_image.shape[0] - 1, path_image.shape[1] - 1)
end = (0, 0)
if not validate_environment(path_image, start, end):
    print("错误：起点和终点之间没有连通路径！")
    sys.exit(1)

print("【Dijkstra 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 1
dijkstra_classic_path, runs = DijkstraClassic(path_image, start, end, diagonal_movement=diagonal)
if dijkstra_classic_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(dijkstra_classic_path))
    plot_path(gray_image, dijkstra_classic_path, start, end, title="Dijkstra Classic")
else:
    print("未找到路径！")
    
print("【A* 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 1
astar_classic_path, runs = AstarClassic(path_image, start, end, diagonal_movement=diagonal)
if astar_classic_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(astar_classic_path))
    plot_path(gray_image, astar_classic_path, start, end, title="A* Classic")
else:
    print("未找到路径！")

print("【BestFirst 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 3
bestfirst_classic_path, runs = Best_FirstClassic(path_image, start, end, diagonal_movement=diagonal)
if bestfirst_classic_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(bestfirst_classic_path))
    plot_path(gray_image, bestfirst_classic_path, start, end, title="BestFirst Classic")
else:
    print("未找到路径！")
    
print("【BFS 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 3
bfs_classic_path, runs = BFS(path_image, start, end, diagonal_movement=diagonal)
if bfs_classic_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(bfs_classic_path))
    plot_path(gray_image, bfs_classic_path, start, end, title="BestFirst Classic")
else:
    print("未找到路径！")    
    
# print("【IDA算法 - 经典版本】开始路径规划...")
# # 设置对角线移动策略（此处选择总是允许对角线移动）
# diagonal = 3
# ida_classic_path, runs = IDAStarClassic(path_image, start, end, diagonal_movement=diagonal)
# if ida_classic_path:
#     print("找到路径，迭代次数：", runs)
#     print("步数：", compute_path_length(ida_classic_path))
#     plot_path(gray_image, ida_classic_path, start, end, title="BestFirst Classic")
# else:
#     print("未找到路径！")       
    
print("\n【Theta* 算法】开始路径规划...")
theta_star_path = ThetastarClassic(path_image, start, end)
if theta_star_path:
    print("路径找到，步数:", compute_path_length(theta_star_path))
    plot_path(gray_image.T, theta_star_path, start, end, title="Theta* Path")
else:
    print("未找到路径!")

# print("\n【LPA* 算法】开始路径规划...")
# lpa_star = LPAstar(path_image, start, end)
# lpa_star_path = lpa_star.computeShortestPath()
# if lpa_star_path:
#     print("路径找到，步数:", compute_path_length(lpa_star_path))
#     plot_path(gray_image.T, lpa_star_path, start, end, title="Theta* Path")
# else:
#     print("未找到路径!")

# print("\n【RRT 算法】开始路径规划...")
# rrt = RRT(start, end, path_image, 0.5, 0.1, 5000)  # 创建RRT实例
# rrt_path = rrt.search()  # 进行路径规划
# if rrt_path:
#     print("路径找到，步数:", compute_path_length(rrt_path))
#     plot_path(gray_image.T, rrt_path, start, end, title="RRT Path")
# else:
#     print("未找到路径!")

# print("\n【RRTstar 算法】开始路径规划...")
# rrt_star = RRTstar(start, end, path_image,
#                    step_len=1.0,
#                    goal_sample_rate=0.1,
#                    iter_max=5000,
#                    search_radius=10.0)
# rrt_star_path = rrt_star.planning()
# if rrt_star_path:
#     print("路径找到，步数:", compute_path_length(rrt_star_path))
#     plot_path(gray_image.T, rrt_star_path, start, end, title="RRT Path")
# else:
#     print("未找到路径!")

# print("\n【蚁群算法】开始路径规划...")
# aco = ACOPathPlanner(path_image, start, end)
# aco_path = aco.search()
# if aco_path:
#     print("路径找到，步数:", compute_path_length(aco_path))
#     plot_path(gray_image, aco_path, start, end, title="ACO Path")
# else:
#     print("未找到路径!")
    
# print("\n【势场法】开始路径规划...")
# pf = PotentialFieldPlanner(path_image, start, end)
# pf_path = pf.search()
# if pf_path:
#     print("路径找到，步数:", compute_path_length(pf_path))
#     plot_path(gray_image, pf_path, start, end, title="Potential Field Path")
# else:
#     print("未找到路径!")
    
# ----------------------------
# 对比各算法的路径指标
methods = {
    "A* Classic": astar_classic_path,
    "Dijkstra Classic": dijkstra_classic_path,
    #"RRT": rrt_path,
    "Theta*": theta_star_path,
    #"ACO": aco_path,
    #"Potential Field": pf_path,
    "BFT": bestfirst_classic_path,
    "BFS": bfs_classic_path,
    "Theta*":theta_star_path
}

path_metrics = {}
for method, path in methods.items():
    if path:
        path_metrics[method] = {
            "路径长度（步数）": compute_path_length(path),
            "转弯次数": compute_turn_count(path),
            "平滑性（累计转弯角度）": compute_smoothness(path),
            "可行性代价（冰厚）": compute_feasibility(path, normalized_image),
            "风险指数（海冰风险）": compute_risk(path, density_map_resized)
        }
# 打印统计结果
for method, metrics in path_metrics.items():
    print(f"\n【{method}】路径指标：")
    for key, value in metrics.items():
        print(f"  {key}: {value:.2f}" if isinstance(value, float) else f"  {key}: {value}")
        
# 获取所有方法名称
methods_list = list(path_metrics.keys())

# 获取所有指标名称
metrics_names = list(next(iter(path_metrics.values())).keys())

# 构建 NumPy 矩阵（行：方法，列：指标）
metrics_matrix = np.array([[path_metrics[method][metric] for metric in metrics_names] for method in methods_list])
metrics_matrixs=[]
metrics_matrixs.append(metrics_matrix)


# from AstarC import AstarC
# print("\n【AstarC 算法】开始路径规划...")
# A_starC_path = AstarC(path_image, start, end)
# if A_starC_path:
#     print("路径找到，步数:", compute_path_length(A_starC_path))
#     plot_path(gray_image, A_starC_path, start, end, title="Theta* Path")
# else:
#     print("未找到路径!")


