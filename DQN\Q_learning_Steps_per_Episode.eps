%!PS-Adobe-3.0 EPSF-3.0
%%Title: Q_learning_Steps_per_Episode.eps
%%Creator: Mat<PERSON>lotlib v3.7.5, https://matplotlib.org/
%%CreationDate: Sat Feb 24 16:04:51 2024
%%Orientation: portrait
%%BoundingBox: 75 223 537 569
%%HiResBoundingBox: 75.600000 223.200000 536.400000 568.800000
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Matplotlib.
10 dict begin
/FontName /DejaVuSans def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-2090 -948 3673 2524] def
/FontType 3 def
/Encoding [/space /zero /one /two /three /four /five /six /eight /E /T /a /d /e /i /k /n /o /p /s /t] def
/CharStrings 22 dict dup begin
/.notdef 0 def
/space{651 0 0 0 0 0 sc
ce} _d
/zero{1303 0 135 -29 1167 1520 sc
651 1360 m
547 1360 469 1309 416 1206 c
364 1104 338 950 338 745 c
338 540 364 387 416 284 c
469 182 547 131 651 131 c
756 131 834 182 886 284 c
939 387 965 540 965 745 c
965 950 939 1104 886 1206 c
834 1309 756 1360 651 1360 c

651 1520 m
818 1520 946 1454 1034 1321 c
1123 1189 1167 997 1167 745 c
1167 494 1123 302 1034 169 c
946 37 818 -29 651 -29 c
484 -29 356 37 267 169 c
179 302 135 494 135 745 c
135 997 179 1189 267 1321 c
356 1454 484 1520 651 1520 c

ce} _d
/one{1303 0 225 0 1114 1493 sc
254 170 m
584 170 l
584 1309 l
225 1237 l
225 1421 l
582 1493 l
784 1493 l
784 170 l
1114 170 l
1114 0 l
254 0 l
254 170 l

ce} _d
/two{1303 0 150 0 1098 1520 sc
393 170 m
1098 170 l
1098 0 l
150 0 l
150 170 l
227 249 331 356 463 489 c
596 623 679 709 713 748 c
778 821 823 882 848 932 c
874 983 887 1032 887 1081 c
887 1160 859 1225 803 1275 c
748 1325 675 1350 586 1350 c
523 1350 456 1339 385 1317 c
315 1295 240 1262 160 1217 c
160 1421 l
241 1454 317 1478 388 1495 c
459 1512 523 1520 582 1520 c
737 1520 860 1481 952 1404 c
1044 1327 1090 1223 1090 1094 c
1090 1033 1078 974 1055 919 c
1032 864 991 800 930 725 c
913 706 860 650 771 557 c
682 465 556 336 393 170 c

ce} _d
/three{1303 0 156 -29 1139 1520 sc
831 805 m
928 784 1003 741 1057 676 c
1112 611 1139 530 1139 434 c
1139 287 1088 173 987 92 c
886 11 742 -29 555 -29 c
492 -29 428 -23 361 -10 c
295 2 227 20 156 45 c
156 240 l
212 207 273 183 340 166 c
407 149 476 141 549 141 c
676 141 772 166 838 216 c
905 266 938 339 938 434 c
938 522 907 591 845 640 c
784 690 698 715 588 715 c
414 715 l
414 881 l
596 881 l
695 881 771 901 824 940 c
877 980 903 1037 903 1112 c
903 1189 876 1247 821 1288 c
767 1329 689 1350 588 1350 c
533 1350 473 1344 410 1332 c
347 1320 277 1301 201 1276 c
201 1456 l
278 1477 349 1493 416 1504 c
483 1515 547 1520 606 1520 c
759 1520 881 1485 970 1415 c
1059 1346 1104 1252 1104 1133 c
1104 1050 1080 980 1033 923 c
986 866 918 827 831 805 c

ce} _d
/four{1303 0 100 0 1188 1493 sc
774 1317 m
264 520 l
774 520 l
774 1317 l

721 1493 m
975 1493 l
975 520 l
1188 520 l
1188 352 l
975 352 l
975 0 l
774 0 l
774 352 l
100 352 l
100 547 l
721 1493 l

ce} _d
/five{1303 0 158 -29 1124 1493 sc
221 1493 m
1014 1493 l
1014 1323 l
406 1323 l
406 957 l
435 967 465 974 494 979 c
523 984 553 987 582 987 c
749 987 881 941 978 850 c
1075 759 1124 635 1124 479 c
1124 318 1074 193 974 104 c
874 15 733 -29 551 -29 c
488 -29 424 -24 359 -13 c
294 -2 227 14 158 35 c
158 238 l
218 205 280 181 344 165 c
408 149 476 141 547 141 c
662 141 754 171 821 232 c
888 293 922 375 922 479 c
922 583 888 665 821 726 c
754 787 662 817 547 817 c
493 817 439 811 385 799 c
332 787 277 768 221 743 c
221 1493 l

ce} _d
/six{1303 0 143 -29 1174 1520 sc
676 827 m
585 827 513 796 460 734 c
407 672 381 587 381 479 c
381 372 407 287 460 224 c
513 162 585 131 676 131 c
767 131 838 162 891 224 c
944 287 971 372 971 479 c
971 587 944 672 891 734 c
838 796 767 827 676 827 c

1077 1460 m
1077 1276 l
1026 1300 975 1318 923 1331 c
872 1344 821 1350 770 1350 c
637 1350 535 1305 464 1215 c
394 1125 354 989 344 807 c
383 865 433 909 492 940 c
551 971 617 987 688 987 c
838 987 956 941 1043 850 c
1130 759 1174 636 1174 479 c
1174 326 1129 203 1038 110 c
947 17 827 -29 676 -29 c
503 -29 371 37 280 169 c
189 302 143 494 143 745 c
143 981 199 1169 311 1309 c
423 1450 573 1520 762 1520 c
813 1520 864 1515 915 1505 c
967 1495 1021 1480 1077 1460 c

ce} _d
/eight{1303 0 139 -29 1163 1520 sc
651 709 m
555 709 479 683 424 632 c
369 581 342 510 342 420 c
342 330 369 259 424 208 c
479 157 555 131 651 131 c
747 131 823 157 878 208 c
933 260 961 331 961 420 c
961 510 933 581 878 632 c
823 683 748 709 651 709 c

449 795 m
362 816 295 857 246 916 c
198 975 174 1048 174 1133 c
174 1252 216 1347 301 1416 c
386 1485 503 1520 651 1520 c
800 1520 916 1485 1001 1416 c
1086 1347 1128 1252 1128 1133 c
1128 1048 1104 975 1055 916 c
1007 857 940 816 854 795 c
951 772 1027 728 1081 662 c
1136 596 1163 515 1163 420 c
1163 275 1119 164 1030 87 c
942 10 816 -29 651 -29 c
486 -29 360 10 271 87 c
183 164 139 275 139 420 c
139 515 166 596 221 662 c
276 728 352 772 449 795 c

375 1114 m
375 1037 399 976 447 933 c
496 890 564 868 651 868 c
738 868 805 890 854 933 c
903 976 928 1037 928 1114 c
928 1191 903 1252 854 1295 c
805 1338 738 1360 651 1360 c
564 1360 496 1338 447 1295 c
399 1252 375 1191 375 1114 c

ce} _d
/E{1294 0 201 0 1163 1493 sc
201 1493 m
1145 1493 l
1145 1323 l
403 1323 l
403 881 l
1114 881 l
1114 711 l
403 711 l
403 170 l
1163 170 l
1163 0 l
201 0 l
201 1493 l

ce} _d
/T{1251 0 -6 0 1257 1493 sc
-6 1493 m
1257 1493 l
1257 1323 l
727 1323 l
727 0 l
524 0 l
524 1323 l
-6 1323 l
-6 1493 l

ce} _d
/a{1255 0 123 -29 1069 1147 sc
702 563 m
553 563 450 546 393 512 c
336 478 307 420 307 338 c
307 273 328 221 371 182 c
414 144 473 125 547 125 c
649 125 731 161 792 233 c
854 306 885 402 885 522 c
885 563 l
702 563 l

1069 639 m
1069 0 l
885 0 l
885 170 l
843 102 791 52 728 19 c
665 -13 589 -29 498 -29 c
383 -29 292 3 224 67 c
157 132 123 218 123 326 c
123 452 165 547 249 611 c
334 675 460 707 627 707 c
885 707 l
885 725 l
885 810 857 875 801 921 c
746 968 668 991 567 991 c
503 991 441 983 380 968 c
319 953 261 930 205 899 c
205 1069 l
272 1095 338 1114 401 1127 c
464 1140 526 1147 586 1147 c
748 1147 869 1105 949 1021 c
1029 937 1069 810 1069 639 c

ce} _d
/d{1300 0 113 -29 1114 1556 sc
930 950 m
930 1556 l
1114 1556 l
1114 0 l
930 0 l
930 168 l
891 101 842 52 783 19 c
724 -13 654 -29 571 -29 c
436 -29 325 25 240 133 c
155 241 113 383 113 559 c
113 735 155 877 240 985 c
325 1093 436 1147 571 1147 c
654 1147 724 1131 783 1098 c
842 1066 891 1017 930 950 c

303 559 m
303 424 331 317 386 240 c
442 163 519 125 616 125 c
713 125 790 163 846 240 c
902 317 930 424 930 559 c
930 694 902 800 846 877 c
790 954 713 993 616 993 c
519 993 442 954 386 877 c
331 800 303 694 303 559 c

ce} _d
/e{1260 0 113 -29 1151 1147 sc
1151 606 m
1151 516 l
305 516 l
313 389 351 293 419 226 c
488 160 583 127 705 127 c
776 127 844 136 910 153 c
977 170 1043 196 1108 231 c
1108 57 l
1042 29 974 8 905 -7 c
836 -22 765 -29 694 -29 c
515 -29 374 23 269 127 c
165 231 113 372 113 549 c
113 732 162 878 261 985 c
360 1093 494 1147 662 1147 c
813 1147 932 1098 1019 1001 c
1107 904 1151 773 1151 606 c

967 660 m
966 761 937 841 882 901 c
827 961 755 991 664 991 c
561 991 479 962 417 904 c
356 846 320 764 311 659 c
967 660 l

ce} _d
/i{569 0 193 0 377 1556 sc
193 1120 m
377 1120 l
377 0 l
193 0 l
193 1120 l

193 1556 m
377 1556 l
377 1323 l
193 1323 l
193 1556 l

ce} _d
/k{1186 0 186 0 1180 1556 sc
186 1556 m
371 1556 l
371 637 l
920 1120 l
1155 1120 l
561 596 l
1180 0 l
940 0 l
371 547 l
371 0 l
186 0 l
186 1556 l

ce} _d
/n{1298 0 186 0 1124 1147 sc
1124 676 m
1124 0 l
940 0 l
940 670 l
940 776 919 855 878 908 c
837 961 775 987 692 987 c
593 987 514 955 457 892 c
400 829 371 742 371 633 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
415 1013 467 1064 526 1097 c
586 1130 655 1147 733 1147 c
862 1147 959 1107 1025 1027 c
1091 948 1124 831 1124 676 c

ce} _d
/o{1253 0 113 -29 1141 1147 sc
627 991 m
528 991 450 952 393 875 c
336 798 307 693 307 559 c
307 425 335 319 392 242 c
449 165 528 127 627 127 c
725 127 803 166 860 243 c
917 320 946 426 946 559 c
946 692 917 797 860 874 c
803 952 725 991 627 991 c

627 1147 m
787 1147 913 1095 1004 991 c
1095 887 1141 743 1141 559 c
1141 376 1095 232 1004 127 c
913 23 787 -29 627 -29 c
466 -29 340 23 249 127 c
158 232 113 376 113 559 c
113 743 158 887 249 991 c
340 1095 466 1147 627 1147 c

ce} _d
/p{1300 0 186 -426 1188 1147 sc
371 168 m
371 -426 l
186 -426 l
186 1120 l
371 1120 l
371 950 l
410 1017 458 1066 517 1098 c
576 1131 647 1147 729 1147 c
865 1147 975 1093 1060 985 c
1145 877 1188 735 1188 559 c
1188 383 1145 241 1060 133 c
975 25 865 -29 729 -29 c
647 -29 576 -13 517 19 c
458 52 410 101 371 168 c

997 559 m
997 694 969 800 913 877 c
858 954 781 993 684 993 c
587 993 510 954 454 877 c
399 800 371 694 371 559 c
371 424 399 317 454 240 c
510 163 587 125 684 125 c
781 125 858 163 913 240 c
969 317 997 424 997 559 c

ce} _d
/s{1067 0 111 -29 967 1147 sc
907 1087 m
907 913 l
855 940 801 960 745 973 c
689 986 631 993 571 993 c
480 993 411 979 365 951 c
320 923 297 881 297 825 c
297 782 313 749 346 724 c
379 700 444 677 543 655 c
606 641 l
737 613 829 573 884 522 c
939 471 967 400 967 309 c
967 205 926 123 843 62 c
761 1 648 -29 504 -29 c
444 -29 381 -23 316 -11 c
251 0 183 18 111 41 c
111 231 l
179 196 246 169 312 151 c
378 134 443 125 508 125 c
595 125 661 140 708 169 c
755 199 778 241 778 295 c
778 345 761 383 727 410 c
694 437 620 462 506 487 c
442 502 l
328 526 246 563 195 612 c
144 662 119 730 119 817 c
119 922 156 1004 231 1061 c
306 1118 412 1147 549 1147 c
617 1147 681 1142 741 1132 c
801 1122 856 1107 907 1087 c

ce} _d
/t{803 0 55 0 754 1438 sc
375 1438 m
375 1120 l
754 1120 l
754 977 l
375 977 l
375 369 l
375 278 387 219 412 193 c
437 167 488 154 565 154 c
754 154 l
754 0 l
565 0 l
423 0 325 26 271 79 c
217 132 190 229 190 369 c
190 977 l
55 977 l
55 1120 l
190 1120 l
190 1438 l
375 1438 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
75.6 223.2 translate
460.8 345.6 0 0 clipbox
gsave
0 0 m
460.8 0 l
460.8 345.6 l
0 345.6 l
cl
1.000 setgray
fill
grestore
gsave
65.909375 46.971875 m
450 46.971875 l
450 334.8 l
65.909375 334.8 l
cl
1.000 setgray
fill
grestore
0.800 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
83.368 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

79.5477 30.8469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
153.343 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

141.882 30.8469 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
223.317 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

211.856 30.8469 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
293.292 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

281.831 30.8469 translate
0 rotate
0 0 m /three glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
363.266 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

351.806 30.8469 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
433.241 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

421.78 30.8469 translate
0 rotate
0 0 m /five glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

230.462 13.7063 translate
0 rotate
0 0 m /E glyphshow
8.8457 0 m /p glyphshow
17.7324 0 m /i glyphshow
21.6221 0 m /s glyphshow
28.916 0 m /o glyphshow
37.4814 0 m /d glyphshow
46.3682 0 m /e glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 56.6714 o
grestore
/DejaVuSans 12.000 selectfont
gsave

51.2687 52.1089 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 101.786 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 97.223 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 146.9 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 142.337 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 192.014 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 187.451 translate
0 rotate
0 0 m /six glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 237.128 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 232.565 translate
0 rotate
0 0 m /eight glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 282.242 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 277.68 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
22.9043 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
65.9094 327.356 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 322.794 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /two glyphshow
15.2695 0 m /zero glyphshow
22.9043 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

21.4406 149.948 translate
90 rotate
0 0 m /T glyphshow
6.17676 0 m /a glyphshow
14.7559 0 m /k glyphshow
22.3633 0 m /e glyphshow
30.9766 0 m /n glyphshow
39.8496 0 m /space glyphshow
44.2998 0 m /s glyphshow
51.5938 0 m /t glyphshow
57.083 0 m /e glyphshow
65.6963 0 m /p glyphshow
74.583 0 m /s glyphshow
grestore
1.500 setlinewidth
2 setlinecap
0.000 0.000 1.000 setrgbcolor
gsave
384.091 287.828 65.909 46.972 clipbox
83.36804 72.912498 m
84.067786 98.85312 l
84.767532 73.363639 l
85.467278 76.521628 l
86.167024 207.127023 l
86.86677 169.907869 l
87.566516 98.401979 l
88.266262 321.716903 l
88.966008 213.21743 l
89.665755 203.066752 l
90.365501 199.908763 l
91.065247 158.178196 l
91.764993 134.944421 l
92.464739 137.425698 l
93.164485 134.267709 l
93.864231 180.284118 l
94.563977 72.686927 l
95.263723 165.622027 l
95.963469 121.861324 l
96.663215 143.290534 l
97.362961 120.056759 l
98.062708 135.169992 l
98.762454 129.079585 l
99.4622 142.388252 l
100.161946 105.394668 l
100.861692 109.229369 l
101.561438 194.946209 l
102.261184 133.139856 l
102.96093 96.597414 l
103.660676 100.432115 l
104.360422 108.552657 l
105.060168 110.808364 l
105.759914 126.147166 l
106.459661 91.860431 l
107.159407 115.545347 l
107.859153 128.854014 l
108.558899 126.598308 l
109.258645 147.801947 l
109.958391 100.657685 l
110.658137 101.559968 l
111.357883 107.650375 l
112.057629 110.357222 l
112.757375 114.868635 l
113.457121 66.370949 l
114.156867 100.657685 l
114.856614 88.702442 l
115.55636 87.574589 l
116.955852 132.463144 l
117.655598 113.515211 l
118.355344 115.996488 l
119.05509 96.822984 l
119.754836 104.266815 l
120.454582 95.469561 l
121.154328 84.4166 l
121.854074 90.958148 l
122.55382 118.703336 l
123.253567 88.928012 l
123.953313 112.387358 l
124.653059 121.861324 l
125.352805 71.784644 l
126.752297 76.747198 l
128.151789 112.838499 l
128.851535 100.206544 l
129.551281 74.942633 l
130.251027 97.274126 l
130.950773 77.875051 l
131.65052 113.06407 l
132.350266 134.042139 l
133.050012 68.626656 l
133.749758 73.81478 l
134.449504 76.747198 l
135.14925 99.078691 l
136.548742 62.085107 l
137.248488 70.656791 l
137.948234 71.107933 l
138.64798 117.801053 l
139.347726 74.717063 l
140.047473 106.973663 l
140.747219 69.303367 l
141.446965 86.446735 l
142.146711 133.590997 l
142.846457 65.919808 l
143.546203 68.401085 l
144.245949 78.326193 l
144.945695 78.777334 l
145.645441 90.958148 l
146.345187 61.182825 l
147.744679 86.897877 l
148.444425 88.476871 l
149.144172 75.393774 l
149.843918 80.356328 l
150.543664 95.469561 l
151.943156 68.626656 l
152.642902 63.664102 l
153.342648 67.047661 l
154.042394 88.02573 l
154.74214 88.928012 l
155.441886 82.160893 l
156.141632 79.228475 l
156.841378 77.42391 l
157.541125 67.949944 l
158.240871 75.168204 l
158.940617 71.784644 l
159.640363 71.333503 l
160.340109 76.521628 l
161.039855 92.988284 l
161.739601 75.393774 l
162.439347 67.949944 l
163.139093 87.574589 l
163.838839 81.03304 l
164.538585 93.890566 l
165.238331 64.791955 l
165.938078 62.761819 l
166.637824 68.175514 l
167.33757 62.98739 l
168.037316 72.461356 l
168.737062 96.822984 l
169.436808 67.949944 l
170.136554 72.010215 l
170.8363 69.077797 l
171.536046 70.20565 l
172.235792 63.664102 l
172.935538 75.619345 l
173.635284 85.544453 l
174.335031 66.822091 l
175.034777 71.333503 l
175.734523 69.528938 l
176.434269 62.310678 l
177.134015 78.551763 l
177.833761 73.589209 l
178.533507 67.273232 l
179.233253 64.791955 l
179.932999 67.273232 l
181.332491 65.017525 l
182.032237 73.589209 l
182.731984 69.077797 l
183.43173 74.717063 l
184.131476 63.21296 l
184.831222 62.085107 l
185.530968 63.21296 l
186.230714 76.747198 l
186.93046 69.980079 l
187.630206 71.559074 l
188.329952 72.010215 l
189.029698 64.566384 l
189.729444 62.536249 l
190.42919 71.784644 l
191.828683 63.21296 l
192.528429 60.506113 l
193.228175 64.340814 l
193.927921 63.21296 l
194.627667 66.822091 l
195.327413 63.889672 l
196.027159 65.243096 l
196.726905 63.889672 l
197.426651 70.882362 l
198.126397 62.310678 l
198.826143 63.438531 l
199.52589 63.889672 l
200.225636 61.182825 l
200.925382 62.761819 l
201.625128 66.145379 l
202.324874 62.761819 l
203.02462 63.21296 l
204.424112 65.017525 l
205.123858 61.182825 l
205.823604 62.98739 l
206.52335 65.243096 l
207.922843 60.731683 l
208.622589 62.761819 l
209.322335 67.273232 l
210.721827 61.182825 l
211.421573 63.438531 l
212.121319 62.085107 l
212.821065 62.085107 l
213.520811 63.664102 l
214.220557 63.21296 l
214.920303 61.182825 l
215.620049 62.536249 l
216.319796 67.498802 l
217.019542 61.633966 l
217.719288 60.957254 l
218.419034 63.21296 l
219.11878 62.98739 l
219.818526 62.536249 l
220.518272 61.182825 l
221.218018 61.408395 l
221.917764 61.182825 l
222.61751 63.438531 l
224.017002 60.731683 l
224.716749 62.761819 l
225.416495 60.731683 l
226.116241 62.98739 l
226.815987 60.731683 l
227.515733 61.633966 l
228.215479 61.633966 l
228.915225 61.408395 l
229.614971 61.859537 l
231.014463 61.859537 l
231.714209 63.21296 l
232.413955 61.408395 l
233.813448 62.310678 l
234.513194 61.408395 l
235.21294 62.310678 l
235.912686 61.408395 l
236.612432 61.408395 l
237.312178 62.536249 l
238.011924 62.98739 l
238.71167 60.957254 l
239.411416 60.731683 l
240.111162 61.408395 l
240.810908 61.633966 l
241.510655 60.731683 l
242.910147 60.731683 l
243.609893 61.408395 l
244.309639 61.182825 l
245.009385 61.859537 l
245.709131 60.957254 l
246.408877 61.408395 l
247.108623 60.731683 l
247.808369 60.957254 l
248.508115 60.731683 l
249.207861 61.182825 l
249.907608 60.731683 l
252.706592 60.731683 l
253.406338 61.408395 l
254.106084 61.633966 l
254.80583 62.98739 l
255.505576 61.182825 l
256.205322 60.731683 l
258.304561 61.408395 l
259.004307 60.731683 l
259.704053 61.408395 l
260.403799 60.957254 l
261.103545 60.731683 l
261.803291 60.957254 l
262.503037 60.957254 l
263.202783 61.408395 l
263.902529 60.731683 l
264.602275 61.408395 l
265.302021 61.633966 l
266.001767 61.182825 l
266.701514 60.957254 l
267.40126 60.957254 l
268.101006 61.182825 l
268.800752 60.731683 l
269.500498 62.310678 l
270.200244 60.731683 l
270.89999 60.731683 l
271.599736 61.182825 l
272.299482 61.182825 l
272.999228 60.280542 l
273.698974 62.085107 l
274.39872 62.310678 l
275.098467 61.408395 l
275.798213 61.633966 l
276.497959 60.957254 l
277.197705 60.506113 l
277.897451 61.408395 l
278.597197 61.182825 l
279.296943 61.408395 l
279.996689 62.761819 l
280.696435 61.633966 l
281.396181 60.731683 l
282.095927 60.731683 l
282.795673 61.408395 l
283.49542 60.731683 l
284.195166 62.536249 l
284.894912 61.633966 l
285.594658 60.280542 l
286.294404 60.957254 l
286.99415 60.731683 l
287.693896 60.731683 l
288.393642 61.182825 l
289.093388 61.859537 l
289.793134 61.408395 l
290.49288 61.182825 l
291.192626 61.859537 l
291.892373 61.182825 l
292.592119 63.21296 l
293.291865 61.182825 l
293.991611 60.731683 l
294.691357 61.408395 l
295.391103 61.633966 l
296.790595 60.731683 l
297.490341 60.731683 l
298.190087 60.957254 l
298.889833 60.731683 l
300.289326 60.731683 l
300.989072 61.408395 l
301.688818 61.182825 l
302.388564 60.054972 l
303.788056 61.408395 l
304.487802 60.731683 l
305.187548 61.182825 l
305.887294 61.859537 l
306.58704 61.182825 l
307.286786 61.182825 l
307.986532 60.731683 l
311.485263 60.731683 l
312.185009 61.182825 l
312.884755 60.957254 l
313.584501 74.491492 l
314.284247 60.957254 l
314.983993 60.731683 l
315.683739 60.731683 l
316.383485 61.408395 l
317.083232 60.731683 l
317.782978 61.633966 l
318.482724 61.633966 l
319.882216 60.731683 l
320.581962 60.731683 l
321.281708 61.408395 l
321.981454 60.957254 l
322.6812 60.731683 l
323.380946 61.182825 l
324.080692 60.731683 l
326.179931 60.731683 l
326.879677 61.408395 l
327.579423 61.182825 l
328.279169 61.182825 l
328.978915 60.731683 l
329.678661 61.408395 l
330.378407 61.182825 l
331.078153 61.633966 l
332.477645 60.731683 l
334.576884 60.731683 l
335.27663 60.957254 l
335.976376 60.957254 l
336.676122 60.731683 l
337.375868 61.182825 l
338.075614 61.182825 l
338.77536 60.731683 l
339.475106 61.182825 l
340.174852 60.731683 l
340.874598 61.182825 l
341.574344 60.957254 l
342.274091 61.408395 l
342.973837 60.731683 l
343.673583 60.506113 l
344.373329 61.633966 l
347.172313 60.731683 l
347.872059 60.731683 l
348.571805 61.182825 l
349.271551 60.731683 l
349.971297 61.633966 l
350.671044 61.182825 l
351.37079 61.408395 l
352.070536 60.731683 l
353.470028 60.731683 l
354.169774 61.182825 l
354.86952 61.182825 l
355.569266 60.731683 l
356.968758 60.731683 l
357.668504 61.859537 l
358.36825 60.731683 l
359.067997 60.957254 l
359.767743 60.731683 l
361.167235 60.731683 l
361.866981 61.182825 l
362.566727 60.731683 l
366.065457 60.731683 l
366.765203 61.182825 l
367.46495 60.731683 l
370.96368 60.731683 l
371.663426 61.182825 l
372.363172 60.731683 l
373.062918 61.859537 l
373.762664 61.182825 l
374.46241 61.408395 l
375.162156 60.731683 l
376.561649 60.731683 l
377.261395 60.957254 l
377.961141 60.731683 l
380.060379 60.731683 l
380.760125 61.182825 l
381.459871 60.731683 l
382.159617 61.182825 l
383.559109 61.182825 l
384.258855 61.408395 l
384.958602 61.408395 l
385.658348 60.731683 l
388.457332 60.731683 l
389.157078 61.182825 l
389.856824 60.731683 l
390.55657 60.957254 l
391.256316 60.957254 l
391.956062 61.182825 l
392.655808 60.731683 l
394.755047 60.731683 l
395.454793 61.633966 l
396.154539 60.731683 l
401.052761 60.731683 l
401.752508 60.957254 l
402.452254 60.957254 l
403.152 63.21296 l
403.851746 60.731683 l
405.950984 60.731683 l
406.65073 61.408395 l
407.350476 60.731683 l
410.149461 60.731683 l
410.849207 60.957254 l
411.548953 60.731683 l
420.645652 60.731683 l
421.345398 61.182825 l
422.045144 60.731683 l
432.541335 60.731683 l
432.541335 60.731683 l
stroke
grestore
0.800 setlinewidth
0 setlinejoin
0.000 setgray
gsave
65.909375 46.971875 m
65.909375 334.8 l
stroke
grestore
gsave
450 46.971875 m
450 334.8 l
stroke
grestore
gsave
65.909375 46.971875 m
450 46.971875 l
stroke
grestore
gsave
65.909375 334.8 m
450 334.8 l
stroke
grestore

end
showpage
