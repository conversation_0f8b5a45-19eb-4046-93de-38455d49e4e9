import cv2
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from skimage.segmentation import random_walker
from skimage.filters import gaussian, threshold_otsu
from skimage.measure import label, regionprops_table
import pandas as pd
from skimage.measure import regionprops
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler
from scipy.ndimage import binary_dilation
# ===================== Step 0: 读取图像 =====================
# 读取输入图像
# 根据需要修改图像路径
image = cv2.imread("A.png")  # 替换为你的图像路径
# 检查图像是否成功读取
if image is None:
    raise ValueError("图像加载失败，请检查路径是否正确！")
######=====================A.图像预处理 =====================####### 
# ===================== Step 1: 转为灰度图像 =====================
# 转换为灰度图像，简化计算，突出亮度差异，适用于大多数分割算法
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
# ===================== Step 2: 图像平滑处理（滤波降噪） =====================
# 高斯滤波平滑图像，减少高频噪声，保持边缘信息
smoothed_image = gaussian(gray_image, sigma=1)
# 中值滤波进一步去除椒盐噪声
median_image = cv2.medianBlur((smoothed_image * 255).astype(np.uint8), 5)
# ===================== Step 3: 增强对比度 =====================
# 使用直方图均衡化增强对比度，突出图像细节
# enhanced_image = cv2.equalizeHist(median_image)
# 自适应直方图均衡化（CLAHE），避免过度增强，适用于局部对比度提升
clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
enhanced_image = clahe.apply(median_image)
# ===================== Step 4: 边缘增强与纹理特征提取 =====================
# Canny 边缘检测，提取海冰与水体的边缘特征
edges = cv2.Canny(enhanced_image, threshold1=50, threshold2=150)
# 使用拉普拉斯滤波器进一步增强边缘，突出细节变化
# laplacian_edges = cv2.Laplacian(enhanced_image, cv2.CV_64F)
# ===================== Step 5: 可视化结果 =====================
# 显示原始图像和处理后的图像
plt.figure(figsize=(12, 8))
plt.subplot(2, 3, 1)
plt.title("Original Image")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.axis('off')
plt.subplot(2, 3, 2)
plt.title("Grayscale Image")
plt.imshow(gray_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 3, 3)
plt.title("Smoothed Image")
plt.imshow(median_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 3, 4)
plt.title("median_image")
plt.imshow(median_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 3, 5)
plt.title("Contrast Enhanced")
plt.imshow(enhanced_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 3, 6)
plt.title("Canny Edges")
plt.imshow(edges, cmap='gray')
plt.axis('off')
plt.tight_layout()
plt.show()
# ===================== 3D 可视化灰度图像 =====================
h, w = gray_image.shape
x = np.linspace(0, w, w)
y = np.linspace(0, h, h)
x, y = np.meshgrid(x, y)  # 生成网格坐标
z = gray_image  # 将灰度值作为 z 高度
z = enhanced_image  # 将灰度值作为 z 高度
# 创建 3D 绘图
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')
# 绘制曲面
surface = ax.plot_surface(x, y, z, cmap="gray", edgecolor='none')
# 添加颜色条
fig.colorbar(surface, ax=ax, shrink=0.5, aspect=10)
# 设置标题和轴标签
ax.set_title("3D Visualization of Grayscale Image")
ax.set_xlabel("X-axis")
ax.set_ylabel("Y-axis")
ax.set_zlabel("Pixel Intensity")
plt.show()
######=====================B.图像分割 =====================####### 
image_seg=enhanced_image
# ===================== 方法 1: 应用 Otsu 阈值分割 =====================
# 使用 Otsu 方法自动确定阈值进行二值化
_, otsu_thresh = cv2.threshold((image_seg * 255).astype(np.uint8), 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
# ===================== 方法 2: 应用 Gabor 滤波器 + K-Means 聚类分割=====================
def apply_gabor_filter(image, ksize=4, sigma=3.0, theta=np.pi/4, lambd=10.0, gamma=0.5):
    """
    应用 Gabor 滤波器来提取特定方向和频率的纹理特征。
    参数：
        image: 输入灰度图像。
        ksize: Gabor 核的大小。
        sigma: Gabor 核的高斯标准差。
        theta: 滤波器方向（弧度）。
        lambd: 波长。
        gamma: 空间纵横比。
    返回：
        filtered_image: 滤波后的图像。
    """
    gabor_kernel = cv2.getGaborKernel((ksize, ksize), sigma, theta, lambd, gamma, 0, ktype=cv2.CV_32F)
    filtered_image = cv2.filter2D(image, cv2.CV_8UC3, gabor_kernel)
    return filtered_image
# 应用 Gabor 滤波器
filtered_image = apply_gabor_filter(image_seg)
# 将 Gabor 滤波后的图像展平为二维数组（因为是灰度图像，只有一个通道）
pixel_values = image_seg.reshape((-1, 1))
#pixel_values = filtered_image.reshape((-1, 1))
pixel_values = np.float32(pixel_values)  # 转为浮点型，便于 K-Means 处理
# 定义 K-Means 聚类参数
criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 100, 0.2)  # 迭代停止条件
k = 3  # 目标分为两类（海冰和水）
_, labels, centers = cv2.kmeans(pixel_values, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
# 将聚类结果映射回图像形状
segmented_image_kmeans = labels.reshape(filtered_image.shape)
# ===================== 方法 3: 阈值分割（固定阈值） =====================
_, fixed_thresh = cv2.threshold(image_seg, 127, 255, cv2.THRESH_BINARY)
# ===================== 方法 4: 随机游走方法 =====================
# 初始化标记矩阵，0 表示未标记的像素
markers = np.zeros_like(image_seg, dtype=np.int32)
# 手动设置前景（海冰）和背景（水）的标记
markers[image_seg < 100] = 1  # 背景标记
markers[image_seg > 150] = 2  # 前景标记
# 使用随机游走算法对图像进行分割
labels_rw = random_walker(smoothed_image, markers, beta=10, mode='bf')
# ===================== 最终可视化对比 =====================
plt.figure(figsize=(18, 12))

# 原始图像
plt.subplot(2, 3, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.title("Original Image")
plt.axis("off")

# Otsu 阈值分割
plt.subplot(2, 3, 2)
plt.imshow(otsu_thresh, cmap="gray")
plt.title("Otsu Thresholding")
plt.axis("off")

# Gabor 滤波器 + K-Means 分割
plt.subplot(2, 3, 3)
plt.imshow(segmented_image_kmeans, cmap="jet")
plt.title("Gabor + K-Means")
plt.axis("off")

# 固定阈值分割
plt.subplot(2, 3, 4)
plt.imshow(fixed_thresh, cmap="gray")
plt.title("Fixed Threshold")
plt.axis("off")

# 随机游走分割
plt.subplot(2, 3, 5)
plt.imshow(labels_rw, cmap="gray")
plt.title("Random Walker")
plt.axis("off")
plt.tight_layout()
plt.show()
# ===================== 可视化随机游走分割结果 =====================
plt.figure(figsize=(15, 10))
# 原始灰度图像
plt.subplot(1, 3, 1)
plt.imshow(gray_image, cmap='gray')
plt.title("Original Grayscale Image")
plt.axis("off")
# 标记结果
plt.subplot(1, 3, 2)
plt.imshow(markers, cmap='jet')
plt.title("Markers")
plt.axis("off")
# 随机游走分割结果
plt.subplot(1, 3, 3)
plt.imshow(labels_rw, cmap='gray')
plt.title("Segmented Image (Random Walker)")
plt.axis("off")
plt.tight_layout()
plt.show()

# =====================高级统计分析功能=====================
ice_image=fixed_thresh
from scipy import ndimage
import matplotlib.patches as patches
#使用分割得到的标签图，每个连通区域可以视为一块独立的海冰。
# 识别连通区域,进行单一的颜色识别
labeled_ice, num_features = ndimage.label(ice_image)

# 可视化每块海冰
plt.figure(figsize=(10, 8))
plt.imshow(labeled_ice, cmap='nipy_spectral')
plt.title(f"Identified Sea Ice Blocks: {num_features}")
plt.axis("off")
plt.show()

# 创建图像
fig, ax = plt.subplots(figsize=(10, 8))
ax.imshow(labeled_ice, cmap='nipy_spectral')
ax.set_title(f"Identified Sea Ice Blocks: {num_features}")
ax.axis("off")

# 识别每个连通区域的边界框
objects = ndimage.find_objects(labeled_ice)
# 绘制红色方框标记每块海冰
for obj_slice in objects:
    if obj_slice is not None:
        y_start, y_stop = obj_slice[0].start, obj_slice[0].stop
        x_start, x_stop = obj_slice[1].start, obj_slice[1].stop
        width = x_stop - x_start
        height = y_stop - y_start
        # 添加红色方框
        rect = patches.Rectangle((x_start, y_start), width, height, 
                                 linewidth=2, edgecolor='red', facecolor='none')
        ax.add_patch(rect)
plt.show()

#2.1 海冰的膨胀安全距离
weighted_image=

#定义膨胀操作以模拟 "充气" 海冰，保证安全距离
safety_margin = 1  # 安全边距
inflated_ice = binary_dilation(fixed_thresh, iterations=safety_margin)

#创建加权图像，初步为空白区域（0值）和冰厚区域（根据 gray_image 加权）
weighted_image = np.copy(gray_image)

#设置膨胀区域为障碍物，其他空白区域根据冰厚值加权
weighted_image[inflated_ice == 1] = 1  # 膨胀区域赋予最大冰厚值
weighted_image[inflated_ice == 0] = gray_image[inflated_ice == 0]  # 其他区域保留原冰厚值

# 可视化
plt.figure(figsize=(8, 6))
plt.imshow(weighted_image, cmap='jet', origin='upper')
plt.axis('off')
plt.show()

#2.1 海冰厚度的估计（基于灰度特征）
#厚度估算改进： 如果有真实厚度数据，可以通过回归模型建立更精准的灰度-厚度映射。
#假设灰度值与厚度呈一定的物理关系，可以通过以下方式估算：
# 假设 gray_image 是你的冰厚图像
scaler = MinMaxScaler()
# 将 gray_image 扁平化为一维数组，MinMaxScaler 要求二维数据
gray_image_flattened = weighted_image.flatten().reshape(-1, 1)
# 对数据进行归一化
normalized_gray_image_flattened = scaler.fit_transform(gray_image_flattened)
# 将归一化后的图像重新调整为原来的形状
normalized_image = normalized_gray_image_flattened.reshape(gray_image.shape)

plt.figure(figsize=(8, 6))
plt.imshow(normalized_image, cmap='jet', origin='upper')
plt.axis('off')
plt.show()

# 计算每块海冰的平均灰度值（作为厚度的近似）
thickness_list = ndimage.mean(normalized_image, labels=labeled_ice, index=range(1, num_features + 1))
# 输出厚度信息
for i, thickness in enumerate(thickness_list, 1):
    print(f"Sea Ice Block {i}: Estimated Thickness = {thickness:.2f}")
    
#2.2 图片的冰区覆盖率
ice_area = np.sum(ice_image > 0)  # 冰层区域的面积
total_area = ice_image.size  # 图像的总面积
coverage = (ice_area / total_area) * 100  # 覆盖率（百分比）
print(f"Ice Coverage: {coverage:.2f}%")

#2.3 图片的冰区密集度
# 假设图像被划分为网格，例如每个网格为10x10像素
grid_size = 50
grid_rows = gray_image.shape[0] // grid_size
grid_cols = gray_image.shape[1] // grid_size

#2.3 冰层覆盖率
density_map = np.zeros((grid_rows, grid_cols))
for row in range(grid_rows):
    for col in range(grid_cols):
        # 提取当前网格区域
        grid_area = ice_image[row * grid_size:(row + 1) * grid_size, col * grid_size:(col + 1) * grid_size]
        # 计算当前网格的冰层覆盖率
        ice_in_grid = np.sum(grid_area > 0)
        total_grid_area = grid_area.size
        density_map[row, col] = (ice_in_grid / total_grid_area) * 100  # 覆盖率百分比

# 可视化冰层分布密度
plt.figure(figsize=(8, 6))
plt.imshow(density_map, cmap='jet', interpolation='nearest')
plt.colorbar(label='Ice Coverage (%)')
plt.title('Ice Distribution Density')
plt.axis('off')
plt.show()

# 3. 海冰大小的统计（面积和形状特征）
# 可以通过计算每块海冰的像素数量来估算面积：
# 计算每块海冰的面积（像素数）
area_list = ndimage.sum(np.ones_like(gray_image), labels=labeled_ice, index=range(1, num_features + 1))

# 输出面积信息
for i, area in enumerate(area_list, 1):
    print(f"Sea Ice Block {i}: Area = {area} pixels")
# 计算形状特征
props = regionprops(labeled_ice, intensity_image=gray_image)
for i, prop in enumerate(props, 1):
    print(f"Sea Ice Block {i}: Area = {prop.area}, Perimeter = {prop.perimeter}, Eccentricity = {prop.eccentricity}")

# 厚度分布
plt.figure(figsize=(8, 4))
sns.histplot(thickness_list, kde=True)
plt.title("Distribution of Sea Ice Thickness")
plt.xlabel("Estimated Thickness")
plt.ylabel("Frequency")
plt.show()

# 面积分布
plt.figure(figsize=(8, 4))
sns.histplot(area_list, kde=True, color="orange")
plt.title("Distribution of Sea Ice Area")
plt.xlabel("Area (pixels)")
plt.ylabel("Frequency")
plt.show()

# 4. 单一海冰的特征
#关联labeled_ice和厚度，形状特征，可以选择绘制某个海冰到一个图中，并展示相关信息进行绘制
# 假设你已经有了 labeled_ice 和 thickness_list、props 等信息
# 选择你想要显示的海冰块，例如选择海冰块1
target_block = 20
# 创建一个掩膜，仅保留目标海冰块
target_mask = labeled_ice == target_block
# 可视化该目标海冰块（白色）
plt.figure(figsize=(10, 8))
plt.imshow(target_mask, cmap='gray')
plt.title(f"Sea Ice Block {target_block}: Thickness = {thickness_list[target_block - 1]:.2f}")
plt.axis("off")
plt.show()
# 输出该目标海冰块的相关形状特征
target_prop = props[target_block - 1]
print(f"Sea Ice Block {target_block}:")
print(f"Estimated Thickness = {thickness_list[target_block - 1]:.2f}")
print(f"Area = {target_prop.area} pixels")
print(f"Perimeter = {target_prop.perimeter:.2f}")
print(f"Eccentricity = {target_prop.eccentricity:.2f}")
# ===================== 路径规划功能 =====================
from pathfinding.core.grid import Grid
from pathfinding.finder.a_star import AStarFinder
from pathfinding.finder.best_first import BestFirst 
from pathfinding.finder.bi_a_star import BiAStarFinder
from pathfinding.finder.breadth_first import BreadthFirstFinder
from pathfinding.finder.dijkstra import DijkstraFinder
from pathfinding.finder.ida_star import IDAStarFinder
from pathfinding.finder.msp import MinimumSpanningTree 
from pathfinding.finder.finder import ExecutionRunsException, ExecutionTimeException
from pathfinding.core.diagonal_movement import DiagonalMovement

# ===================== 数据处理 =====================
路径长度，
转弯次数，
路径平滑性，
路径可行性（冰厚），
海冰风险指数（覆盖率）density_map


# ===================== 路径规划参数 ====================
import heapq
import numpy as np
import matplotlib.pyplot as plt

# 计算欧几里得距离作为启发式函数
def heuristic(a, b):
    return np.linalg.norm(np.array(a) - np.array(b))

# A*算法
def astar_algorithm(weighted_image, start, goal):
    rows, cols = weighted_image.shape
    open_list = []
    heapq.heappush(open_list, (0 + heuristic(start, goal), 0, start))
    came_from = {}
    g_score = {start: 0}
    f_score = {start: heuristic(start, goal)}
    
    while open_list:
        _, current_g, current = heapq.heappop(open_list)
        
        if current == goal:
            path = []
            while current in came_from:
                path.append(current)
                current = came_from[current]
            path.append(start)
            path.reverse()
            return path, g_score[goal]
        
        for neighbor in neighbors(current, rows, cols):
            if weighted_image[neighbor] == 1:  # 如果是不可通行区域
                continue
            tentative_g_score = current_g + weighted_image[neighbor]  # 根据浮冰的风险设置路径代价
            if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                came_from[neighbor] = current
                g_score[neighbor] = tentative_g_score
                f_score[neighbor] = tentative_g_score + heuristic(neighbor, goal)
                heapq.heappush(open_list, (f_score[neighbor], tentative_g_score, neighbor))
    
    return None, float('inf')

# Dijkstra算法
def dijkstra_algorithm(weighted_image, start, goal):
    rows, cols = weighted_image.shape
    open_list = []
    heapq.heappush(open_list, (0, start))
    came_from = {}
    g_score = {start: 0}
    
    while open_list:
        current_g, current = heapq.heappop(open_list)
        
        if current == goal:
            path = []
            while current in came_from:
                path.append(current)
                current = came_from[current]
            path.append(start)
            path.reverse()
            return path, g_score[goal]
        
        for neighbor in neighbors(current, rows, cols):
            if weighted_image[neighbor] == 1:  # 如果是不可通行区域
                continue
            tentative_g_score = current_g + weighted_image[neighbor]  # 根据浮冰的风险设置路径代价
            if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                came_from[neighbor] = current
                g_score[neighbor] = tentative_g_score
                heapq.heappush(open_list, (tentative_g_score, neighbor))
    
    return None, float('inf')

# 邻居函数：返回当前节点的所有可行邻居
def neighbors(node, rows, cols):
    x, y = node
    neighbors = []
    for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
        nx, ny = x + dx, y + dy
        if 0 <= nx < rows and 0 <= ny < cols:
            neighbors.append((nx, ny))
    return neighbors

# 可视化路径
def plot_path(weighted_image, path):
    fig, ax = plt.subplots(figsize=(10, 10))
    ax.imshow(weighted_image, cmap='Blues')
    path_x, path_y = zip(*path)
    ax.plot(path_y, path_x, color='red', linewidth=3)
    plt.gca().invert_yaxis()
    plt.show()

# 创建一个加权图（带有海冰）
def create_weighted_image(rows, cols):
    weighted_image = np.random.rand(rows, cols)  # 随机生成一个0-1之间的浮动海冰图像
    weighted_image[weighted_image < 0.2] = 1  # 设置为冰的区域（不可通行）
    return weighted_image

# 主程序
# 定义起点和终点，起点在右下角，终点在左上角
start = (weighted_image.shape[1] - 1, weighted_image.shape[0] - 1) 
goal = (0, 0)

# A*算法
path_astar, cost_astar = astar_algorithm(weighted_image, start, goal)

# Dijkstra算法
path_dijkstra, cost_dijkstra = dijkstra_algorithm(weighted_image, start, goal)

# 可视化路径
if path_astar:
    plot_path(weighted_image, path_astar)
if path_dijkstra:
    plot_path(weighted_image, path_dijkstra)

import gym
import numpy as np
from gym import spaces
import matplotlib.pyplot as plt

class SeaIceEnv(gym.Env):
    def __init__(self, weighted_image):
        super(SeaIceEnv, self).__init__()

        self.grid_map = weighted_image
        self.start = (weighted_image.shape[0] - 1, weighted_image.shape[1] - 1)  # 右下角
        self.goal = (0, 0)  # 左上角
        
        # 定义动作空间（上、下、左、右）
        self.action_space = spaces.Discrete(4)
        
        # 定义状态空间为网格大小
        self.observation_space = spaces.Box(low=0, high=1, shape=(weighted_image.shape[0], weighted_image.shape[1]), dtype=np.float32)
        
        self.state = self.start
        self.done = False
        self.path = []
        
    def step(self, action):
        if self.done:
            return self.state, 0, self.done, {}

        x, y = self.state
        if action == 0:  # 向上
            new_state = (x-1, y)
        elif action == 1:  # 向下
            new_state = (x+1, y)
        elif action == 2:  # 向左
            new_state = (x, y-1)
        elif action == 3:  # 向右
            new_state = (x, y+1)

        # 判断新的位置是否在有效范围内
        if 0 <= new_state[0] < self.grid_map.shape[0] and 0 <= new_state[1] < self.grid_map.shape[1]:
            # 获取当前位置的碰撞风险
            risk = self.grid_map[new_state]
            reward = -risk  # 越大的风险，奖励越低

            # 到达终点
            if new_state == self.goal:
                self.done = True
                reward = 100  # 到达目标时获得较大奖励
                self.path.append(new_state)

            self.state = new_state
            self.path.append(new_state)
            return self.state, reward, self.done, {}

        # 如果越界，则惩罚
        return self.state, -10, self.done, {}

    def reset(self):
        self.state = self.start
        self.done = False
        self.path = [self.start]
        return self.state

    def render(self):
        grid = np.zeros_like(self.grid_map)
        for (x, y) in self.path:
            grid[x, y] = 1
        plt.imshow(grid, cmap='Blues')
        plt.title("Path Visualization")
        plt.show()

import random
import numpy as np

class QLearningAgent:
    def __init__(self, action_space, alpha=0.1, gamma=0.9, epsilon=0.1):
        self.action_space = action_space
        self.alpha = alpha
        self.gamma = gamma
        self.epsilon = epsilon
        self.q_table = {}

    def get_state_key(self, state):
        return tuple(state)

    def choose_action(self, state):
        state_key = self.get_state_key(state)
        if random.uniform(0, 1) < self.epsilon:
            return random.choice(range(self.action_space.n))  # 探索
        else:
            # 选择最大Q值的动作
            return np.argmax(self.q_table.get(state_key, np.zeros(self.action_space.n)))

    def update_q(self, state, action, reward, next_state):
        state_key = self.get_state_key(state)
        next_state_key = self.get_state_key(next_state)
        current_q = self.q_table.get(state_key, np.zeros(self.action_space.n))[action]
        future_q = np.max(self.q_table.get(next_state_key, np.zeros(self.action_space.n)))
        # Q值更新公式
        new_q = current_q + self.alpha * (reward + self.gamma * future_q - current_q)
        if state_key not in self.q_table:
            self.q_table[state_key] = np.zeros(self.action_space.n)
        self.q_table[state_key][action] = new_q

def train_qlearning(env, agent, episodes=1000):
    for _ in range(episodes):
        state = env.reset()
        done = False
        while not done:
            action = agent.choose_action(state)
            next_state, reward, done, _ = env.step(action)
            agent.update_q(state, action, reward, next_state)
            state = next_state

import heapq

def astar(weighted_image, start, goal):
    def heuristic(a, b):
        return abs(a[0] - b[0]) + abs(a[1] - b[1])

    open_list = []
    heapq.heappush(open_list, (0, start))
    came_from = {}
    g_score = {start: 0}
    f_score = {start: heuristic(start, goal)}

    while open_list:
        _, current = heapq.heappop(open_list)

        if current == goal:
            path = []
            while current in came_from:
                path.append(current)
                current = came_from[current]
            path.reverse()
            return path

        for neighbor in [(current[0]-1, current[1]), (current[0]+1, current[1]),
                         (current[0], current[1]-1), (current[0], current[1]+1)]:
            if 0 <= neighbor[0] < weighted_image.shape[0] and 0 <= neighbor[1] < weighted_image.shape[1]:
                tentative_g_score = g_score[current] + weighted_image[neighbor]
                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = tentative_g_score + heuristic(neighbor, goal)
                    heapq.heappush(open_list, (f_score[neighbor], neighbor))

    return None  # No path found



# 假设 weighted_image 已经加载
env = SeaIceEnv(weighted_image)
agent = QLearningAgent(env.action_space)
train_qlearning(env, agent)

# Q-learning 结果
path_rl = env.path
risk_rl = sum(weighted_image[x, y] for x, y in path_rl)
length_rl = len(path_rl)

# A* 结果
path_astar = astar(weighted_image, env.start, env.goal)
risk_astar = sum(weighted_image[x, y] for x, y in path_astar)
length_astar = len(path_astar)

# Dijkstra 结果
path_dijkstra = dijkstra(weighted_image, env.start, env.goal)
risk_dijkstra = sum(weighted_image[x, y] for x, y in path_dijkstra)
length_dijkstra = len(path_dijkstra)

print(f"Q-learning Path: {path_rl}, Risk: {risk_rl}, Length: {length_rl}")
print(f"A* Path: {path_astar}, Risk: {risk_astar}, Length: {length_astar}")
print(f"Dijkstra Path: {path_dijkstra}, Risk: {risk_dijkstra}, Length: {length_dijkstra}")

# 可视化路径
def visualize_path(weighted_image, path, title="Path"):
    grid = np.zeros_like(weighted_image)
    for (x, y) in path:
        grid[x, y] = 1
    plt.imshow(weighted_image, cmap='Blues', alpha=0.5)
    plt.imshow(grid, cmap='Reds', alpha=0.7)
    plt.title(title)
    plt.show()

visualize_path(weighted_image, path_rl, title="Q-learning Path")
visualize_path(weighted_image, path_astar, title="A* Path")
visualize_path(weighted_image, path_dijkstra, title="Dijkstra Path")
