[build-system]
requires = ["setuptools>=43.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "python-motion-planning"
version = "1.1.1"
description = "Motion planning algorithms for Python"
maintainers = [
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
license = {file = "LICENSE"}
requires-python = ">=3.6"
dependencies = [
    "numpy",
    "scipy",
    "matplotlib",
    "cvxopt",
    "osqp",
    "tqdm"
]
classifiers = [
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
]

[project.urls]
Repository = "https://github.com/ai-winter/python_motion_planning"

[tool.setuptools.packages.find]
where = ["src"]