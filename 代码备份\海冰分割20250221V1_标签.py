import cv2
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from skimage.segmentation import slic, random_walker
from skimage.filters import gaussian, threshold_otsu
from skimage.measure import label, regionprops_table
import pandas as pd
from skimage.measure import regionprops
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler
from scipy.ndimage import binary_dilation
import matplotlib.pyplot as plt
import math
import numpy as np
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
from skimage.filters import gabor_kernel
from scipy import ndimage as ndi
import os
from sklearn.cluster import MeanShift, DBSCAN
#thresholds=[]
# ===================== Step 0: 读取图像 =====================
threshold=120
#thresholds.append(threshold)
image_name = "sea_ice_078"
base_path = "D:/代码/ICE/sea_ice"
image_path = os.path.join(base_path, f"{image_name}.jpg")
# 检查图像路径是否存在
if not os.path.exists(image_path):
    raise FileNotFoundError(f"图像路径 '{image_path}' 不存在，请检查路径是否正确！")
# 读取输入图像
image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
# 检查图像是否成功读取
if image is None:
    raise ValueError(f"无法读取图像 '{image_path}'，请检查文件是否损坏或格式是否支持！")
######=====================A.图像预处理 =====================####### 
# ===================== Step 1: 转为灰度图像 =====================
# 转换为灰度图像，简化计算，突出亮度差异，适用于大多数分割算法
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
# ===================== Step 2: 减少反光 =====================
#光照反光抑制与对比度增强：  为了减轻反光干扰，并增强冰水之间的视觉差异
#同态滤波 (Homomorphic Filtering)： 可以将图像的照度分量和反射分量分离，
#减弱照度分量的影响，从而减少光照不均匀和反光现象。
def homomorphic_filter(img, gamma_h=1.5, gamma_l=0.5, d0=10, c=1):
    """
    同态滤波器
    Args:
        img: 灰度图像
        gamma_h: 高频增益
        gamma_l: 低频增益
        d0: 截止频率
        c: 锐化系数
    Returns:
        滤波后的图像
    """
    rows, cols = img.shape
    img_log = np.log1p(np.array(img, dtype="float") / 255) # 转换为对数域
    # 构建滤波器
    H = np.zeros((rows, cols), np.float32)
    for i in range(rows):
        for j in range(cols):
            H[i, j] = (gamma_h - gamma_l) * (1 - np.exp(-c * ((i**2 + j**2) / d0**2))) + gamma_l
    # 频域变换
    img_fft = np.fft.fft2(img_log)
    img_fft_shifted = np.fft.fftshift(img_fft)
    # 滤波
    img_fft_filtered = img_fft_shifted * H
    img_fft_inverse_shifted = np.fft.ifftshift(img_fft_filtered)
    img_filtered = np.fft.ifft2(img_fft_inverse_shifted)
    # 指数变换，恢复到图像灰度范围
    img_exp = np.expm1(np.real(img_filtered))
    img_output = np.array(np.clip(img_exp * 255 + 0.5, 0, 255), dtype=np.uint8)
    return img_output
homo_image = homomorphic_filter(gray_image) # 对增强对比度后的图像进行同态滤波
# ===================== Step 3: 图像平滑处理（滤波降噪） =====================
# 高斯滤波平滑图像，减少高频噪声，保持边缘信息
smoothed_image = gaussian(homo_image, sigma=0.5)
# 中值滤波进一步去除椒盐噪声
median_image = cv2.medianBlur((smoothed_image * 255).astype(np.uint8), 5)
# ===================== Step 4: 增强对比度 =====================
# 使用直方图均衡化增强对比度，突出图像细节
#enhanced_image = cv2.equalizeHist(homo_image)
# 自适应直方图均衡化（CLAHE），避免过度增强，适用于局部对比度提升
clahe = cv2.createCLAHE(clipLimit=1.0, tileGridSize=(8, 8))
enhanced_image = clahe.apply(homo_image)
# clahe = cv2.createCLAHE(clipLimit=1.0, tileGridSize=(8, 8))
# enhanced_image = clahe.apply(enhanced_image)
# ===================== Step 4: 边缘增强与纹理特征提取 =====================
# Canny 边缘检测，提取海冰与水体的边缘特征
edges = cv2.Canny(enhanced_image, threshold1=50, threshold2=150)
# 使用拉普拉斯滤波器进一步增强边缘，突出细节变化
# laplacian_edges = cv2.Laplacian(enhanced_image, cv2.CV_64F)

# # 定义 Gabor 滤波器参数
# frequencies = [0.1, 0.5, 1.0, ]  # 不同频率
# thetas = [0, np.pi/4, np.pi/2, 3*np.pi/4]  # 不同方向

# # 定义图像金字塔参数
# num_levels = 3  # 金字塔层数

# # 构建图像金字塔
# pyramid = [enhanced_image]
# for i in range(1, num_levels):
#     downsampled = cv2.pyrDown(pyramid[i - 1])
#     pyramid.append(downsampled)

# # 存储 Gabor 滤波结果
# gabor_responses = []

# # 预计算 Gabor 滤波器内核
# kernels = {}
# for freq in frequencies:
#     for theta in thetas:
#         kernel = cv2.getGaborKernel((21, 21), sigma=5, theta=theta, lambd=1/freq, gamma=1, psi=0, ktype=cv2.CV_32F)
#         kernel /= np.sqrt((kernel * kernel).sum())
#         kernels[(freq, theta)] = kernel

# # 对每个金字塔层级的图像进行 Gabor 滤波
# plt.figure(figsize=(20, 12),dpi=500)
# plot_index = 1
# for level, img in enumerate(pyramid):
#     for freq in frequencies:
#         for theta in thetas:
#             # 获取预计算的 Gabor 滤波器内核
#             kernel = kernels[(freq, theta)]
#             # 应用 Gabor 滤波器
#             filtered = cv2.filter2D(img, cv2.CV_8UC3, kernel)
#             # 计算幅度响应
#             magnitude = np.abs(filtered)
#             # 存储结果
#             gabor_responses.append(magnitude)
#             # 显示结果
#             plt.subplot(num_levels, len(frequencies) * len(thetas), plot_index)
#             plt.imshow(magnitude, cmap='gray')
#            # plt.title(f"Level={level}, Freq={freq:.1f}, Theta={theta:.2f}")
#             plt.axis('off')
#             plot_index += 1
# plt.tight_layout()
# plt.show()

# # 将所有 Gabor 响应拼接成一个特征矩阵
# features = []
# original_image_shape = pyramid[0].shape[:2] # 获取原始图像尺寸，假设 pyramid[0] 是原始图像或增强后的图像
# for level_response in gabor_responses:
#     # 确保所有响应图像尺寸一致，或者调整到原始图像尺寸 (例如使用 resize)
#     resized_response = cv2.resize(level_response, original_image_shape[::-1], interpolation=cv2.INTER_LINEAR)
#     if len(features) == 0:
#         features = resized_response.reshape((-1, 1)) # 初始化特征，假设第一个响应作为初始特征
#     else:
#         features = np.concatenate((features, resized_response.reshape((-1, 1))), axis=1) # 将新的响应添加到特征向量
        
# from sklearn.decomposition import PCA
# pca = PCA(n_components=2)
# features= pca.fit_transform(features)
# feature_image= np.concatenate((enhanced_image.reshape(-1,1), features), axis=1) # 拼接

# ===================== Step 5: 可视化结果 =====================
# 显示原始图像和处理后的图像
plt.figure(figsize=(12, 9),dpi=200)
plt.subplot(3, 3, 1)
plt.title("Original Image")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.axis('off')
plt.subplot(3, 3, 2)
plt.title("Grayscale Image")
plt.imshow(gray_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 3)
plt.title("homo_image")
plt.imshow(homo_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 4)
plt.title("Smoothed Image")
plt.imshow(median_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 5)
plt.title("median_image")
plt.imshow(median_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 6)
plt.title("Contrast Enhanced")
plt.imshow(enhanced_image, cmap='gray')
plt.axis('off')
plt.subplot(3, 3, 7)
plt.title("Canny Edges")
plt.imshow(edges, cmap='gray')
plt.axis('off')
# plt.subplot(3, 3, 8)
# plt.title("Canny Edges")
# plt.imshow(magnitude, cmap='gray')
# plt.axis('off')
# plt.tight_layout()
plt.show()
# ===================== 3D 可视化灰度图像 =====================
h, w = gray_image.shape
x = np.linspace(0, w, w)
y = np.linspace(0, h, h)
x, y = np.meshgrid(x, y)  # 生成网格坐标
z = gray_image  # 将灰度值作为 z 高度
z = enhanced_image  # 将灰度值作为 z 高度
# 创建 3D 绘图
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')
# 绘制曲面
surface = ax.plot_surface(x, y, z, cmap="gray", edgecolor='none')
# 添加颜色条
fig.colorbar(surface, ax=ax, shrink=0.5, aspect=10)
# 设置标题和轴标签
ax.set_title("3D Visualization of Grayscale Image")
ax.set_xlabel("X-axis")
ax.set_ylabel("Y-axis")
ax.set_zlabel("Pixel Intensity")
plt.show()
# --------------------- 图像分割方法 ---------------------
image_seg = enhanced_image
#image_data=enhanced_image.reshape((-1, 1))

# 方法 1: 应用 Otsu 阈值分割
_, otsu_thresh = cv2.threshold((image_seg * 255).astype(np.uint8), 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

# 方法 3: 阈值分割（固定阈值）
_, fixed_thresh = cv2.threshold((image_seg * 255).astype(np.uint8), threshold, 255, cv2.THRESH_BINARY)

# 方法 4：自适应阈值
adaptive_thresh = cv2.adaptiveThreshold((image_seg * 255).astype(np.uint8), 255,
                                        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                        cv2.THRESH_BINARY, 11, 2)

# 方法 4: 随机游走方法
markers = np.zeros_like(image_seg, dtype=np.int32)
markers[image_seg < 100] = 1  # 调整阈值以适应归一化图像
markers[image_seg > 150] = 2  # 调整阈值以适应归一化图像
labels_rw = random_walker(image_seg, markers, beta=5, mode='bf')

# 方法5：分水岭算法
_, thresh = cv2.threshold(image_seg, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
dist_transform = cv2.distanceTransform(thresh, cv2.DIST_L2, 5)
_, sure_fg = cv2.threshold(dist_transform, 0.5*dist_transform.max(), 255, 0)
sure_fg = np.uint8(sure_fg)
unknown = cv2.subtract(thresh, sure_fg)
_, markers = cv2.connectedComponents(sure_fg)
markers += 1
markers[unknown==255] = 0
markers = cv2.watershed(cv2.merge([image_seg]*3), markers)
watershed_seg = np.where(markers == 1, 0, 255).astype(np.uint8)

# 方法 6：边缘检测+填充分割
edges = cv2.Canny(image_seg, 50, 150)
contours, _ = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
mask = np.zeros_like(image_seg)
edges_seg = cv2.drawContours(mask, contours, -1, 255, thickness=cv2.FILLED)

# 方法 7: 应用 K-Means 聚类分割
# pixel_values = np.float32(feature_image.reshape((-1, 1)))
# criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 100, 0.2)
# k = 2
# _, labels, centers = cv2.kmeans(pixel_values, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
# segmented_image_kmeans = labels.reshape(enhanced_image.shape)

# 方法8： 深度学习分割 (Deep Learning Segmentation)
# 提示：深度学习分割通常需要预训练模型和复杂的环境配置。
# 这里仅作为概念展示，实际应用需要使用如 TensorFlow, PyTorch 等框架加载模型并进行推理。
# 例如使用预训练的 Mask R-CNN, U-Net 等模型进行海冰/水分割。
# deep_learning_seg = ...  #  需要加载模型并进行预测的代码，此处省略具体实现

# 创建一个包含所有子图的布局
fig, axes = plt.subplots(2, 3, figsize=(18, 12), dpi=500)
# 图像标题与对应的处理方法
images = [
    (cv2.cvtColor(image, cv2.COLOR_BGR2RGB), "Original Image"),
    (otsu_thresh, "Otsu Thresholding"),
    (fixed_thresh, "Fixed Threshold"),
    (adaptive_thresh, "Adaptive Threshold"),
    (labels_rw, "Random Walker"),
    (watershed_seg, "Watershed"),
    #(segmented_image_kmeans, "K-Means")
]
# 遍历子图并展示各个图像
for i, (img, title) in enumerate(images):
    ax = axes[i // 3, i % 3]  # 计算位置
    ax.imshow(img, cmap='gray' if i != 6 else 'gray')  # K-means 使用 jet 色图
    ax.set_title(title)
    ax.axis('off')
# 调整布局，使各个图像之间的间距合适
plt.tight_layout()
plt.show()
# 保存为 Excel
base_path = "C:/Users/<USER>/Desktop/ICE/label"
output_excel_path = os.path.join(base_path, f"{image_name}.xlsx")
df = pd.DataFrame(fixed_thresh)
#df = pd.DataFrame(otsu_thresh)
os.makedirs(os.path.dirname(output_excel_path), exist_ok=True)
df.to_excel(output_excel_path, index=False, header=False)
print(f"处理完成，结果已保存至 {output_excel_path}")
# 可视化结果
plt.figure(figsize=(12, 9), dpi=200)
plt.subplot(1, 3, 1)
plt.title("Original Image")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.axis('off')
plt.subplot(1, 3, 2)
plt.title("Contrast Enhanced")
plt.imshow(enhanced_image, cmap='gray')
plt.axis('off')
plt.subplot(1, 3, 3)
plt.title("Contrast Enhanced")
plt.imshow(fixed_thresh, cmap='gray')
plt.axis('off')
plt.show()


