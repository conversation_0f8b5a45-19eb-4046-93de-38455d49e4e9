%!PS-Adobe-3.0 EPSF-3.0
%%Title: DQN_Shortest_Path.eps
%%Creator: Mat<PERSON>lotlib v3.7.5, https://matplotlib.org/
%%CreationDate: Sat Feb 24 16:10:17 2024
%%Orientation: portrait
%%BoundingBox: 75 223 537 569
%%HiResBoundingBox: 75.600000 223.200000 536.400000 568.800000
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Matplotlib.
10 dict begin
/FontName /DejaVuSans def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-2090 -948 3673 2524] def
/FontType 3 def
/Encoding [/space /parenleft /parenright /m /zero /one /two /four /six /eight /y /x] def
/CharStrings 13 dict dup begin
/.notdef 0 def
/space{651 0 0 0 0 0 sc
ce} _d
/parenleft{799 0 176 -270 635 1554 sc
635 1554 m
546 1401 479 1249 436 1099 c
393 949 371 797 371 643 c
371 489 393 336 436 185 c
480 34 546 -117 635 -270 c
475 -270 l
375 -113 300 41 250 192 c
201 343 176 494 176 643 c
176 792 201 941 250 1092 c
299 1243 374 1397 475 1554 c
635 1554 l

ce} _d
/parenright{799 0 164 -270 623 1554 sc
164 1554 m
324 1554 l
424 1397 499 1243 548 1092 c
598 941 623 792 623 643 c
623 494 598 343 548 192 c
499 41 424 -113 324 -270 c
164 -270 l
253 -117 319 34 362 185 c
406 336 428 489 428 643 c
428 797 406 949 362 1099 c
319 1249 253 1401 164 1554 c

ce} _d
/m{1995 0 186 0 1821 1147 sc
1065 905 m
1111 988 1166 1049 1230 1088 c
1294 1127 1369 1147 1456 1147 c
1573 1147 1663 1106 1726 1024 c
1789 943 1821 827 1821 676 c
1821 0 l
1636 0 l
1636 670 l
1636 777 1617 857 1579 909 c
1541 961 1483 987 1405 987 c
1310 987 1234 955 1179 892 c
1124 829 1096 742 1096 633 c
1096 0 l
911 0 l
911 670 l
911 778 892 858 854 909 c
816 961 757 987 678 987 c
584 987 509 955 454 891 c
399 828 371 742 371 633 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
413 1015 463 1065 522 1098 c
581 1131 650 1147 731 1147 c
812 1147 881 1126 938 1085 c
995 1044 1038 984 1065 905 c

ce} _d
/zero{1303 0 135 -29 1167 1520 sc
651 1360 m
547 1360 469 1309 416 1206 c
364 1104 338 950 338 745 c
338 540 364 387 416 284 c
469 182 547 131 651 131 c
756 131 834 182 886 284 c
939 387 965 540 965 745 c
965 950 939 1104 886 1206 c
834 1309 756 1360 651 1360 c

651 1520 m
818 1520 946 1454 1034 1321 c
1123 1189 1167 997 1167 745 c
1167 494 1123 302 1034 169 c
946 37 818 -29 651 -29 c
484 -29 356 37 267 169 c
179 302 135 494 135 745 c
135 997 179 1189 267 1321 c
356 1454 484 1520 651 1520 c

ce} _d
/one{1303 0 225 0 1114 1493 sc
254 170 m
584 170 l
584 1309 l
225 1237 l
225 1421 l
582 1493 l
784 1493 l
784 170 l
1114 170 l
1114 0 l
254 0 l
254 170 l

ce} _d
/two{1303 0 150 0 1098 1520 sc
393 170 m
1098 170 l
1098 0 l
150 0 l
150 170 l
227 249 331 356 463 489 c
596 623 679 709 713 748 c
778 821 823 882 848 932 c
874 983 887 1032 887 1081 c
887 1160 859 1225 803 1275 c
748 1325 675 1350 586 1350 c
523 1350 456 1339 385 1317 c
315 1295 240 1262 160 1217 c
160 1421 l
241 1454 317 1478 388 1495 c
459 1512 523 1520 582 1520 c
737 1520 860 1481 952 1404 c
1044 1327 1090 1223 1090 1094 c
1090 1033 1078 974 1055 919 c
1032 864 991 800 930 725 c
913 706 860 650 771 557 c
682 465 556 336 393 170 c

ce} _d
/four{1303 0 100 0 1188 1493 sc
774 1317 m
264 520 l
774 520 l
774 1317 l

721 1493 m
975 1493 l
975 520 l
1188 520 l
1188 352 l
975 352 l
975 0 l
774 0 l
774 352 l
100 352 l
100 547 l
721 1493 l

ce} _d
/six{1303 0 143 -29 1174 1520 sc
676 827 m
585 827 513 796 460 734 c
407 672 381 587 381 479 c
381 372 407 287 460 224 c
513 162 585 131 676 131 c
767 131 838 162 891 224 c
944 287 971 372 971 479 c
971 587 944 672 891 734 c
838 796 767 827 676 827 c

1077 1460 m
1077 1276 l
1026 1300 975 1318 923 1331 c
872 1344 821 1350 770 1350 c
637 1350 535 1305 464 1215 c
394 1125 354 989 344 807 c
383 865 433 909 492 940 c
551 971 617 987 688 987 c
838 987 956 941 1043 850 c
1130 759 1174 636 1174 479 c
1174 326 1129 203 1038 110 c
947 17 827 -29 676 -29 c
503 -29 371 37 280 169 c
189 302 143 494 143 745 c
143 981 199 1169 311 1309 c
423 1450 573 1520 762 1520 c
813 1520 864 1515 915 1505 c
967 1495 1021 1480 1077 1460 c

ce} _d
/eight{1303 0 139 -29 1163 1520 sc
651 709 m
555 709 479 683 424 632 c
369 581 342 510 342 420 c
342 330 369 259 424 208 c
479 157 555 131 651 131 c
747 131 823 157 878 208 c
933 260 961 331 961 420 c
961 510 933 581 878 632 c
823 683 748 709 651 709 c

449 795 m
362 816 295 857 246 916 c
198 975 174 1048 174 1133 c
174 1252 216 1347 301 1416 c
386 1485 503 1520 651 1520 c
800 1520 916 1485 1001 1416 c
1086 1347 1128 1252 1128 1133 c
1128 1048 1104 975 1055 916 c
1007 857 940 816 854 795 c
951 772 1027 728 1081 662 c
1136 596 1163 515 1163 420 c
1163 275 1119 164 1030 87 c
942 10 816 -29 651 -29 c
486 -29 360 10 271 87 c
183 164 139 275 139 420 c
139 515 166 596 221 662 c
276 728 352 772 449 795 c

375 1114 m
375 1037 399 976 447 933 c
496 890 564 868 651 868 c
738 868 805 890 854 933 c
903 976 928 1037 928 1114 c
928 1191 903 1252 854 1295 c
805 1338 738 1360 651 1360 c
564 1360 496 1338 447 1295 c
399 1252 375 1191 375 1114 c

ce} _d
/y{1212 0 61 -426 1151 1120 sc
659 -104 m
607 -237 556 -324 507 -365 c
458 -406 392 -426 309 -426 c
162 -426 l
162 -272 l
270 -272 l
321 -272 360 -260 388 -236 c
416 -212 447 -155 481 -66 c
514 18 l
61 1120 l
256 1120 l
606 244 l
956 1120 l
1151 1120 l
659 -104 l

ce} _d
/x{1212 0 59 0 1145 1120 sc
1124 1120 m
719 575 l
1145 0 l
928 0 l
602 440 l
276 0 l
59 0 l
494 586 l
96 1120 l
313 1120 l
610 721 l
907 1120 l
1124 1120 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
75.6 223.2 translate
460.8 345.6 0 0 clipbox
gsave
0 0 m
460.8 0 l
460.8 345.6 l
0 345.6 l
cl
1.000 setgray
fill
grestore
gsave
103.104 38.016 m
369.216 38.016 l
369.216 304.128 l
103.104 304.128 l
cl
1.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
129.7152 39.01392 m
147.34512 39.01392 l
146.3472 41.00976 l
156.3264 38.016 l
146.3472 35.02224 l
147.34512 37.01808 l
129.7152 37.01808 l
129.7152 39.01392 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
155.620764 38.721636 m
175.88124 58.982112 l
173.764332 59.687748 l
182.9376 64.6272 l
177.998148 55.453932 l
177.292512 57.57084 l
157.032036 37.310364 l
155.620764 38.721636 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
182.9376 65.62512 m
200.56752 65.62512 l
199.5696 67.62096 l
209.5488 64.6272 l
199.5696 61.63344 l
200.56752 63.62928 l
182.9376 63.62928 l
182.9376 65.62512 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
209.5488 65.62512 m
227.17872 65.62512 l
226.1808 67.62096 l
236.16 64.6272 l
226.1808 61.63344 l
227.17872 63.62928 l
209.5488 63.62928 l
209.5488 65.62512 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
235.454364 63.921564 m
215.193888 84.18204 l
214.488252 82.065132 l
209.5488 91.2384 l
218.722068 86.298948 l
216.60516 85.593312 l
236.865636 65.332836 l
235.454364 63.921564 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
208.843164 91.944036 m
229.10364 112.204512 l
226.986732 112.910148 l
236.16 117.8496 l
231.220548 108.676332 l
230.514912 110.79324 l
210.254436 90.532764 l
208.843164 91.944036 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
236.16 118.84752 m
253.78992 118.84752 l
252.792 120.84336 l
262.7712 117.8496 l
252.792 114.85584 l
253.78992 116.85168 l
236.16 116.85168 l
236.16 118.84752 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
262.7712 118.84752 m
280.40112 118.84752 l
279.4032 120.84336 l
289.3824 117.8496 l
279.4032 114.85584 l
280.40112 116.85168 l
262.7712 116.85168 l
262.7712 118.84752 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
288.676764 118.555236 m
308.93724 138.815712 l
306.820332 139.521348 l
315.9936 144.4608 l
311.054148 135.287532 l
310.348512 137.40444 l
290.088036 117.143964 l
288.676764 118.555236 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
314.99568 144.4608 m
314.99568 162.09072 l
312.99984 161.0928 l
315.9936 171.072 l
318.98736 161.0928 l
316.99152 162.09072 l
316.99152 144.4608 l
314.99568 144.4608 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
314.99568 171.072 m
314.99568 188.70192 l
312.99984 187.704 l
315.9936 197.6832 l
318.98736 187.704 l
316.99152 188.70192 l
316.99152 171.072 l
314.99568 171.072 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
315.287964 198.388836 m
335.54844 218.649312 l
333.431532 219.354948 l
342.6048 224.2944 l
337.665348 215.121132 l
336.959712 217.23804 l
316.699236 196.977564 l
315.287964 198.388836 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
341.899164 225.000036 m
362.15964 245.260512 l
360.042732 245.966148 l
369.216 250.9056 l
364.276548 241.732332 l
363.570912 243.84924 l
343.310436 223.588764 l
341.899164 225.000036 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
368.510364 250.199964 m
348.249888 270.46044 l
347.544252 268.343532 l
342.6048 277.5168 l
351.778068 272.577348 l
349.66116 271.871712 l
369.921636 251.611236 l
368.510364 250.199964 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
341.60688 277.5168 m
341.60688 295.14672 l
339.61104 294.1488 l
342.6048 304.128 l
345.59856 294.1488 l
343.60272 295.14672 l
343.60272 277.5168 l
341.60688 277.5168 l
0.000 setgray
fill
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
343.60272 304.128 m
343.10376 304.992224 l
342.10584 304.992224 l
341.60688 304.128 l
342.10584 303.263776 l
343.10376 303.263776 l
343.60272 304.128 l
343.10376 304.992224 l
0.000 setgray
fill
grestore
1.000 setlinewidth
0 setlinejoin
0 setlinecap
[] 0 setdash
0.000 0.000 1.000 setrgbcolor
gsave
266.112 266.112 103.104 38.016 clipbox
169.632 157.7664 m
196.2432 157.7664 l
196.2432 184.3776 l
169.632 184.3776 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
169.632 131.1552 m
196.2432 131.1552 l
196.2432 157.7664 l
169.632 157.7664 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
169.632 104.544 m
196.2432 104.544 l
196.2432 131.1552 l
169.632 131.1552 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
169.632 77.9328 m
196.2432 77.9328 l
196.2432 104.544 l
169.632 104.544 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
169.632 51.3216 m
196.2432 51.3216 l
196.2432 77.9328 l
169.632 77.9328 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
169.632 24.7104 m
196.2432 24.7104 l
196.2432 51.3216 l
169.632 51.3216 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
276.0768 290.8224 m
302.688 290.8224 l
302.688 317.4336 l
276.0768 317.4336 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
276.0768 264.2112 m
302.688 264.2112 l
302.688 290.8224 l
276.0768 290.8224 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
276.0768 237.6 m
302.688 237.6 l
302.688 264.2112 l
276.0768 264.2112 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
276.0768 210.9888 m
302.688 210.9888 l
302.688 237.6 l
276.0768 237.6 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
276.0768 184.3776 m
302.688 184.3776 l
302.688 210.9888 l
276.0768 210.9888 l
cl
gsave
fill
grestore
stroke
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
276.0768 157.7664 m
302.688 157.7664 l
302.688 184.3776 l
276.0768 184.3776 l
cl
gsave
fill
grestore
stroke
grestore
1 setlinejoin
0.000 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
1.000 0.000 0.000 setrgbcolor
fill
grestore
stroke
grestore
} bind def
129.715 38.016 o
grestore
gsave
266.112 266.112 103.104 38.016 clipbox
/o {
gsave
newpath
translate
1.0 setlinewidth
1 setlinejoin

0 setlinecap

-5 -5 m
5 -5 l
5 5 l
-5 5 l
cl

gsave
1.000 0.000 0.000 setrgbcolor
fill
grestore
stroke
grestore
} bind def
342.605 304.128 o
grestore
0.800 setlinewidth
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
103.104 38.016 m
103.104 304.128 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.104 38.016 o
grestore
/DejaVuSans 12.000 selectfont
gsave

99.2837 21.891 translate
0 rotate
0 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
156.3264 38.016 m
156.3264 304.128 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
156.326 38.016 o
grestore
/DejaVuSans 12.000 selectfont
gsave

148.686 21.891 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
209.5488 38.016 m
209.5488 304.128 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
209.549 38.016 o
grestore
/DejaVuSans 12.000 selectfont
gsave

201.908 21.891 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
262.7712 38.016 m
262.7712 304.128 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
262.771 38.016 o
grestore
/DejaVuSans 12.000 selectfont
gsave

255.131 21.891 translate
0 rotate
0 0 m /six glyphshow
7.63477 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
315.9936 38.016 m
315.9936 304.128 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
315.994 38.016 o
grestore
/DejaVuSans 12.000 selectfont
gsave

308.353 21.891 translate
0 rotate
0 0 m /eight glyphshow
7.63477 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
369.216 38.016 m
369.216 304.128 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
369.216 38.016 o
grestore
/DejaVuSans 12.000 selectfont
gsave

357.755 21.891 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

217.504 4.75037 translate
0 rotate
0 0 m /x glyphshow
8.28516 0 m /space glyphshow
12.7354 0 m /parenleft glyphshow
18.1973 0 m /m glyphshow
31.835 0 m /parenright glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
103.104 38.016 m
369.216 38.016 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.104 38.016 o
grestore
/DejaVuSans 12.000 selectfont
gsave

88.4634 33.4535 translate
0 rotate
0 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
103.104 91.2384 m
369.216 91.2384 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.104 91.2384 o
grestore
/DejaVuSans 12.000 selectfont
gsave

80.8228 86.6759 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
103.104 144.4608 m
369.216 144.4608 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.104 144.461 o
grestore
/DejaVuSans 12.000 selectfont
gsave

80.8228 139.898 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
103.104 197.6832 m
369.216 197.6832 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.104 197.683 o
grestore
/DejaVuSans 12.000 selectfont
gsave

80.8228 193.121 translate
0 rotate
0 0 m /six glyphshow
7.63477 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
103.104 250.9056 m
369.216 250.9056 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.104 250.906 o
grestore
/DejaVuSans 12.000 selectfont
gsave

80.8228 246.343 translate
0 rotate
0 0 m /eight glyphshow
7.63477 0 m /zero glyphshow
grestore
[0.8 1.32] 0 setdash
0.690 setgray
gsave
266.112 266.112 103.104 38.016 clipbox
103.104 304.128 m
369.216 304.128 l
stroke
grestore
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
103.104 304.128 o
grestore
/DejaVuSans 12.000 selectfont
gsave

73.1821 299.565 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

66.2759 152.416 translate
90 rotate
0 0 m /y glyphshow
8.28516 0 m /space glyphshow
12.7354 0 m /parenleft glyphshow
18.1973 0 m /m glyphshow
31.835 0 m /parenright glyphshow
grestore
0 setlinejoin
2 setlinecap
gsave
103.104 38.016 m
103.104 304.128 l
stroke
grestore
gsave
369.216 38.016 m
369.216 304.128 l
stroke
grestore
gsave
103.104 38.016 m
369.216 38.016 l
stroke
grestore
gsave
103.104 304.128 m
369.216 304.128 l
stroke
grestore

end
showpage
