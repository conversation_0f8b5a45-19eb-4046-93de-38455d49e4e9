import cv2
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from skimage.segmentation import random_walker
from skimage.filters import gaussian, threshold_otsu,difference_of_gaussians
from skimage.measure import label, regionprops_table
import pandas as pd
from skimage.measure import regionprops
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler
from scipy.ndimage import binary_dilation
import matplotlib.pyplot as plt
import math
import numpy as np
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage.filters import gaussian
from scipy.ndimage import binary_dilation
from skimage.feature import canny
from skimage.filters import sobel
from scipy.ndimage import gaussian_filter
import os
# ===================== Step 0: 读取图像 =====================
# 读取输入图像
image_path = "C:/Users/<USER>/Desktop/ICE/sea_ice/sea_ice_647.jpg"
# 检查图像路径是否存在
if not os.path.exists(image_path):
    raise FileNotFoundError(f"图像路径 '{image_path}' 不存在，请检查路径是否正确！")
# 读取输入图像
image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
# 检查图像是否成功读取
if image is None:
    raise ValueError(f"无法读取图像 '{image_path}'，请检查文件是否损坏或格式是否支持！")
######=====================A.图像预处理 =====================####### 
# ===================== Step 1: 转为灰度图像 =====================
# 转换为灰度图像，简化计算，突出亮度差异，适用于大多数分割算法
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
# ===================== Step 2: 图像平滑处理（滤波降噪） =====================
# 高斯滤波平滑图像，减少高频噪声，保持边缘信息
#smoothed_image = gaussian(gray_image, sigma=0.4)
# 自适应非局部均值滤波（Non-Local Means Denoising, NL-Means）
smoothed_image = cv2.fastNlMeansDenoising(gray_image, h=10, templateWindowSize=5, searchWindowSize=20)
# 中值滤波进一步去除椒盐噪声
#smoothed_image = cv2.medianBlur((gray_image * 255).astype(np.uint8), 5)
# ===================== Step 2: 减少反光 =====================
#光照反光抑制与对比度增强：  为了减轻反光干扰，并增强冰水之间的视觉差异
#同态滤波 (Homomorphic Filtering)： 可以将图像的照度分量和反射分量分离，
#减弱照度分量的影响，从而减少光照不均匀和反光现象。
def homomorphic_filter(img, gamma_h=1.3, gamma_l=0.5, d0=10, c=10):
    """
    同态滤波器
    Args:
        img: 灰度图像
        gamma_h: 高频增益
        gamma_l: 低频增益
        d0: 截止频率
        c: 锐化系数
    Returns:
        滤波后的图像
    """
    rows, cols = img.shape
    img_log = np.log1p(np.array(img, dtype="float") / 255) # 转换为对数域
    # 构建滤波器
    H = np.zeros((rows, cols), np.float32)
    for i in range(rows):
        for j in range(cols):
            H[i, j] = (gamma_h - gamma_l) * (1 - np.exp(-c * ((i**2 + j**2) / d0**2))) + gamma_l
    # 频域变换
    img_fft = np.fft.fft2(img_log)
    img_fft_shifted = np.fft.fftshift(img_fft)
    # 滤波
    img_fft_filtered = img_fft_shifted * H
    img_fft_inverse_shifted = np.fft.ifftshift(img_fft_filtered)
    img_filtered = np.fft.ifft2(img_fft_inverse_shifted)
    # 指数变换，恢复到图像灰度范围
    img_exp = np.expm1(np.real(img_filtered))
    img_output = np.array(np.clip(img_exp * 255 + 0.5, 0, 255), dtype=np.uint8)
    return img_output
homo_image = homomorphic_filter(smoothed_image) # 对增强对比度后的图像进行同态滤波
# ===================== Step 3: 增强对比度 =====================
# 使用直方图均衡化增强对比度，突出图像细节
#enhanced_image = cv2.equalizeHist(homo_image)
# 自适应直方图均衡化（CLAHE），避免过度增强，适用于局部对比度提升
clahe = cv2.createCLAHE(clipLimit=1, tileGridSize=(10, 10))
enhanced_image = clahe.apply(homo_image)
# ===================== Step 4: 边缘增强与纹理特征提取 =====================
# Canny 边缘检测，提取海冰与水体的边缘特征
#edges = cv2.Canny(enhanced_image, threshold1=50, threshold2=150)
# 使用拉普拉斯滤波器进一步增强边缘，突出细节变化
# laplacian_edges = cv2.Laplacian(enhanced_image, cv2.CV_64F)
# ===================== Step 5: 可视化结果 =====================
# 显示原始图像和处理后的图像
plt.figure(figsize=(8, 8),dpi=1000)
plt.subplot(2, 2, 1)
#plt.title("Original Image")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.axis('off')
plt.subplot(2, 2, 2)
#plt.title("Grayscale Image")
plt.imshow(gray_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 2, 3)
#plt.title("Smoothed Image")
plt.imshow(smoothed_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 2, 4)
#plt.title("homo_image")
plt.imshow(homo_image, cmap='gray')
plt.axis('off')
# plt.subplot(2, 3, 5)
# plt.title("Contrast Enhanced")
# plt.imshow(enhanced_image, cmap='gray')
# plt.axis('off')
# plt.subplot(2, 3, 6)
# plt.title("Canny Edges")
# plt.imshow(edges, cmap='gray')
# plt.axis('off')
# plt.tight_layout()
plt.show()

# ===================== 3D 可视化灰度图像 =====================
h, w = gray_image.shape
x = np.linspace(0, w, w)
y = np.linspace(0, h, h)
x, y = np.meshgrid(x, y)  # 生成网格坐标
z = gray_image  # 将灰度值作为 z 高度
z = enhanced_image  # 将灰度值作为 z 高度
# 创建 3D 绘图
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')
# 绘制曲面
surface = ax.plot_surface(x, y, z, cmap="gray", edgecolor='none')
# 添加颜色条
fig.colorbar(surface, ax=ax, shrink=0.5, aspect=10)
# 设置标题和轴标签
ax.set_title("3D Visualization of Grayscale Image")
ax.set_xlabel("X-axis")
ax.set_ylabel("Y-axis")
ax.set_zlabel("Pixel Intensity")
plt.show()

######=====================B.图像分割 =====================####### 
image_seg =enhanced_image
# ===================== 方法 1: 应用 Otsu 阈值分割 =====================
# 使用 Otsu 方法自动确定阈值进行二值化
_, otsu_thresh = cv2.threshold((image_seg * 255).astype(np.uint8), 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
#===================== 方法 2: K-Means 聚类分割=====================
pixel_values = image_seg.reshape((-1, 1))
pixel_values = np.float32(pixel_values)  # 转为浮点型，便于 K-Means 处理
# 定义 K-Means 聚类参数
criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 100, 0.2)  # 迭代停止条件
k = 2  # 目标分为两类（海冰和水）
_, labels, centers = cv2.kmeans(pixel_values, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
# 将聚类结果映射回图像形状
segmented_image_kmeans = labels.reshape(image_seg.shape)

# ===================== 方法 3: 阈值分割（固定阈值） =====================
#_, fixed_thresh = cv2.threshold(image_seg, 127, 255, cv2.THRESH_BINARY)
# ===================== 方法 3: 自适应均值 =====================
# fixed_thresh = cv2.adaptiveThreshold(image_seg, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
#                                    cv2.THRESH_BINARY, 11, 2)
# ===================== 方法 4: 随机游走方法 =====================
# 初始化标记矩阵，0 表示未标记的像素
markers = np.zeros_like(image_seg, dtype=np.int32)
# 手动设置前景（海冰）和背景（水）的标记
markers[image_seg < 100] = 1  # 背景标记
markers[image_seg > 150] = 2  # 前景标记
# 使用随机游走算法对图像进行分割
labels_rw = random_walker(smoothed_image, markers, beta=10, mode='bf')
# ===================== 方法 5: 自适应相场 =====================
epsilon=0.05
mu=1.0 
lambda_=1.5 
dt=0.01 
n_iters=1000
use_adaptive_weights=True
use_edge_preservation=True
use_texture_feature=True

# 归一化图像到[0,1]
img = homo_image / 255.0

# 计算边缘特征 - 用于自适应权重和边缘保持正则化项
edge_map = None
texture_feature = None
if use_adaptive_weights or use_edge_preservation:
    # 使用Sobel算子计算梯度幅值作为边缘图
    edge_map = sobel(img)
    edge_map = edge_map / np.max(edge_map)  # 归一化到[0,1]

# 提取纹理特征 - 用于增强分割
if use_texture_feature:
    # 使用不同尺度的高斯滤波器提取纹理特征
    texture_feature = np.zeros_like(img)
    for sigma in [0.5, 1.0, 2.0]:
        texture_feature += np.abs(img - gaussian_filter(img, sigma))
    texture_feature = texture_feature / np.max(texture_feature)  # 归一化

# 初始化相场函数（使用Otsu阈值进行初始化）
_, init_phi = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
phi = init_phi.astype(float) / 255.0

# 式场演化
for i in range(n_iters):
    # 计算梯度
    grad_phi_x = np.gradient(phi, axis=1)
    grad_phi_y = np.gradient(phi, axis=0)
    grad_phi_norm = np.sqrt(grad_phi_x**2 + grad_phi_y**2)
    
    # 计算曲率
    laplacian = cv2.Laplacian(phi, cv2.CV_64F)
    
    # 自适应权重系数 - 创新点1：根据图像局部特征动态调整权重
    lambda_adaptive = lambda_
    mu_adaptive = mu
    epsilon_adaptive = epsilon
    
    if use_adaptive_weights and edge_map is not None:
        # 在边缘区域减小扩散系数，增大图像驱动力
        edge_weight = 1.0 - 0.7 * edge_map  # 边缘处权重较小
        lambda_adaptive = lambda_ * (1.0 + edge_map)  # 边缘处增大图像驱动力
        mu_adaptive = mu * edge_weight  # 边缘处减小扩散系数
        epsilon_adaptive = epsilon * edge_weight  # 边缘处减小界面宽度
    
    # 计算图像驱动项
    image_force = lambda_adaptive * (img - 0.5)
    
    # 如果使用纹理特征，将其融入图像驱动项
    if use_texture_feature and texture_feature is not None:
        image_force += 0.3 * lambda_adaptive * texture_feature
    
    # 创新点2：边缘保持正则化项
    edge_preservation_term = 0
    if use_edge_preservation and edge_map is not None:
        # 在边缘处增加额外的正则化项，保持边缘的几何特征
        edge_preservation_term = 0.2 * edge_map * grad_phi_norm
    
    # 更新相场函数
    dphi = mu_adaptive * (epsilon_adaptive * laplacian - (1/epsilon_adaptive) * phi * (1-phi) * (1-2*phi)) + image_force + edge_preservation_term
    phi = phi + dt * dphi
    
    # 限制phi在[0,1]范围内
    phi = np.clip(phi, 0, 1)

# 二值化得到最终分割结果
apsis_segmented = (phi > 0.15).astype(np.uint8) * 255
# ===================== 最终可视化对比 =====================
plt.figure(figsize=(20, 15),dpi=800)

# 原始图像
plt.subplot(2, 3, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
#plt.title("Original Image")
plt.axis("off")

# 读取 Excel 文件
file_path = 'label/sea_ice_647.xlsx'
data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
# 将数据转换为 NumPy 数组
numpy_array = data.to_numpy()

# 固定阈值分割
plt.subplot(2, 3, 2)
plt.imshow(numpy_array, cmap="gray")
#plt.title("Fixed Threshold")
plt.axis("off")

# Otsu 阈值分割
plt.subplot(2, 3, 3)
plt.imshow(otsu_thresh, cmap="gray")
#plt.title("Otsu Thresholding")
plt.axis("off")

# Gabor 滤波器 + K-Means 分割
plt.subplot(2, 3, 4)
plt.imshow(segmented_image_kmeans, cmap="gray")
#plt.title("Gabor + K-Means")
plt.axis("off")

# 随机游走分割
plt.subplot(2, 3, 5)
plt.imshow(labels_rw, cmap="gray_r")
#plt.title("Random Walker")
plt.axis("off")

# 随机游走分割
plt.subplot(2, 3, 6)
plt.imshow(apsis_segmented, cmap="gray_r")
#plt.title("APSIS")
plt.axis("off")
plt.tight_layout()
plt.show()

# ===================== 可视化随机游走分割结果 =====================
# plt.figure(figsize=(15, 10))
# # 原始灰度图像
# plt.subplot(1, 3, 1)
# plt.imshow(gray_image, cmap='gray')
# plt.title("Original Grayscale Image")
# plt.axis("off")
# # 标记结果
# plt.subplot(1, 3, 2)
# plt.imshow(markers, cmap='jet')
# plt.title("Markers")
# plt.axis("off")
# # 随机游走分割结果
# plt.subplot(1, 3, 3)
# plt.imshow(labels_rw, cmap='gray')
# plt.title("Segmented Image (Random Walker)")
# plt.axis("off")
# plt.tight_layout()
# plt.show()

# ===================== 评价指标获取 =====================
import numpy as np
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

# 假设 y_true 和 y_pred 是 256x256 的矩阵
y_true =np.where(numpy_array == 255, 1, numpy_array)
y_pred = np.where(otsu_thresh == 255, 1, otsu_thresh)
#y_pred = segmented_image_kmeans
# # 图像二值化反转（将黑白颜色反转）
# y_pred = np.where(y_pred == 0, 1, np.where(y_pred == 1, 0, y_pred))
# y_pred = y_pred

# 将矩阵展平为一维数组
y_true_flat = y_true.flatten()
y_pred_flat = y_pred.flatten()

# 计算 Accuracy
accuracy = accuracy_score(y_true_flat, y_pred_flat)

# 计算 Precision
precision = precision_score(y_true_flat, y_pred_flat)

# 计算 Recall
recall = recall_score(y_true_flat, y_pred_flat)

# 计算 F1-score
f1 = f1_score(y_true_flat, y_pred_flat)

print(f"Accuracy: {accuracy}")
print(f"Precision: {precision}")
print(f"Recall: {recall}")
print(f"F1-score: {f1}")

# =====================高级统计分析功能=====================
from scipy import ndimage
import matplotlib.patches as patches
import time
# 读取 Excel 文件
file_path = 'label/sea_ice_171.xlsx'
data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
# 将数据转换为 NumPy 数组
numpy_array = data.to_numpy()
plt.imshow(numpy_array, cmap='gray')
plt.title("Original Grayscale Image")
plt.axis("off")

#0.选择图片分割后的结果
#ice_image=fixed_thresh
# 图像二值化反转（将黑白颜色反转）
numpy_array = np.where(numpy_array == 0, 255, np.where(numpy_array == 255, 0, numpy_array))
ice_image = numpy_array

# 1. 海冰连通区域识别与可视化
#使用分割得到的标签图，每个连通区域可以视为一块独立的海冰。
# 识别连通区域,进行单一的颜色识别
labeled_ice, num_features = ndimage.label(ice_image)
print(f"共识别出 {num_features} 块独立海冰")

# 可视化每块海冰(用颜色区分)
plt.figure(figsize=(10, 8))
plt.imshow(labeled_ice, cmap='nipy_spectral')
plt.title(f"海冰块识别结果: {num_features}块", fontsize=14)
plt.axis("off")
plt.colorbar(label='海冰块编号')
plt.show()

# 可视化每块海冰(用方框区分)
fig, ax = plt.subplots(figsize=(10, 8))
ax.imshow(labeled_ice, cmap='nipy_spectral')
ax.set_title(f"labeled_ice: {num_features}块", fontsize=14)
ax.axis("off")

# 识别每个连通区域的边界框
objects = ndimage.find_objects(labeled_ice)
# 绘制红色方框标记每块海冰
for i, obj_slice in enumerate(objects, 1):
    if obj_slice is not None:
        y_start, y_stop = obj_slice[0].start, obj_slice[0].stop
        x_start, x_stop = obj_slice[1].start, obj_slice[1].stop
        width = x_stop - x_start
        height = y_stop - y_start
        # 添加红色方框
        rect = patches.Rectangle((x_start, y_start), width, height, 
                                linewidth=2, edgecolor='red', facecolor='none')
        ax.add_patch(rect)
        # 添加编号标签
        ax.text(x_start, y_start-5, f"{i}", color='white', fontweight='bold', 
                backgroundcolor='red', fontsize=8)
plt.show()

#1.1 海冰的膨胀安全距离
#定义膨胀操作以模拟 "充气" 海冰，保证安全距离
safety_margin = 1  # 安全边距（可根据实际需求调整）
# 膨胀操作
start_time = time.time()
inflated_ice = binary_dilation(ice_image, iterations=safety_margin)
print(f"膨胀操作耗时: {time.time() - start_time:.4f}秒")

#创建加权图像，初步为空白区域（0值）和冰厚区域（根据 gray_image 加权）
weighted_image = np.copy(ice_image)
#设置膨胀区域为障碍物，其他空白区域根据冰厚值加权
weighted_image[inflated_ice == 1] = 1  # 赋予1（表示海冰区域）
weighted_image[inflated_ice == 0] = 0  # 赋予0（表示非海冰区域）

# 可视化膨胀后的海冰区域
plt.figure(figsize=(8, 6))
plt.imshow(weighted_image, cmap='jet', origin='upper')
plt.title("膨胀后的海冰区域（安全距离考虑）", fontsize=14)
plt.colorbar(label='海冰标记')
plt.axis('off')
plt.show()

#1.2 海冰的膨胀后的栅格定义
# 连通区域标记
labeled_inflated, num_inflated = ndimage.label(weighted_image)
print(f"膨胀后共有 {num_inflated} 块连通区域")

# 方案1：矩形包围框及掩膜处理
rect_mask = np.zeros_like(ice_image)
fig, ax = plt.subplots(figsize=(10, 8))
ax.imshow(ice_image, cmap='gray')
ax.set_title(f"矩形安全边界框: {num_inflated}块", fontsize=14)
ax.axis("off")

objects_inflated = ndimage.find_objects(labeled_inflated)
# 存储所有矩形区域的信息，用于后续分析
rect_info = []
for i, obj_slice in enumerate(objects_inflated, 1):
    if obj_slice:
        y_start, y_stop = obj_slice[0].start, obj_slice[0].stop
        x_start, x_stop = obj_slice[1].start, obj_slice[1].stop
        width = x_stop - x_start
        height = y_stop - y_start
        rect = patches.Rectangle((x_start, y_start), 
                                width, height,
                                linewidth=1, edgecolor='cyan', 
                                facecolor='none', linestyle='--')
        ax.add_patch(rect)
        # 添加编号标签
        ax.text(x_start, y_start-5, f"{i}", color='cyan', fontweight='bold', fontsize=8)
        # 赋值为1（创建矩形掩膜）
        rect_mask[y_start:y_stop, x_start:x_stop] = 1
        # 存储矩形信息
        rect_info.append({
            'id': i,
            'x_start': x_start,
            'y_start': y_start,
            'width': width,
            'height': height,
            'area': width * height
        })
plt.show()

# 掩膜可视化
plt.figure(figsize=(8, 6))
plt.imshow(rect_mask, cmap='gray', origin='upper')
plt.title("矩形安全区域掩膜", fontsize=14)
plt.axis('off')
plt.show()

# # 方案2：圆形包围框及掩膜处理 - 优化版本
# circle_mask = np.zeros_like(ice_image)
# fig, ax = plt.subplots(figsize=(10, 8))
# ax.imshow(ice_image, cmap='gray')
# ax.set_title(f"圆形安全边界: {num_inflated}块", fontsize=14)
# ax.axis("off")

# # 存储所有圆形区域的信息，用于后续分析
# circle_info = []
# start_time = time.time()

# # 创建坐标网格，避免循环计算
# y_coords, x_coords = np.ogrid[:ice_image.shape[0], :ice_image.shape[1]]

# for label in range(1, num_inflated + 1):
#     mask = labeled_inflated == label
#     coords = np.argwhere(mask)
#     if len(coords) == 0:
#         continue
        
#     y_center, x_center = coords.mean(axis=0)
#     distances = np.sqrt((coords[:,0] - y_center)**2 + (coords[:,1] - x_center)**2)
#     radius = np.max(distances)
    
#     circle = patches.Circle((x_center, y_center), radius,
#                            linewidth=1, edgecolor='magenta',
#                            facecolor='none', linestyle='--')
#     ax.add_patch(circle)
#     # 添加编号标签
#     ax.text(x_center, y_center-radius-5, f"{label}", color='magenta', 
#             fontweight='bold', fontsize=8)
    
#     # 使用向量化操作计算圆形掩膜
#     dist_from_center = np.sqrt((y_coords - y_center)**2 + (x_coords - x_center)**2)
#     circle_mask[dist_from_center <= radius] = 1
    
#     # 存储圆形信息
#     circle_info.append({
#         'id': label,
#         'center_x': x_center,
#         'center_y': y_center,
#         'radius': radius,
#         'area': np.pi * radius**2
#     })

# print(f"圆形掩膜计算耗时: {time.time() - start_time:.4f}秒")
# plt.show()

# # 掩膜可视化
# plt.figure(figsize=(8, 6))
# plt.imshow(circle_mask, cmap='gray', origin='upper')
# plt.title("圆形安全区域掩膜", fontsize=14)
# plt.axis('off')
# plt.show()

# # 比较两种掩膜的覆盖区域差异
# plt.figure(figsize=(10, 8))
# diff_mask = circle_mask - rect_mask
# plt.imshow(diff_mask, cmap='bwr')
# plt.title("圆形vs矩形掩膜差异 (红色:圆形独有, 蓝色:矩形独有)", fontsize=14)
# plt.colorbar(label='差异')
# plt.axis('off')
# plt.show()

#2.1 海冰厚度的估计（基于灰度特征）
#厚度估算改进： 如果有真实厚度数据，可以通过回归模型建立更精准的灰度-厚度映射。
#假设灰度值与厚度呈一定的物理关系，可以通过以下方式估算：
print("开始估算海冰厚度...")
# 假设 gray_image 是你的冰厚图像
scaler = MinMaxScaler(feature_range=(0, 1))  # 可以调整范围，例如(0,5)表示0-5米厚度
# 将 gray_image 扁平化为一维数组，MinMaxScaler 要求二维数据
gray_image_flattened = gray_image.flatten().reshape(-1, 1)
# 对数据进行归一化
normalized_gray_image_flattened = scaler.fit_transform(gray_image_flattened)
# 将归一化后的图像重新调整为原来的形状
normalized_image = normalized_gray_image_flattened.reshape(ice_image.shape)
thickness_image = normalized_image  # 冰厚结果
# 可视化厚度分布
plt.figure(figsize=(8, 6))
plt.imshow(thickness_image, cmap='jet', origin='upper')
plt.title("海冰厚度估计分布", fontsize=14)
plt.colorbar(label='估计厚度 (归一化单位)')
plt.axis('off')
plt.show()

# 计算每块海冰的平均灰度值（作为厚度的近似）
thickness_list = ndimage.mean(thickness_image, labels=labeled_ice, index=range(1, num_features + 1))
# 输出厚度信息（仅显示前10块和统计信息）
print(f"海冰厚度统计 (共{len(thickness_list)}块):")
for i, thickness in enumerate(thickness_list[:10], 1):
    print(f"海冰块 {i}: 估计厚度 = {thickness:.4f}")
if len(thickness_list) > 10:
    print("...")
print(f"平均厚度: {np.mean(thickness_list):.4f}")
print(f"最大厚度: {np.max(thickness_list):.4f}")
print(f"最小厚度: {np.min(thickness_list):.4f}")
    
#2.2 图片的冰区覆盖率
ice_area = np.sum(ice_image > 0)  # 冰层区域的面积
total_area = ice_image.size  # 图像的总面积
coverage = (ice_area / total_area) * 100  # 覆盖率（百分比）
print(f"海冰覆盖率: {coverage:.2f}%")

# 可视化覆盖率
plt.figure(figsize=(6, 6))
labels = ['ICE', 'Water']
sizes = [ice_area, total_area - ice_area]
colors = ['lightblue', 'lightgray']
plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
plt.axis('equal')
plt.title('%', fontsize=14)
plt.show()

#2.3 冰层覆盖率图片的冰区密集度
print("计算海冰密集度分布...")
# 网格参数
grid_size = 20  # 可调整网格大小以改变分析精度
grid_rows = ice_image.shape[0] // grid_size
grid_cols = ice_image.shape[1] // grid_size
# 计算密度图
density_map = np.zeros((grid_rows, grid_cols))
for row in range(grid_rows):
    for col in range(grid_cols):
        grid_area = ice_image[row * grid_size:(row + 1) * grid_size, col * grid_size:(col + 1) * grid_size]
        ice_in_grid = np.sum(grid_area > 0)
        total_grid_area = grid_area.size
        density_map[row, col] = (ice_in_grid / total_grid_area) * 100  # 覆盖率百分比

# 可视化冰层分布密度
plt.figure(figsize=(10, 8))
im = plt.imshow(density_map, cmap='jet', interpolation='nearest')
plt.colorbar(im, label='海冰覆盖率 (%)')
plt.title('海冰分布密集度 (网格分析)', fontsize=14)
plt.xlabel(f'网格列 (每格 {grid_size} 像素)')
plt.ylabel(f'网格行 (每格 {grid_size} 像素)')
plt.show()

# 修正插值部分
# 计算正确的缩放因子
zoom_factor_row = ice_image.shape[0] / density_map.shape[0]
zoom_factor_col = ice_image.shape[1] / density_map.shape[1]
# 使用双线性插值（order=1）
density_map_resized = zoom(density_map, (zoom_factor_row, zoom_factor_col), order=1)
# 检查新数组的形状
print("新网格大小：", density_map_resized.shape)
# 可视化原始与插值后的网格
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.imshow(density_map, cmap='viridis', interpolation='none')
plt.title("Mesh1")
plt.colorbar()
plt.subplot(1, 2, 2)
plt.imshow(density_map_resized, cmap='viridis', interpolation='none')
plt.title("Mesh2")
plt.colorbar()
plt.show()

# 3. 海冰大小的统计（面积和形状特征）
# 可以通过计算每块海冰的像素数量来估算面积：
# 计算每块海冰的面积（像素数）
area_list = ndimage.sum(np.ones_like(gray_image), labels=labeled_ice, index=range(1, num_features + 1))

# 输出面积信息
for i, area in enumerate(area_list, 1):
    print(f"Sea Ice Block {i}: Area = {area} pixels")
# 计算形状特征
props = regionprops(labeled_ice, intensity_image=gray_image)
for i, prop in enumerate(props, 1):
    print(f"Sea Ice Block {i}: Area = {prop.area}, Perimeter = {prop.perimeter}, Eccentricity = {prop.eccentricity}")

# 厚度分布
plt.figure(figsize=(8, 4))
sns.histplot(thickness_list, kde=True)
plt.title("Distribution of Sea Ice Thickness")
plt.xlabel("Estimated Thickness")
plt.ylabel("Frequency")
plt.show()

# 面积分布
plt.figure(figsize=(8, 4))
sns.histplot(area_list, kde=True, color="orange")
plt.title("Distribution of Sea Ice Area")
plt.xlabel("Area (pixels)")
plt.ylabel("Frequency")
plt.show()

# 4. 单一海冰的特征
#关联labeled_ice和厚度，形状特征，可以选择绘制某个海冰到一个图中，并展示相关信息进行绘制
# 假设你已经有了 labeled_ice 和 thickness_list、props 等信息
# 选择你想要显示的海冰块，例如选择海冰块1
target_block = 20
# 创建一个掩膜，仅保留目标海冰块
target_mask = labeled_ice == target_block
# 可视化该目标海冰块（白色）
plt.figure(figsize=(10, 8))
plt.imshow(target_mask, cmap='gray')
plt.title(f"Sea Ice Block {target_block}: Thickness = {thickness_list[target_block - 1]:.2f}")
plt.axis("off")
plt.show()
# 输出该目标海冰块的相关形状特征
target_prop = props[target_block - 1]
print(f"Sea Ice Block {target_block}:")
print(f"Estimated Thickness = {thickness_list[target_block - 1]:.2f}")
print(f"Area = {target_prop.area} pixels")
print(f"Perimeter = {target_prop.perimeter:.2f}")
print(f"Eccentricity = {target_prop.eccentricity:.2f}")

# ===================== 路径规划功能 =====================
# =============================================================================
# 分类 A: 基于图的搜索算法 (Graph-Based Search Algorithms)
# =============================================================================
# 1. Dijkstra 算法（DijkstraClassic）
# 描述：
#   - 基于广度优先搜索，逐步扩展节点，计算起点到所有节点的最短路径。
# 优点：
#   - 全局最优：在边权非负的图中保证找到最优路径。
#   - 完整性：只要存在一条可达路径，算法必能找到。
# 缺点：
#   - 计算量大：在大规模图或高分辨率网格中，搜索节点数激增，计算时间较长。
#   - 无启发信息：对目标信息没有利用，搜索效率相对较低。
from DijkstraClassic import DijkstraClassic
# 2. A* 算法（AstarClassic）
# 描述：
#   - 在 Dijkstra 的基础上引入启发式函数 h(n)，使搜索过程朝向目标方向引导。
# 优点：
#   - 高效性：合理的启发函数可大幅缩小搜索空间，提高搜索速度。
#   - 最优性和完整性：当启发函数满足一致性条件时，能保证全局最优解。
# 缺点：
#   - 启发函数依赖：算法性能受启发函数设计影响较大，不当设计可能影响效果。
#   - 内存消耗大：需保存开放列表和闭合列表，处理高分辨率问题时内存开销较高。
from AstarClassic import AstarClassic
# 3. 最佳优先搜索（Best-First Search）（Best_FirstClassic）
# 描述：
#   - 利用启发函数评估节点的重要性，优先扩展最有希望到达目标的节点。
# 优点：
#   - 搜索速度快：若启发函数设计合理，能迅速指引搜索方向。
# 缺点：
#   - 最优性不保证：可能因启发函数的局部最优性而错失全局最优路径。
#   - 强依赖启发信息：启发函数设计不当时，性能大打折扣。
from Best_FirstClassic import Best_FirstClassic
# 4. 宽度优先搜索 (BFS)
# 描述：
#   - 逐层展开搜索，不考虑移动代价，从而保证搜索到的路径步数最少。
# 优点：
#   - 保证最少步数：能找到步数最少的路径（适用于所有移动代价相同的情况）。
#   - 算法简单且完整：实现简单，只要路径存在必定能找到。
# 缺点：
#   - 忽略移动代价：不适用于代价不统一的场景，不能保证整体代价最小。
from BFS import BFS
# 5. 迭代加深 A*（IDA*）（IDAStarClassic）
# 描述：
#   - 结合迭代加深和 A* 的思想，通过不断提高 f = g + h 的阈值来搜索路径。
# 优点：
#   - 内存效率高：相比标准 A*，IDA* 不需存储庞大的开放列表，适合内存受限场景。
# 缺点：
#   - 重复计算：可能多次重复搜索同一节点，导致计算量增加。
#   - 对阈值敏感：搜索效率和成功率依赖于阈值设定策略。
from IDA import IDAStarClassic
# 6. Theta* 算法（ThetastarClassic）
# 描述：
#   - 类似于 A* 算法，但通过视线检测实现路径平滑，适合网格障碍物环境。
# 优点：
#   - 平滑路径：能够生成较为直接和连续的路径。
#   - 保留 A* 的优势：在满足一定条件下依然能保证最优性。
# 缺点：
#   - 算法复杂：实现上比标准 A* 更复杂，尤其在处理复杂障碍时。
#   - 计算开销：在障碍密集区域，视线检测可能导致额外计算负担。
from ThetastarClassic import ThetastarClassic
from LPAstar import LPAstar
# =============================================================================
# 分类 B: 采样（随机）基方法 (Sampling-Based Methods)
# =============================================================================
# 1. 快速探索随机树（RRT）
# 描述：
#   - 从起点开始，通过在连续空间中随机采样并扩展树来快速探索整个搜索空间。
# 优点：
#   - 探索效率高：适合高维空间和复杂约束问题，能快速覆盖大部分自由空间。
#   - 实现简单：算法直观，编码实现较为容易。
# 缺点：
#   - 路径不最优：生成的路径通常较长且转弯多，需要后续平滑处理。
#   - 结果依赖随机性：每次运行结果可能有所不同。
from RRT import RRT
from RRTstar import RRTstar
#RRT 和概率路线图方法 （PRM） 是运动规划中常用的两种算法
# =============================================================================
# 分类 C: 仿生智能算法 (Bio-Inspired Algorithms)
# =============================================================================
# 1. 蚁群算法（Ant Colony Optimization，ACO）
# 描述：
#   - 模拟蚂蚁觅食行为，通过信息素的正反馈机制在图上搜索路径。
# 优点：
#   - 鲁棒性强：能在复杂、多变的环境中找到较优路径，具备自适应能力。
#   - 并行处理：算法具备天然并行特性，可通过并行计算加速搜索。
# 缺点：
#   - 计算密集：计算量大，收敛速度较慢。
#   - 易陷局部最优：参数选择不当可能导致收敛到次优解，需要精心调参。
from ACO_PathPlanning import ACOPathPlanner
# =============================================================================
# 分类 D: 基于势场的方法 (Potential Field Methods)
# =============================================================================
# 1. 势场规划方法（PotentialFieldPlanner）
# 描述：
#   - 利用人工势场模型，将目标设为吸引源、障碍物设为排斥源，通过势场梯度引导移动。
# 优点：
#   - 直观简单：算法易于理解和实现，实时性好。
#   - 计算效率高：在许多实际应用中能迅速计算出合适路径。
# 缺点：
#   - 局部极小值问题：容易陷入局部极小值，导致无法达到目标。
#   - 参数敏感：需要精细调节吸引和排斥参数以避免性能下降。
#from PotentialFieldPlanner import PotentialFieldPlanner
from MOAStar import MOAStar
from MOAStar import MOAStar
# =============================================================================
import math
from skimage.draw import line
from typing import List, Tuple

def interpolate_path(path):
    """
    对路径进行插值，使其包含所有点与点之间的连线路径上的点
    使用 Bresenham 线算法来获取每条边上的所有整数坐标点
    """
    if not path or len(path) < 2:
        return []
        
    interpolated_path = []
    for i in range(len(path) - 1):
        rr, cc = line(path[i][0], path[i][1], path[i+1][0], path[i+1][1])  # 获取线段上的所有整数点
        interpolated_path.extend(zip(rr, cc))  # 添加到路径列表中
    
    # 去除重复点以提高效率
    return list(dict.fromkeys(interpolated_path))

# 指标计算函数
def compute_path_length(path):
    """
    计算路径长度（即路过的像素点数）
    """
    interpolated_path = interpolate_path(path)
    return len(interpolated_path) if interpolated_path else 0

def compute_turn_count(path):
    """
    计算路径的转弯次数
    """
    turn_count = 0
    if len(path) < 3:
        return 0
    prev_move = (path[1][0] - path[0][0], path[1][1] - path[0][1])
    for i in range(1, len(path)-1):
        curr_move = (path[i+1][0] - path[i][0], path[i+1][1] - path[i][1])
        if curr_move != prev_move:
            turn_count += 1
        prev_move = curr_move
    return turn_count

def compute_smoothness(path):
    """
    计算路径的平滑度，基于路径中相邻向量之间的夹角。
    平滑度 = 所有转角度数的总和。
    参数：
        path (list of tuple): 路径点的坐标列表，如 [(x1, y1), (x2, y2), ...]

    返回：
        float: 平滑度，角度总和（单位：度）
    """
    smoothness = 0
    if len(path) < 3:
        return 0  # 不足以形成转弯
    
    def vector(p1, p2):
        return (p2[0] - p1[0], p2[1] - p1[1])
        
    def angle_between(v1, v2):
        # 计算两个向量之间的夹角（单位：度）
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]
        magnitude_v1 = math.hypot(v1[0], v1[1])
        magnitude_v2 = math.hypot(v2[0], v2[1])
        if magnitude_v1 == 0 or magnitude_v2 == 0:
            return 0  # 避免除以零     
        # 计算余弦值，防止浮点数误差导致超出[-1, 1]范围
        cos_theta = max(min(dot_product / (magnitude_v1 * magnitude_v2), 1), -1)
        angle_rad = math.acos(cos_theta)
        return math.degrees(angle_rad)
        
    for i in range(1, len(path) - 1):
        v1 = vector(path[i - 1], path[i])
        v2 = vector(path[i], path[i + 1])
        smoothness += angle_between(v1, v2)
    return smoothness

def compute_feasibility(path, thickness_image):
    """
    计算路径的可行性冰厚，考虑路径点和路径连线上的所有点的冰厚值
    
    参数：
        path (list of tuple): 路径点的坐标列表
        thickness_image (numpy.ndarray): 冰厚图像
        
    返回：
        float: 路径上所有点的冰厚总和
    """
    try:
        interpolated_path = interpolate_path(path)  # 生成完整路径点
        if not interpolated_path:
            return 0
            
        # 确保所有点都在图像范围内
        height, width = thickness_image.shape[:2]
        valid_points = [(r, c) for r, c in interpolated_path if 0 <= r < height and 0 <= c < width]
        
        return sum(thickness_image[pos[0], pos[1]] for pos in valid_points)
    except Exception as e:
        print(f"计算可行性时出错: {e}")
        return 0

def compute_risk(path, density_map_resized):
    """
    计算路径的风险，考虑路径点和路径连线上的所有点的风险值
    
    参数：
        path (list of tuple): 路径点的坐标列表
        density_map_resized (numpy.ndarray): 风险密度图
        
    返回：
        float: 路径上所有点的风险总和
    """
    try:
        interpolated_path = interpolate_path(path)  # 生成完整路径点
        if not interpolated_path:
            return 0
            
        # 确保所有点都在图像范围内
        height, width = density_map_resized.shape[:2]
        valid_points = [(r, c) for r, c in interpolated_path if 0 <= r < height and 0 <= c < width]
        
        return sum(density_map_resized[pos[0], pos[1]] for pos in valid_points)
    except Exception as e:
        print(f"计算风险时出错: {e}")
        return 0
# =============================================================================
# 绘制路径函数
def plot_path(grid, path, start, end, title="A* Pathfinding"):
    """
    绘制路径搜索结果

    参数：
      grid: 2D 数组或 numpy.array，表示网格地图。
      path: 路径列表，每个元素为 (x, y) 坐标。如果路径为空则不绘制路径。
      start: 起点坐标 (x, y)。
      end: 终点坐标 (x, y)。
      title: 图形标题（可选）。
    """
    plt.figure(figsize=(5, 5))
    # 显示网格图像，使用 origin='upper' 确保原点在左上角，与数组下标一致
    plt.imshow(grid, cmap='gray', origin='upper')
    # 如果有路径数据，则绘制路径（红色线条）
    if path:
        # 提取路径的 x 和 y 坐标
        path_x = [p[0] for p in path]
        path_y = [p[1] for p in path]
        plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
    # 绘制起点和终点
    plt.scatter(start[0], start[1], color='green', s=100, marker='o', label='Start')
    plt.scatter(end[0], end[1], color='blue', s=100, marker='x', label='End')
    plt.title(title)
    plt.legend()
    plt.grid(True)
    plt.show()
    
 # 在调用算法前添加环境检查
def validate_environment(ice_image, start, goal):
    # 检查起点终点是否可达
    if ice_image[start] != 0 or ice_image[goal] != 0:
        raise ValueError("起点或终点位于障碍物上")
    
    # 简单连通性检查
    tmp_env = ice_image.copy()
    queue = [start]
    tmp_env[start] = 2  # 标记已访问
    while queue:
        x, y = queue.pop(0)
        for dx, dy in [(-1,0),(1,0),(0,-1),(0,1)]:
            nx, ny = x+dx, y+dy
            if 0<=nx<tmp_env.shape[0] and 0<=ny<tmp_env.shape[1]:
                if tmp_env[nx, ny] == 0:
                    if (nx, ny) == goal:
                        return True
                    tmp_env[nx, ny] = 2
                    queue.append((nx, ny))
    return False 

# =============================================================================
# 参数设置：各项指标的权重（新目标函数版本）
from  MOAStar import MOAStarWeights
# 创建权重系数对象并归一化
weights = MOAStarWeights(
    distance_weight=1.0,    # 距离权重
    turning_weight=0.5,     # 转弯惩罚权重
    smoothness_weight=0.8,  # 平滑性惩罚（转弯附加惩罚）
    safety_weight=0.8,      # 冰厚安全性惩罚（冰越厚惩罚越高）
    risk_weight=0.6         # 海冰风险惩罚（风险越高惩罚越高）
).normalize()
# 测试主函数
path_image=ice_image#原始
path_image=weighted_image#膨胀
path_image=rect_mask#方框掩膜
# 保存为 Excel
base_path = "C:/Users/<USER>/Desktop/ICE/lable"
output_excel_path = os.path.join(base_path, f"{1}.xlsx")
df = pd.DataFrame(path_image)
os.makedirs(os.path.dirname(output_excel_path), exist_ok=True)
df.to_excel(output_excel_path, index=False, header=False)
print(f"处理完成，结果已保存至 {output_excel_path}")
#path_image=circle_mask#圆形掩膜

# 设置起点和终点（这里起点设为右下角，终点设为左上角）
start = (path_image.shape[0] - 1, path_image.shape[1] - 1)
end = (0, 0)
if not validate_environment(path_image, start, end):
    print("错误：起点和终点之间没有连通路径！")
    sys.exit(1)
    
from sklearn.preprocessing import MinMaxScaler
safety_map=density_map_resized#安全性地图
risk_map=thickness_image#风险地图
# 安全地图标准化
scaler_safety = MinMaxScaler()
safety_flat = safety_map.reshape(-1, 1)  # 展平为一维样本数组
safety_scaled = scaler_safety.fit_transform(safety_flat)
safety_scaled = safety_scaled.reshape(safety_map.shape)  # 恢复二维形状
# 风险地图标准化
scaler_risk = MinMaxScaler()
risk_flat = risk_map.reshape(-1, 1)
risk_scaled = scaler_risk.fit_transform(risk_flat)
risk_scaled = risk_scaled.reshape(risk_map.shape)
   
print("【MOA* 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 1
Moastar_path, runs = MOAStar(path_image, start, end, weights=weights,safety_map=safety_map,risk_map=risk_map,
            diagonal_movement=diagonal)
if Moastar_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(Moastar_path))
    plot_path(gray_image, Moastar_path, start, end, title="MOA*")
else:
    print("未找到路径！")
    
from FlowAstar import FlowAstar
print("【FlowA* 算法 - 经典版本】开始路径规划...")
# 读取 Excel 文件
file_path = 'lable/velocity.xlsx'
velocity_data = pd.read_excel(file_path, header=None, usecols=range(308), nrows=308)
# 将数据转换为 NumPy 数组
velocity_numpy_array = velocity_data.to_numpy()
# 计算裁剪起始索引
start_idx = (308 - 256) // 2  # 计算起点索引
end_idx = start_idx + 256      # 计算终点索引
# 提取中间 256x256 区域
resampled_matrix = velocity_numpy_array[start_idx:end_idx, start_idx:end_idx]
velocity_field=resampled_matrix
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 1
Flowastar_path, runs = FlowAstar(path_image, start, end, velocity_field, diagonal_movement=diagonal)
if Flowastar_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(Flowastar_path))
    plot_path(gray_image, Flowastar_path, start, end, title="FlowA*")
else:
    print("未找到路径！")

print("【Dijkstra 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 1
dijkstra_classic_path, runs = DijkstraClassic(path_image, start, end, diagonal_movement=diagonal)
if dijkstra_classic_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(dijkstra_classic_path))
    plot_path(gray_image, dijkstra_classic_path, start, end, title="Dijkstra Classic")
else:
    print("未找到路径！")
    
print("【A* 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 1
astar_classic_path, runs = AstarClassic(path_image, start, end, diagonal_movement=diagonal)
if astar_classic_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(astar_classic_path))
    plot_path(gray_image, astar_classic_path, start, end, title="A* Classic")
else:
    print("未找到路径！")

print("【BestFirst 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 3
bestfirst_classic_path, runs = Best_FirstClassic(path_image, start, end, diagonal_movement=diagonal)
if bestfirst_classic_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(bestfirst_classic_path))
    plot_path(gray_image, bestfirst_classic_path, start, end, title="BestFirst Classic")
else:
    print("未找到路径！")
    
print("【BFS 算法 - 经典版本】开始路径规划...")
# 设置对角线移动策略（此处选择总是允许对角线移动）
diagonal = 3
bfs_classic_path, runs = BFS(path_image, start, end, diagonal_movement=diagonal)
if bfs_classic_path:
    print("找到路径，迭代次数：", runs)
    print("步数：", compute_path_length(bfs_classic_path))
    plot_path(gray_image, bfs_classic_path, start, end, title="BestFirst Classic")
else:
    print("未找到路径！")    
    
from FlowThetastar import FlowThetastar
print("\n【FlowTheta* 算法】开始路径规划...")
flowtheta_star_path = FlowThetastar(path_image, start, end,velocity_field)
if flowtheta_star_path:
    print("路径找到，步数:", compute_path_length(flowtheta_star_path))
    plot_path(gray_image.T, flowtheta_star_path, start, end, title="FlowTheta* Path")
else:
    print("未找到路径!") 
    
print("\n【Theta* 算法】开始路径规划...")
theta_star_path = ThetastarClassic(path_image, start, end)
if theta_star_path:
    print("路径找到，步数:", compute_path_length(theta_star_path))
    plot_path(gray_image.T, theta_star_path, start, end, title="Theta* Path")
else:
    print("未找到路径!")

# print("\n【LPA* 算法】开始路径规划...")
# lpa_star = LPAstar(path_image, start, end)
# lpa_star_path = lpa_star.computeShortestPath()
# if lpa_star_path:
#     print("路径找到，步数:", compute_path_length(lpa_star_path))
#     plot_path(gray_image.T, lpa_star_path, start, end, title="Theta* Path")
# else:
#     print("未找到路径!")

print("\n【RRT 算法】开始路径规划...")
rrt = RRT(start, end, path_image, step_size=0.5, max_iterations=1000, goal_sample_rate=0.1)
rrt_path = rrt.plan()
if rrt_path:
    print("路径找到")
    plt.figure(figsize=(8, 8))
    # 显示网格图像，使用 origin='upper' 确保原点在左上角，与数组下标一致
    plt.imshow(gray_image, cmap='gray', origin='upper')
    for node in rrt.nodes:
        if node.parent:
            plt.plot([node.x, node.parent.x], [node.y, node.parent.y], 'g-', alpha=0.3)
    if rrt_path:
        plt.plot(*zip(*rrt_path), 'b-', linewidth=2, label='Path')
    plt.plot(start[0], start[1], 'go', label='Start')
    plt.plot(end[0], end[1], 'ro', label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT 路径规划')
    plt.show()
else:
    print("未找到路径!")

print("\n【RRTstar 算法】开始路径规划...")
rrt_star = RRTstar(start, end, path_image, step_size=0.5, max_iterations=1000, goal_sample_rate=0.1, search_radius=1.0)
rrt_star_path = rrt_star.plan()
if rrt_star_path:
    print("路径找到")
    plt.figure(figsize=(8, 8))
    # 显示网格图像，使用 origin='upper' 确保原点在左上角，与数组下标一致
    plt.imshow(gray_image, cmap='gray', origin='upper')
    for node in rrt_star.nodes:
        if node.parent:
            plt.plot([node.x, node.parent.x], [node.y, node.parent.y], 'g-', alpha=0.3)
    if rrt_path:
        plt.plot(*zip(*rrt_star_path), 'b-', linewidth=2, label='Path')
    plt.plot(start[0], start[1], 'go', label='Start')
    plt.plot(end[0], end[1], 'ro', label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT 路径规划')
    plt.show()
else:
    print("未找到路径!")
    
print("\n【蚁群算法】开始路径规划...")
aco_planner = ACOPathPlanner(start, end, path_image)
# 运行优化
aco_path = aco_planner.optimize()
if aco_path:
    print("路径找到")
    # 可视化结果
    plt.figure(figsize=(8, 8))
    # 显示网格图像，使用 origin='upper' 确保原点在左上角，与数组下标一致
    plt.imshow(gray_image, cmap='gray', origin='upper')
    # 绘制信息素分布
    plt.imshow(aco_planner.pheromone.T, cmap='Blues', alpha=0.3)
    # 绘制最优路径
    path_array = np.array(aco_path)
    plt.plot(path_array[:, 0], path_array[:, 1], 'g-', linewidth=2, label='Path')
    plt.plot(start[0], start[1], 'go', label='Start')
    plt.plot(end[0], end[1], 'ro', label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('ACO 路径规划')
    plt.show()
else:
    print("未找到可行路径")
    
# print("\n【势场法】开始路径规划...")
# pf = PotentialFieldPlanner(path_image, start, end)
# pf_path = pf.search()
# if pf_path:
#     print("路径找到，步数:", compute_path_length(pf_path))
#     plot_path(gray_image, pf_path, start, end, title="Potential Field Path")
# else:
#     print("未找到路径!")
    
# ----------------------------
# 对比各算法的路径指标
methods = {
    "MOA*": Moastar_path,
    "A* Classic": astar_classic_path,
    "Dijkstra Classic": dijkstra_classic_path,
    #"RRT": rrt_path,
    "Theta*": theta_star_path,
    #"ACO": aco_path,
    #"Potential Field": pf_path,
    "BFT": bestfirst_classic_path,
    "BFS": bfs_classic_path,
    "Theta*":theta_star_path
}

path_metrics = {}
for method, path in methods.items():
    if path:
        path_metrics[method] = {
            "路径长度（步数）": compute_path_length(path),
            "转弯次数": compute_turn_count(path),
            "平滑性（累计转弯角度）": compute_smoothness(path),
            "可行性代价（冰厚）": compute_feasibility(path, normalized_image),
            "风险指数（海冰风险）": compute_risk(path, density_map_resized)
        }
# 打印统计结果
for method, metrics in path_metrics.items():
    print(f"\n【{method}】路径指标：")
    for key, value in metrics.items():
        print(f"  {key}: {value:.2f}" if isinstance(value, float) else f"  {key}: {value}")
        
# 获取所有方法名称
methods_list = list(path_metrics.keys())

# 获取所有指标名称
metrics_names = list(next(iter(path_metrics.values())).keys())

# 构建 NumPy 矩阵（行：方法，列：指标）
metrics_matrix = np.array([[path_metrics[method][metric] for metric in metrics_names] for method in methods_list])
metrics_matrixs=[]
metrics_matrixs.append(metrics_matrix)



