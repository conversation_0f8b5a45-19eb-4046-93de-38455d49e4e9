import heapq
import numpy as np
import math
import matplotlib.pyplot as plt

def heuristic(a: tuple, b: tuple) -> float:
    """
    改进的启发函数（octile 距离），适用于八方向移动：
      h = dx + dy + (sqrt(2)-2)*min(dx, dy)
    """
    dx = abs(a[0] - b[0])
    dy = abs(a[1] - b[1])
    return (dx + dy) + (math.sqrt(2) - 2) * min(dx, dy)

def distance(a: tuple, b: tuple) -> float:
    """
    计算两点之间的欧几里得距离
    """
    return math.hypot(a[0] - b[0], a[1] - b[1])

def bresenham_line(start: tuple, end: tuple) -> list:
    """
    使用 Bresenham 算法计算两点间的离散直线
    """
    x1, y1 = start
    x2, y2 = end
    dx = abs(x2 - x1)
    dy = -abs(y2 - y1)
    sx = 1 if x1 < x2 else -1
    sy = 1 if y1 < y2 else -1
    err = dx + dy
    line = []
    
    while True:
        line.append((x1, y1))
        if x1 == x2 and y1 == y2:
            break
        e2 = 2 * err
        if e2 >= dy:
            err += dy
            x1 += sx
        if e2 <= dx:
            err += dx
            y1 += sy
    return line

def has_line_of_sight(ice_image: np.ndarray, a: tuple, b: tuple) -> bool:
    """
    判断 a 到 b 之间的直线路径上是否无障碍（像素值为 0 表示无障碍）
    """
    line = bresenham_line(a, b)
    for (x, y) in line:
        if ice_image[x, y] != 0:
            return False
    return True

def ThetastarClassic(ice_image: np.ndarray, start: tuple, goal: tuple) -> list:
    """
    Theta* 算法实现：
      在标准 A* 的基础上，对扩展的每个邻居，如果祖父节点与邻居之间有直视路径，
      则尝试通过祖父节点更新路径，进而得到更平滑的任意角路径。
    """
    open_heap = []
    # (优先级, 当前代价, 当前节点)
    heapq.heappush(open_heap, (heuristic(start, goal), 0, start))
    came_from = {start: None}
    cost_so_far = {start: 0}
    
    while open_heap:
        current_priority, current_cost, current_node = heapq.heappop(open_heap)
        
        if current_node == goal:
            break
            
        # 扩展八邻域
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                next_node = (current_node[0] + dr, current_node[1] + dc)
                # 检查边界和障碍
                if (0 <= next_node[0] < ice_image.shape[0] and 
                    0 <= next_node[1] < ice_image.shape[1] and 
                    ice_image[next_node[0], next_node[1]] == 0):
                    
                    # 计算从 current 到 next 的实际代价（欧几里得距离）
                    new_cost = cost_so_far[current_node] + distance(current_node, next_node)
                    
                    # 尝试利用祖父节点进行路径优化
                    if came_from[current_node] is not None:
                        grand_parent = came_from[current_node]
                        if has_line_of_sight(ice_image, grand_parent, next_node):
                            # 注意：此处使用欧几里得距离计算祖父节点到 next 的代价
                            tentative_cost = cost_so_far[grand_parent] + distance(grand_parent, next_node)
                            if next_node not in cost_so_far or tentative_cost < cost_so_far[next_node]:
                                came_from[next_node] = grand_parent
                                cost_so_far[next_node] = tentative_cost
                                priority = tentative_cost + heuristic(next_node, goal)
                                heapq.heappush(open_heap, (priority, tentative_cost, next_node))
                                continue
                    
                    if next_node not in cost_so_far or new_cost < cost_so_far[next_node]:
                        cost_so_far[next_node] = new_cost
                        priority = new_cost + heuristic(next_node, goal)
                        heapq.heappush(open_heap, (priority, new_cost, next_node))
                        came_from[next_node] = current_node
    
    # 重构路径
    path = []
    current = goal
    if current not in came_from:
        return None  # 无法到达目标
    while current is not None:
        path.append(current)
        current = came_from.get(current)
    path.reverse()
    return path

# ----------------------- 测试和可视化示例 -----------------------

# def visualize(grid, path, start, goal):
#     plt.figure(figsize=(8, 8))
#     plt.imshow(grid, cmap='Greys', origin='lower')
#     plt.scatter(start[1], start[0], color='green', s=100, label='Start')
#     plt.scatter(goal[1], goal[0], color='blue', s=100, label='Goal')

#     if path:
#         # 提取 x（列）和 y（行）坐标
#         x_coords = [p[1] for p in path]
#         y_coords = [p[0] for p in path]
#         plt.plot(x_coords, y_coords, color='red', linewidth=2, label='Path')

#         # 标记出那些路径段若有障碍（理论上不应发生，因为 Theta* 应该产生平滑路径）
#         for i in range(1, len(path)):
#             if not has_line_of_sight(grid, path[i-1], path[i]):
#                 plt.plot([path[i-1][1], path[i][1]], [path[i-1][0], path[i][0]], color='yellow', linewidth=3, linestyle='dashed')

#     plt.legend()
#     plt.title('Optimized Theta* Path')
#     plt.show()

# # 示例地图：100x100 网格，随机布置障碍物
# np.random.seed(0)
# grid = np.zeros((100, 100), dtype=int)
# # 随机设置 200 个障碍物，障碍物对应像素值为 1
# obstacle_indices = (np.random.randint(0, 100, 200), np.random.randint(0, 100, 200))
# grid[obstacle_indices] = 1

# start = (0, 0)
# goal = (99, 99)

# path = ThetastarClassic(grid, start, goal)
# visualize(grid, path, start, goal)
