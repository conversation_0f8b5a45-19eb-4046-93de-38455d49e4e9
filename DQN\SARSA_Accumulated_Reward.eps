%!PS-Adobe-3.0 EPSF-3.0
%%Title: SARSA_Accumulated_Reward.eps
%%Creator: Matplotlib v3.7.5, https://matplotlib.org/
%%CreationDate: Mon Feb 26 10:57:42 2024
%%Orientation: portrait
%%BoundingBox: 75 223 537 569
%%HiResBoundingBox: 75.600000 223.200000 536.400000 568.800000
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Matplotlib.
10 dict begin
/FontName /DejaVuSans def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-2090 -948 3673 2524] def
/FontType 3 def
/Encoding [/minus /space /zero /one /two /three /four /five /seven /A /E /a /c /d /e /i /l /m /o /p /r /s /t /u /w] def
/CharStrings 26 dict dup begin
/.notdef 0 def
/minus{1716 0 217 557 1499 727 sc
217 727 m
1499 727 l
1499 557 l
217 557 l
217 727 l

ce} _d
/space{651 0 0 0 0 0 sc
ce} _d
/zero{1303 0 135 -29 1167 1520 sc
651 1360 m
547 1360 469 1309 416 1206 c
364 1104 338 950 338 745 c
338 540 364 387 416 284 c
469 182 547 131 651 131 c
756 131 834 182 886 284 c
939 387 965 540 965 745 c
965 950 939 1104 886 1206 c
834 1309 756 1360 651 1360 c

651 1520 m
818 1520 946 1454 1034 1321 c
1123 1189 1167 997 1167 745 c
1167 494 1123 302 1034 169 c
946 37 818 -29 651 -29 c
484 -29 356 37 267 169 c
179 302 135 494 135 745 c
135 997 179 1189 267 1321 c
356 1454 484 1520 651 1520 c

ce} _d
/one{1303 0 225 0 1114 1493 sc
254 170 m
584 170 l
584 1309 l
225 1237 l
225 1421 l
582 1493 l
784 1493 l
784 170 l
1114 170 l
1114 0 l
254 0 l
254 170 l

ce} _d
/two{1303 0 150 0 1098 1520 sc
393 170 m
1098 170 l
1098 0 l
150 0 l
150 170 l
227 249 331 356 463 489 c
596 623 679 709 713 748 c
778 821 823 882 848 932 c
874 983 887 1032 887 1081 c
887 1160 859 1225 803 1275 c
748 1325 675 1350 586 1350 c
523 1350 456 1339 385 1317 c
315 1295 240 1262 160 1217 c
160 1421 l
241 1454 317 1478 388 1495 c
459 1512 523 1520 582 1520 c
737 1520 860 1481 952 1404 c
1044 1327 1090 1223 1090 1094 c
1090 1033 1078 974 1055 919 c
1032 864 991 800 930 725 c
913 706 860 650 771 557 c
682 465 556 336 393 170 c

ce} _d
/three{1303 0 156 -29 1139 1520 sc
831 805 m
928 784 1003 741 1057 676 c
1112 611 1139 530 1139 434 c
1139 287 1088 173 987 92 c
886 11 742 -29 555 -29 c
492 -29 428 -23 361 -10 c
295 2 227 20 156 45 c
156 240 l
212 207 273 183 340 166 c
407 149 476 141 549 141 c
676 141 772 166 838 216 c
905 266 938 339 938 434 c
938 522 907 591 845 640 c
784 690 698 715 588 715 c
414 715 l
414 881 l
596 881 l
695 881 771 901 824 940 c
877 980 903 1037 903 1112 c
903 1189 876 1247 821 1288 c
767 1329 689 1350 588 1350 c
533 1350 473 1344 410 1332 c
347 1320 277 1301 201 1276 c
201 1456 l
278 1477 349 1493 416 1504 c
483 1515 547 1520 606 1520 c
759 1520 881 1485 970 1415 c
1059 1346 1104 1252 1104 1133 c
1104 1050 1080 980 1033 923 c
986 866 918 827 831 805 c

ce} _d
/four{1303 0 100 0 1188 1493 sc
774 1317 m
264 520 l
774 520 l
774 1317 l

721 1493 m
975 1493 l
975 520 l
1188 520 l
1188 352 l
975 352 l
975 0 l
774 0 l
774 352 l
100 352 l
100 547 l
721 1493 l

ce} _d
/five{1303 0 158 -29 1124 1493 sc
221 1493 m
1014 1493 l
1014 1323 l
406 1323 l
406 957 l
435 967 465 974 494 979 c
523 984 553 987 582 987 c
749 987 881 941 978 850 c
1075 759 1124 635 1124 479 c
1124 318 1074 193 974 104 c
874 15 733 -29 551 -29 c
488 -29 424 -24 359 -13 c
294 -2 227 14 158 35 c
158 238 l
218 205 280 181 344 165 c
408 149 476 141 547 141 c
662 141 754 171 821 232 c
888 293 922 375 922 479 c
922 583 888 665 821 726 c
754 787 662 817 547 817 c
493 817 439 811 385 799 c
332 787 277 768 221 743 c
221 1493 l

ce} _d
/seven{1303 0 168 0 1128 1493 sc
168 1493 m
1128 1493 l
1128 1407 l
586 0 l
375 0 l
885 1323 l
168 1323 l
168 1493 l

ce} _d
/A{1401 0 16 0 1384 1493 sc
700 1294 m
426 551 l
975 551 l
700 1294 l

586 1493 m
815 1493 l
1384 0 l
1174 0 l
1038 383 l
365 383 l
229 0 l
16 0 l
586 1493 l

ce} _d
/E{1294 0 201 0 1163 1493 sc
201 1493 m
1145 1493 l
1145 1323 l
403 1323 l
403 881 l
1114 881 l
1114 711 l
403 711 l
403 170 l
1163 170 l
1163 0 l
201 0 l
201 1493 l

ce} _d
/a{1255 0 123 -29 1069 1147 sc
702 563 m
553 563 450 546 393 512 c
336 478 307 420 307 338 c
307 273 328 221 371 182 c
414 144 473 125 547 125 c
649 125 731 161 792 233 c
854 306 885 402 885 522 c
885 563 l
702 563 l

1069 639 m
1069 0 l
885 0 l
885 170 l
843 102 791 52 728 19 c
665 -13 589 -29 498 -29 c
383 -29 292 3 224 67 c
157 132 123 218 123 326 c
123 452 165 547 249 611 c
334 675 460 707 627 707 c
885 707 l
885 725 l
885 810 857 875 801 921 c
746 968 668 991 567 991 c
503 991 441 983 380 968 c
319 953 261 930 205 899 c
205 1069 l
272 1095 338 1114 401 1127 c
464 1140 526 1147 586 1147 c
748 1147 869 1105 949 1021 c
1029 937 1069 810 1069 639 c

ce} _d
/c{1126 0 113 -29 999 1147 sc
999 1077 m
999 905 l
947 934 895 955 842 969 c
790 984 737 991 684 991 c
565 991 472 953 406 877 c
340 802 307 696 307 559 c
307 422 340 316 406 240 c
472 165 565 127 684 127 c
737 127 790 134 842 148 c
895 163 947 184 999 213 c
999 43 l
948 19 894 1 839 -11 c
784 -23 726 -29 664 -29 c
495 -29 361 24 262 130 c
163 236 113 379 113 559 c
113 742 163 885 263 990 c
364 1095 501 1147 676 1147 c
733 1147 788 1141 842 1129 c
896 1118 948 1100 999 1077 c

ce} _d
/d{1300 0 113 -29 1114 1556 sc
930 950 m
930 1556 l
1114 1556 l
1114 0 l
930 0 l
930 168 l
891 101 842 52 783 19 c
724 -13 654 -29 571 -29 c
436 -29 325 25 240 133 c
155 241 113 383 113 559 c
113 735 155 877 240 985 c
325 1093 436 1147 571 1147 c
654 1147 724 1131 783 1098 c
842 1066 891 1017 930 950 c

303 559 m
303 424 331 317 386 240 c
442 163 519 125 616 125 c
713 125 790 163 846 240 c
902 317 930 424 930 559 c
930 694 902 800 846 877 c
790 954 713 993 616 993 c
519 993 442 954 386 877 c
331 800 303 694 303 559 c

ce} _d
/e{1260 0 113 -29 1151 1147 sc
1151 606 m
1151 516 l
305 516 l
313 389 351 293 419 226 c
488 160 583 127 705 127 c
776 127 844 136 910 153 c
977 170 1043 196 1108 231 c
1108 57 l
1042 29 974 8 905 -7 c
836 -22 765 -29 694 -29 c
515 -29 374 23 269 127 c
165 231 113 372 113 549 c
113 732 162 878 261 985 c
360 1093 494 1147 662 1147 c
813 1147 932 1098 1019 1001 c
1107 904 1151 773 1151 606 c

967 660 m
966 761 937 841 882 901 c
827 961 755 991 664 991 c
561 991 479 962 417 904 c
356 846 320 764 311 659 c
967 660 l

ce} _d
/i{569 0 193 0 377 1556 sc
193 1120 m
377 1120 l
377 0 l
193 0 l
193 1120 l

193 1556 m
377 1556 l
377 1323 l
193 1323 l
193 1556 l

ce} _d
/l{569 0 193 0 377 1556 sc
193 1556 m
377 1556 l
377 0 l
193 0 l
193 1556 l

ce} _d
/m{1995 0 186 0 1821 1147 sc
1065 905 m
1111 988 1166 1049 1230 1088 c
1294 1127 1369 1147 1456 1147 c
1573 1147 1663 1106 1726 1024 c
1789 943 1821 827 1821 676 c
1821 0 l
1636 0 l
1636 670 l
1636 777 1617 857 1579 909 c
1541 961 1483 987 1405 987 c
1310 987 1234 955 1179 892 c
1124 829 1096 742 1096 633 c
1096 0 l
911 0 l
911 670 l
911 778 892 858 854 909 c
816 961 757 987 678 987 c
584 987 509 955 454 891 c
399 828 371 742 371 633 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
413 1015 463 1065 522 1098 c
581 1131 650 1147 731 1147 c
812 1147 881 1126 938 1085 c
995 1044 1038 984 1065 905 c

ce} _d
/o{1253 0 113 -29 1141 1147 sc
627 991 m
528 991 450 952 393 875 c
336 798 307 693 307 559 c
307 425 335 319 392 242 c
449 165 528 127 627 127 c
725 127 803 166 860 243 c
917 320 946 426 946 559 c
946 692 917 797 860 874 c
803 952 725 991 627 991 c

627 1147 m
787 1147 913 1095 1004 991 c
1095 887 1141 743 1141 559 c
1141 376 1095 232 1004 127 c
913 23 787 -29 627 -29 c
466 -29 340 23 249 127 c
158 232 113 376 113 559 c
113 743 158 887 249 991 c
340 1095 466 1147 627 1147 c

ce} _d
/p{1300 0 186 -426 1188 1147 sc
371 168 m
371 -426 l
186 -426 l
186 1120 l
371 1120 l
371 950 l
410 1017 458 1066 517 1098 c
576 1131 647 1147 729 1147 c
865 1147 975 1093 1060 985 c
1145 877 1188 735 1188 559 c
1188 383 1145 241 1060 133 c
975 25 865 -29 729 -29 c
647 -29 576 -13 517 19 c
458 52 410 101 371 168 c

997 559 m
997 694 969 800 913 877 c
858 954 781 993 684 993 c
587 993 510 954 454 877 c
399 800 371 694 371 559 c
371 424 399 317 454 240 c
510 163 587 125 684 125 c
781 125 858 163 913 240 c
969 317 997 424 997 559 c

ce} _d
/r{842 0 186 0 842 1147 sc
842 948 m
821 960 799 969 774 974 c
750 980 723 983 694 983 c
590 983 510 949 454 881 c
399 814 371 717 371 590 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
410 1014 460 1064 522 1097 c
584 1130 659 1147 748 1147 c
761 1147 775 1146 790 1144 c
805 1143 822 1140 841 1137 c
842 948 l

ce} _d
/s{1067 0 111 -29 967 1147 sc
907 1087 m
907 913 l
855 940 801 960 745 973 c
689 986 631 993 571 993 c
480 993 411 979 365 951 c
320 923 297 881 297 825 c
297 782 313 749 346 724 c
379 700 444 677 543 655 c
606 641 l
737 613 829 573 884 522 c
939 471 967 400 967 309 c
967 205 926 123 843 62 c
761 1 648 -29 504 -29 c
444 -29 381 -23 316 -11 c
251 0 183 18 111 41 c
111 231 l
179 196 246 169 312 151 c
378 134 443 125 508 125 c
595 125 661 140 708 169 c
755 199 778 241 778 295 c
778 345 761 383 727 410 c
694 437 620 462 506 487 c
442 502 l
328 526 246 563 195 612 c
144 662 119 730 119 817 c
119 922 156 1004 231 1061 c
306 1118 412 1147 549 1147 c
617 1147 681 1142 741 1132 c
801 1122 856 1107 907 1087 c

ce} _d
/t{803 0 55 0 754 1438 sc
375 1438 m
375 1120 l
754 1120 l
754 977 l
375 977 l
375 369 l
375 278 387 219 412 193 c
437 167 488 154 565 154 c
754 154 l
754 0 l
565 0 l
423 0 325 26 271 79 c
217 132 190 229 190 369 c
190 977 l
55 977 l
55 1120 l
190 1120 l
190 1438 l
375 1438 l

ce} _d
/u{1298 0 174 -29 1112 1147 sc
174 442 m
174 1120 l
358 1120 l
358 449 l
358 343 379 263 420 210 c
461 157 523 131 606 131 c
705 131 784 163 841 226 c
899 289 928 376 928 485 c
928 1120 l
1112 1120 l
1112 0 l
928 0 l
928 172 l
883 104 831 53 772 20 c
713 -13 645 -29 567 -29 c
438 -29 341 11 274 91 c
207 171 174 288 174 442 c

637 1147 m
637 1147 l

ce} _d
/w{1675 0 86 0 1589 1120 sc
86 1120 m
270 1120 l
500 246 l
729 1120 l
946 1120 l
1176 246 l
1405 1120 l
1589 1120 l
1296 0 l
1079 0 l
838 918 l
596 0 l
379 0 l
86 1120 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
75.6 223.2 translate
460.8 345.6 0 0 clipbox
gsave
0 0 m
460.8 0 l
460.8 345.6 l
0 345.6 l
cl
1.000 setgray
fill
grestore
gsave
75.971875 46.971875 m
450 46.971875 l
450 334.8 l
75.971875 334.8 l
cl
1.000 setgray
fill
grestore
0.800 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
92.9732 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

89.1528 30.8469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
161.115 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

149.654 30.8469 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
229.256 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

217.795 30.8469 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
297.397 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

285.936 30.8469 translate
0 rotate
0 0 m /three glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
365.539 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

354.078 30.8469 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
433.68 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

422.219 30.8469 translate
0 rotate
0 0 m /five glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

235.494 13.7063 translate
0 rotate
0 0 m /E glyphshow
8.8457 0 m /p glyphshow
17.7324 0 m /i glyphshow
21.6221 0 m /s glyphshow
28.916 0 m /o glyphshow
37.4814 0 m /d glyphshow
46.3682 0 m /e glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 78.5254 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 73.9629 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /seven glyphshow
25.3242 0 m /five glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 113.098 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 108.536 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /five glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 147.671 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 143.108 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /two glyphshow
25.3242 0 m /five glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 182.243 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 177.681 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 216.816 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 212.253 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /seven glyphshow
17.6895 0 m /five glyphshow
25.3242 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 251.388 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 246.826 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /five glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 285.961 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 281.398 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /two glyphshow
17.6895 0 m /five glyphshow
25.3242 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 320.534 o
grestore
/DejaVuSans 12.000 selectfont
gsave

61.3312 315.971 translate
0 rotate
0 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

21.4406 118.605 translate
90 rotate
0 0 m /A glyphshow
9.32715 0 m /c glyphshow
17.0244 0 m /c glyphshow
24.7217 0 m /u glyphshow
33.5947 0 m /m glyphshow
47.2324 0 m /u glyphshow
56.1055 0 m /l glyphshow
59.9951 0 m /a glyphshow
68.5742 0 m /t glyphshow
74.0635 0 m /e glyphshow
82.6768 0 m /d glyphshow
91.5635 0 m /space glyphshow
96.0137 0 m /r glyphshow
101.52 0 m /e glyphshow
110.133 0 m /w glyphshow
121.583 0 m /a glyphshow
130.162 0 m /r glyphshow
135.668 0 m /d glyphshow
grestore
1.500 setlinewidth
2 setlinecap
0.000 0.000 1.000 setrgbcolor
gsave
374.028 287.828 75.972 46.972 clipbox
92.973153 275.62017 m
93.654567 301.255629 l
94.335981 309.948794 l
95.017395 305.735014 l
95.698809 309.168316 l
96.380223 294.857465 l
97.061637 301.404624 l
97.743051 265.836789 l
98.424465 299.316653 l
99.105879 299.10879 l
99.787293 246.209013 l
100.468707 214.706104 l
101.150121 288.698185 l
101.831535 212.171006 l
102.512949 223.862112 l
103.194363 253.334943 l
103.875777 196.61152 l
104.557191 262.4342 l
105.238605 297.647323 l
105.920019 280.591766 l
106.601433 198.921036 l
107.282847 301.213335 l
107.964261 204.139678 l
108.645675 274.106012 l
109.327089 184.435458 l
110.008503 305.003608 l
110.689916 172.867549 l
111.37133 154.177173 l
112.052744 273.614949 l
112.734158 238.80884 l
113.415572 223.733426 l
114.096986 125.380185 l
114.7784 200.763684 l
115.459814 255.575598 l
116.141228 177.474305 l
116.822642 219.375649 l
117.504056 234.251075 l
118.18547 241.578643 l
118.866884 231.843828 l
119.548298 283.383504 l
120.229712 141.675151 l
120.911126 260.479523 l
121.59254 206.253831 l
122.273954 294.617189 l
122.955368 198.820381 l
123.636782 158.182582 l
124.318196 163.288133 l
124.99961 114.210614 l
125.681024 238.010877 l
126.362438 174.668136 l
127.725266 297.855865 l
128.40668 214.347493 l
129.088094 145.233264 l
129.769507 289.698756 l
130.450921 192.72608 l
131.132335 165.834867 l
131.813749 144.200217 l
132.495163 239.891497 l
133.176577 277.546511 l
133.857991 92.893355 l
134.539405 240.561722 l
135.220819 103.016223 l
135.902233 213.497043 l
136.583647 193.609819 l
137.265061 120.100731 l
137.946475 129.912511 l
138.627889 278.122925 l
139.309303 108.664959 l
139.990717 271.531172 l
140.672131 217.088823 l
141.353545 217.04414 l
142.034959 273.42585 l
142.716373 236.006615 l
143.397787 209.518218 l
144.079201 178.569634 l
144.760615 261.873828 l
145.442029 245.749722 l
146.123443 209.388741 l
146.804857 256.460657 l
147.486271 219.49049 l
148.167685 287.687407 l
148.849098 240.223735 l
149.530512 296.046501 l
150.211926 278.505302 l
150.89334 148.503546 l
151.574754 250.347158 l
152.256168 206.211667 l
152.937582 286.771546 l
153.618996 262.418128 l
154.30041 149.869286 l
154.981824 190.20396 l
155.663238 60.054972 l
156.344652 287.978633 l
157.026066 280.389837 l
157.70748 67.513678 l
158.388894 214.974476 l
159.070308 237.030979 l
159.751722 225.237411 l
160.433136 303.699408 l
161.11455 287.648523 l
161.795964 159.702162 l
162.477378 259.57668 l
163.158792 273.152456 l
163.840206 289.579839 l
164.52162 254.768945 l
165.203034 259.780332 l
165.884448 182.000459 l
166.565862 255.206259 l
167.247276 165.555138 l
167.928689 170.61698 l
168.610103 112.89346 l
169.291517 140.519401 l
169.972931 265.5237 l
170.654345 117.874806 l
171.335759 163.767134 l
172.017173 270.26649 l
172.698587 263.512261 l
173.380001 215.778638 l
174.061415 289.36725 l
174.742829 210.650484 l
175.424243 272.546573 l
176.105657 240.82747 l
176.787071 279.509938 l
177.468485 199.591383 l
178.149899 240.930905 l
178.831313 272.115349 l
179.512727 270.252545 l
180.194141 240.591382 l
180.875555 236.161524 l
181.556969 246.545563 l
182.238383 300.372803 l
182.919797 281.708368 l
183.601211 253.668873 l
184.282625 270.624764 l
184.964039 297.727706 l
185.645453 174.889355 l
186.326867 296.272479 l
187.00828 290.995522 l
187.689694 252.614445 l
188.371108 304.379744 l
189.052522 159.587553 l
189.733936 289.581524 l
190.41535 214.993791 l
191.096764 247.086933 l
191.778178 294.656389 l
192.459592 230.653608 l
193.141006 222.705735 l
193.82242 294.575852 l
194.503834 288.835748 l
195.185248 280.779433 l
195.866662 255.128673 l
196.548076 317.654065 l
197.22949 296.085195 l
197.910904 307.281251 l
198.592318 283.834828 l
199.273732 283.444259 l
199.955146 280.748385 l
200.63656 298.551726 l
201.317974 211.626504 l
201.999388 301.121757 l
202.680802 228.579452 l
203.362216 291.234529 l
204.04363 209.06051 l
204.725044 220.738668 l
205.406458 258.113896 l
206.087871 265.379319 l
206.769285 254.025941 l
207.450699 264.752881 l
208.132113 293.298986 l
208.813527 220.806215 l
209.494941 281.171562 l
210.176355 194.53768 l
210.857769 301.541924 l
211.539183 285.650063 l
212.220597 250.609825 l
212.902011 269.459446 l
213.583425 274.072532 l
214.264839 178.223807 l
214.946253 202.205847 l
216.309081 274.793547 l
216.990495 289.3468 l
217.671909 221.854943 l
218.353323 293.668864 l
219.034737 285.855651 l
219.716151 242.672769 l
220.397565 219.287692 l
221.078979 269.278624 l
221.760393 184.326432 l
222.441807 215.719512 l
223.804635 232.326892 l
224.486049 279.553654 l
225.167462 303.867504 l
225.848876 294.593048 l
226.53029 228.471762 l
227.211704 259.153067 l
227.893118 231.966384 l
228.574532 250.505073 l
229.255946 285.816585 l
229.93736 290.6098 l
230.618774 264.191889 l
231.300188 299.009847 l
232.663016 205.458574 l
233.34443 286.908183 l
234.025844 277.332779 l
234.707258 237.497177 l
235.388672 306.660384 l
236.070086 273.948784 l
236.7515 202.685748 l
237.432914 217.493028 l
238.114328 183.876332 l
238.795742 294.852815 l
239.477156 248.698033 l
240.15857 217.891445 l
240.839984 296.753648 l
241.521398 269.282646 l
242.202812 294.627357 l
242.884226 196.770411 l
243.56564 304.503283 l
244.247053 223.042774 l
244.928467 278.324139 l
245.609881 249.220019 l
246.291295 273.78865 l
246.972709 262.311085 l
247.654123 305.485261 l
248.335537 209.348446 l
249.016951 281.718443 l
249.698365 296.637372 l
250.379779 289.576698 l
251.061193 280.903325 l
251.742607 243.699099 l
252.424021 260.493358 l
253.105435 291.464658 l
253.786849 253.755962 l
254.468263 195.160525 l
255.149677 253.163111 l
255.831091 278.465236 l
256.512505 319.953571 l
257.193919 283.901557 l
257.875333 285.092652 l
258.556747 292.629025 l
259.238161 289.699523 l
259.919575 294.852305 l
260.600989 245.036897 l
261.282403 314.71718 l
261.963817 296.216586 l
262.645231 295.619592 l
263.326644 271.470259 l
264.008058 301.677223 l
264.689472 231.949988 l
265.370886 279.25705 l
266.0523 295.261708 l
266.733714 197.964313 l
267.415128 205.139833 l
268.096542 261.354306 l
268.777956 303.30251 l
269.45937 269.957372 l
270.140784 244.521693 l
270.822198 280.67051 l
271.503612 280.152765 l
272.185026 265.588175 l
272.86644 309.793099 l
273.547854 281.192445 l
274.229268 284.265376 l
274.910682 300.589072 l
275.592096 290.358527 l
276.27351 254.804637 l
276.954924 297.106286 l
277.636338 251.615301 l
278.317752 285.243994 l
278.999166 266.309244 l
279.68058 281.531942 l
280.361994 284.539426 l
281.043408 261.335061 l
281.724822 302.840823 l
282.406235 281.812073 l
283.087649 266.208645 l
283.769063 292.443442 l
284.450477 302.03356 l
285.131891 250.08936 l
285.813305 273.383831 l
286.494719 283.350423 l
287.176133 244.312984 l
287.857547 258.755366 l
288.538961 304.563181 l
289.220375 294.117545 l
289.901789 271.621425 l
290.583203 279.040921 l
291.264617 273.022315 l
291.946031 298.925488 l
292.627445 282.99633 l
293.308859 287.167465 l
293.990273 305.784781 l
294.671687 251.210922 l
295.353101 292.431286 l
296.034515 293.766364 l
296.715929 311.066704 l
297.397343 252.998671 l
298.078757 254.825702 l
298.760171 273.305391 l
299.441585 298.415152 l
300.122999 293.357986 l
300.804413 292.884114 l
301.485826 265.834582 l
302.16724 295.143647 l
302.848654 297.816735 l
303.530068 310.519625 l
304.211482 288.518219 l
304.892896 281.813437 l
305.57431 285.677701 l
306.255724 277.370961 l
306.937138 236.653202 l
307.618552 302.574075 l
308.299966 308.130951 l
308.98138 275.829089 l
309.662794 300.575095 l
310.344208 294.791149 l
311.025622 281.41304 l
311.707036 283.397353 l
312.38845 304.08778 l
313.069864 305.465849 l
313.751278 279.139199 l
314.432692 217.914796 l
315.114106 304.089524 l
315.79552 310.842105 l
316.476934 306.992081 l
317.158348 283.11857 l
317.839762 305.332131 l
318.521176 304.202599 l
319.20259 294.116686 l
319.884004 296.641212 l
320.565417 301.028711 l
321.246831 278.895992 l
321.928245 302.418877 l
322.609659 276.242163 l
323.291073 310.235035 l
323.972487 283.300823 l
324.653901 299.646022 l
325.335315 303.985472 l
326.016729 309.078575 l
326.698143 308.765139 l
327.379557 283.522425 l
328.060971 280.575884 l
328.742385 267.908675 l
329.423799 304.022197 l
330.105213 299.188713 l
330.786627 306.854129 l
331.468041 280.042957 l
332.149455 289.669109 l
332.830869 307.844758 l
333.512283 304.996761 l
334.193697 311.907488 l
334.875111 298.794092 l
335.556525 309.938296 l
336.237939 306.275502 l
336.919353 307.588089 l
337.600767 310.806226 l
338.282181 302.670225 l
338.963595 289.415731 l
339.645008 302.232838 l
340.326422 311.017837 l
341.007836 313.018636 l
341.68925 235.545556 l
342.370664 294.333942 l
343.052078 311.700073 l
343.733492 321.716903 l
344.414906 313.761351 l
345.09632 304.112079 l
345.777734 311.321125 l
346.459148 311.391858 l
347.140562 302.929947 l
347.821976 302.923848 l
348.50339 304.173302 l
349.184804 307.377453 l
349.866218 314.851759 l
350.547632 296.052697 l
351.229046 308.522073 l
351.91046 305.992283 l
352.591874 308.569054 l
353.273288 303.814061 l
353.954702 309.562021 l
354.636116 308.553255 l
355.31753 304.817433 l
355.998944 302.870293 l
356.680358 289.641859 l
357.361772 305.134124 l
358.043186 303.921199 l
358.724599 300.815264 l
360.087427 309.696809 l
360.768841 287.190251 l
361.450255 310.931408 l
362.131669 306.313942 l
362.813083 276.209042 l
363.494497 310.531173 l
364.857325 304.950923 l
365.538739 292.763897 l
366.220153 307.370857 l
366.901567 310.409403 l
367.582981 312.546427 l
368.264395 301.76265 l
368.945809 311.468943 l
369.627223 307.11119 l
370.308637 308.996187 l
370.990051 302.05236 l
371.671465 302.363609 l
372.352879 304.380613 l
373.034293 301.459197 l
373.715707 300.751763 l
374.397121 307.996757 l
375.078535 313.594038 l
375.759949 321.028358 l
377.122777 286.82163 l
377.80419 300.000855 l
378.485604 290.722462 l
379.167018 304.24635 l
379.848432 287.930675 l
380.529846 311.329764 l
381.21126 310.588285 l
381.892674 311.930072 l
382.574088 312.560937 l
383.255502 300.453135 l
383.936916 307.253945 l
384.61833 295.326281 l
385.299744 305.291887 l
385.981158 262.026433 l
386.662572 309.811237 l
388.0254 256.730581 l
388.706814 284.937388 l
389.388228 301.684427 l
390.069642 305.756063 l
390.751056 302.465639 l
391.43247 300.955141 l
392.113884 304.62148 l
392.795298 281.10292 l
393.476712 311.976721 l
394.158126 299.139962 l
394.83954 304.41959 l
395.520954 307.491156 l
396.202368 317.697929 l
396.883781 295.656064 l
397.565195 312.612877 l
398.246609 291.447872 l
398.928023 301.379185 l
399.609437 301.695673 l
400.290851 279.123744 l
400.972265 307.32144 l
401.653679 274.958838 l
402.335093 281.172569 l
403.016507 277.129905 l
403.697921 295.595737 l
404.379335 272.079218 l
405.060749 304.571214 l
405.742163 302.671434 l
406.423577 278.579156 l
407.104991 245.839975 l
407.786405 304.285249 l
409.149233 304.254794 l
409.830647 305.814629 l
410.512061 296.57685 l
411.193475 295.071474 l
411.874889 307.560594 l
412.556303 308.868207 l
413.237717 305.501801 l
413.919131 233.24411 l
414.600545 294.9377 l
415.281959 290.676601 l
415.963372 304.2862 l
416.644786 289.515162 l
417.3262 262.411443 l
418.007614 310.01545 l
418.689028 285.466811 l
419.370442 232.238836 l
420.051856 301.797236 l
420.73327 296.73791 l
421.414684 309.281164 l
422.096098 273.648811 l
422.777512 286.200091 l
423.458926 311.757076 l
424.14034 306.375754 l
424.821754 294.939605 l
425.503168 311.271983 l
426.865996 303.517229 l
427.54741 279.214701 l
428.228824 282.394184 l
428.910238 308.739302 l
430.273066 279.460089 l
430.95448 299.069209 l
431.635894 245.585907 l
432.998722 302.618202 l
432.998722 302.618202 l
stroke
grestore
0.800 setlinewidth
0 setlinejoin
0.000 setgray
gsave
75.971875 46.971875 m
75.971875 334.8 l
stroke
grestore
gsave
450 46.971875 m
450 334.8 l
stroke
grestore
gsave
75.971875 46.971875 m
450 46.971875 l
stroke
grestore
gsave
75.971875 334.8 m
450 334.8 l
stroke
grestore

end
showpage
