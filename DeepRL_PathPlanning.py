#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准DQN路径规划实现
深度Q网络用于海冰环境中的路径规划
"""

import numpy as np
import random
import matplotlib.pyplot as plt
from collections import deque
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 尝试导入PyTorch，如果失败则抛出错误
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
    print("✅ PyTorch导入成功")
except ImportError as e:
    raise ImportError(f"PyTorch未安装或不可用: {e}. 请安装PyTorch或使用简化版DQN")

class SimpleDQNNetwork(nn.Module):
    """标准DQN网络 - 优化适配二值地图"""
    def __init__(self, action_size=8):
        super(SimpleDQNNetwork, self).__init__()
        # 方案1: 局部观察窗口 + 全局信息
        # 局部观察: 32x32单通道
        self.local_conv1 = nn.Conv2d(1, 16, kernel_size=4, stride=2)  # 16x15x15
        self.local_conv2 = nn.Conv2d(16, 32, kernel_size=3, stride=2)  # 32x7x7

        # 全局信息: 位置编码 + 目标方向
        self.global_fc = nn.Linear(4, 64)  # [x, y, goal_x, goal_y] 归一化

        # 融合层
        local_features = 32 * 7 * 7  # 1568
        self.fusion_fc1 = nn.Linear(local_features + 64, 256)
        self.fusion_fc2 = nn.Linear(256, 128)
        self.output_fc = nn.Linear(128, action_size)

    def forward(self, local_map, global_info):
        # 处理局部地图
        local_x = F.relu(self.local_conv1(local_map))
        local_x = F.relu(self.local_conv2(local_x))
        local_x = local_x.view(local_x.size(0), -1)

        # 处理全局信息
        global_x = F.relu(self.global_fc(global_info))

        # 融合特征
        x = torch.cat([local_x, global_x], dim=1)
        x = F.relu(self.fusion_fc1(x))
        x = F.relu(self.fusion_fc2(x))
        return self.output_fc(x)

class ReplayBuffer:
    def __init__(self, capacity=10000):
        self.buffer = deque(maxlen=capacity)

    def push(self, state, action, reward, next_state, done):
        # state和next_state现在是(local_map, global_info)的元组
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        batch = random.sample(self.buffer, batch_size)

        # 分别处理local_map和global_info
        states, actions, rewards, next_states, dones = zip(*batch)

        # 分离local_map和global_info
        local_maps = np.stack([s[0] for s in states])
        global_infos = np.stack([s[1] for s in states])
        next_local_maps = np.stack([s[0] for s in next_states])
        next_global_infos = np.stack([s[1] for s in next_states])

        actions = np.array(actions)
        rewards = np.array(rewards)
        dones = np.array(dones)

        return (local_maps, global_infos), actions, rewards, (next_local_maps, next_global_infos), dones

    def __len__(self):
        return len(self.buffer)

class ClassicDQN:
    def __init__(self, start, goal, grid_map,
                 episodes=200, learning_rate=0.0005, epsilon_decay=0.998,
                 memory_size=20000, local_window=32, epsilon_min=0.05,
                 gamma=0.95, batch_size=64, target_update_freq=5):
        """
        初始化DQN路径规划器

        参数说明:
        - episodes: 训练回合数 [100-500]
        - learning_rate: 学习率 [0.0001-0.001]
        - epsilon_decay: 探索衰减率 [0.995-0.999]
        - memory_size: 经验回放缓冲区大小 [10000-50000]
        - local_window: 局部观察窗口大小 [24-48]
        - epsilon_min: 最小探索率 [0.01-0.1]
        - gamma: 折扣因子 [0.9-0.99]
        - batch_size: 批次大小 [32-128]
        - target_update_freq: 目标网络更新频率 [5-20]
        """
        print(f"初始化DQN: 起点{start}, 终点{goal}, 地图尺寸{np.array(grid_map).shape}")

        self.start = start
        self.goal = goal
        self.grid_map = np.array(grid_map)

        # 验证输入参数
        if not self.is_valid_position(start):
            raise ValueError(f"起始位置 {start} 无效或被障碍物占据")
        if not self.is_valid_position(goal):
            raise ValueError(f"目标位置 {goal} 无效或被障碍物占据")

        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {self.device}")

        # 手动设置的超参数
        self.episodes = episodes
        self.learning_rate = learning_rate
        self.epsilon = 1.0  # 初始探索率
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.gamma = gamma
        self.batch_size = batch_size
        self.target_update_freq = target_update_freq
        self.local_window_size = local_window

        print(f"参数设置: episodes={episodes}, lr={learning_rate}, window={local_window}×{local_window}")

        self.q_network = SimpleDQNNetwork().to(self.device)
        self.target_network = SimpleDQNNetwork().to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)
        self.memory = ReplayBuffer(memory_size)

        # 8个方向的动作：上下左右和对角线
        self.actions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]
        self.episode_rewards = []

        self.update_target_network()
        print("DQN初始化完成")

    def update_target_network(self):
        self.target_network.load_state_dict(self.q_network.state_dict())

    def preprocess_state(self, position):
        """标准DQN状态预处理 - 局部观察 + 全局信息融合"""
        x, y = position

        # 1. 局部观察窗口 (使用配置中的窗口大小)
        window_size = self.local_window_size
        half_window = window_size // 2

        local_map = np.zeros((1, window_size, window_size), dtype=np.float32)

        # 提取局部区域
        start_x = max(0, x - half_window)
        end_x = min(self.grid_map.shape[0], x + half_window)
        start_y = max(0, y - half_window)
        end_y = min(self.grid_map.shape[1], y + half_window)

        # 计算在local_map中的位置
        local_start_x = half_window - (x - start_x)
        local_end_x = local_start_x + (end_x - start_x)
        local_start_y = half_window - (y - start_y)
        local_end_y = local_start_y + (end_y - start_y)

        # 填充局部地图
        local_map[0, local_start_x:local_end_x, local_start_y:local_end_y] = \
            self.grid_map[start_x:end_x, start_y:end_y]

        # 2. 全局信息 (归一化位置 + 目标方向)
        map_h, map_w = self.grid_map.shape
        global_info = np.array([
            x / map_h,  # 当前x位置 (归一化)
            y / map_w,  # 当前y位置 (归一化)
            self.goal[0] / map_h,  # 目标x位置 (归一化)
            self.goal[1] / map_w   # 目标y位置 (归一化)
        ], dtype=np.float32)

        return local_map, global_info

    def get_action(self, state):
        if random.random() < self.epsilon:
            return random.randint(0, 7)

        local_map, global_info = state
        local_tensor = torch.FloatTensor(local_map).unsqueeze(0).to(self.device)
        global_tensor = torch.FloatTensor(global_info).unsqueeze(0).to(self.device)

        q_values = self.q_network(local_tensor, global_tensor)
        return q_values.argmax().item()

    def is_valid_position(self, pos):
        x, y = pos
        if x < 0 or x >= self.grid_map.shape[0] or y < 0 or y >= self.grid_map.shape[1]:
            return False
        return self.grid_map[x, y] == 0

    def get_reward(self, current_pos, next_pos, done):
        """
        改进的奖励函数 - 针对海冰路径规划优化
        """
        # 到达目标的巨大正奖励
        if done and next_pos == self.goal:
            return 100.0

        # 撞墙或越界的负奖励
        if not self.is_valid_position(next_pos):
            return -10.0

        # 基于距离的奖励
        current_dist = np.linalg.norm(np.array(current_pos) - np.array(self.goal))
        next_dist = np.linalg.norm(np.array(next_pos) - np.array(self.goal))
        distance_reward = (current_dist - next_dist) * 2.0

        # 时间惩罚
        time_penalty = -0.01

        # 海冰密度风险惩罚 (如果有风险地图)
        risk_penalty = 0
        if hasattr(self, 'risk_map') and self.risk_map is not None:
            x, y = next_pos
            if 0 <= x < self.risk_map.shape[0] and 0 <= y < self.risk_map.shape[1]:
                risk_penalty = -self.risk_map[x, y] * 0.5

        # 接近目标奖励
        if next_dist < 5.0:
            distance_reward += 5.0

        # 路径平滑度奖励 (避免频繁转向)
        smoothness_reward = 0
        if hasattr(self, 'last_action') and hasattr(self, 'current_action'):
            if self.last_action == self.current_action:
                smoothness_reward = 0.1

        return distance_reward + time_penalty + risk_penalty + smoothness_reward

    def step(self, current_pos, action):
        dx, dy = self.actions[action]
        next_pos = (current_pos[0] + dx, current_pos[1] + dy)
        done = (next_pos == self.goal)

        if not self.is_valid_position(next_pos):
            next_pos = current_pos

        reward = self.get_reward(current_pos, next_pos, done)
        return next_pos, reward, done

    def train(self, episodes=200):
        print(f"开始DQN训练，回合数: {episodes}")

        # 训练统计
        success_count = 0
        best_reward = float('-inf')

        for episode in range(episodes):
            current_pos = self.start
            state = self.preprocess_state(current_pos)
            total_reward = 0
            steps = 0
            max_steps = 500  # 增加最大步数

            for step in range(max_steps):
                action = self.get_action(state)
                next_pos, reward, done = self.step(current_pos, action)
                next_state = self.preprocess_state(next_pos)

                self.memory.push(state, action, reward, next_state, done)

                # 开始训练网络（当有足够经验时）
                if len(self.memory) > self.batch_size:
                    self.replay()

                state = next_state
                current_pos = next_pos
                total_reward += reward
                steps += 1

                if done:
                    success_count += 1
                    break

            self.episode_rewards.append(total_reward)

            # 更新最佳奖励
            if total_reward > best_reward:
                best_reward = total_reward

            # Epsilon衰减
            if self.epsilon > self.epsilon_min:
                self.epsilon *= self.epsilon_decay

            # 使用优化的目标网络更新频率
            if episode % self.target_update_freq == 0:
                self.update_target_network()

            # 打印训练进度
            if episode % 50 == 0:
                avg_reward = np.mean(self.episode_rewards[-50:]) if len(self.episode_rewards) >= 50 else np.mean(self.episode_rewards)
                success_rate = success_count / (episode + 1) * 100
                print(f"回合 {episode}/{episodes}, 平均奖励: {avg_reward:.2f}, 最佳奖励: {best_reward:.2f}, "
                      f"成功率: {success_rate:.1f}%, Epsilon: {self.epsilon:.3f}")

        # 训练完成统计
        final_success_rate = success_count / episodes * 100
        print(f"DQN训练完成! 总成功率: {final_success_rate:.1f}% ({success_count}/{episodes})")
        print(f"最佳奖励: {best_reward:.2f}")

        return self.episode_rewards

    def replay(self):
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)

        # 解包状态
        local_maps, global_infos = states
        next_local_maps, next_global_infos = next_states

        # 转换为张量
        local_maps = torch.FloatTensor(local_maps).to(self.device)
        global_infos = torch.FloatTensor(global_infos).to(self.device)
        next_local_maps = torch.FloatTensor(next_local_maps).to(self.device)
        next_global_infos = torch.FloatTensor(next_global_infos).to(self.device)

        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)

        # 计算当前Q值
        current_q_values = self.q_network(local_maps, global_infos).gather(1, actions.unsqueeze(1))

        # 计算目标Q值
        with torch.no_grad():
            next_q_values = self.target_network(next_local_maps, next_global_infos).max(1)[0].detach()
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)

        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)

        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

    def plan_path(self, max_steps=1000):
        """
        使用训练好的DQN网络进行路径规划
        """
        print(f"开始DQN路径规划，从 {self.start} 到 {self.goal}")

        path = [self.start]
        current_pos = self.start
        visited = {}  # 记录访问次数
        visited[self.start] = 1

        # 设置贪婪策略（不再探索）
        original_epsilon = self.epsilon
        self.epsilon = 0.0  # 完全贪婪

        stuck_count = 0
        max_stuck = 5  # 允许的最大重复次数

        for step in range(max_steps):
            state = self.preprocess_state(current_pos)
            action = self.get_action(state)

            next_pos, reward, done = self.step(current_pos, action)

            # 检查是否到达目标
            if done and next_pos == self.goal:
                path.append(next_pos)
                print(f"✅ DQN找到完整路径，长度: {len(path)}")
                self.epsilon = original_epsilon  # 恢复原始epsilon
                return path

            # 检查是否有效移动
            if next_pos == current_pos:
                stuck_count += 1
                if stuck_count > max_stuck:
                    print(f"⚠️ DQN在位置 {current_pos} 卡住，停止规划")
                    break
            else:
                stuck_count = 0

            # 记录访问次数和处理循环
            if next_pos in visited:
                visited[next_pos] += 1
                # 如果某个位置访问次数过多，可能陷入循环
                if visited[next_pos] > 3:
                    print(f"⚠️ 检测到循环，位置 {next_pos} 已访问 {visited[next_pos]} 次")
                    # 尝试启发式动作打破循环
                    if step < max_steps - 100:  # 还有足够步数时才尝试
                        heuristic_action = self._get_heuristic_action(current_pos)
                        next_pos, reward, done = self.step(current_pos, heuristic_action)
                        if next_pos == current_pos:  # 启发式动作也失败
                            # 尝试逃离动作
                            escape_action = self._get_escape_action(current_pos, visited)
                            next_pos, reward, done = self.step(current_pos, escape_action)
            else:
                visited[next_pos] = 1

            path.append(next_pos)
            current_pos = next_pos

            # 每100步打印进度并检查是否可以直接到达目标
            if step % 100 == 0 and step > 0:
                distance_to_goal = np.linalg.norm(np.array(current_pos) - np.array(self.goal))
                print(f"步骤 {step}: 当前位置 {current_pos}, 距离目标 {distance_to_goal:.2f}")

                # 如果距离目标很近，尝试直接路径
                if distance_to_goal < 20:
                    print("🎯 接近目标，尝试直接路径")
                    direct_path = self._try_direct_path(current_pos, self.goal)
                    if direct_path:
                        path.extend(direct_path[1:])  # 排除当前位置
                        path.append(self.goal)
                        print(f"✅ 找到直接路径到目标，总长度: {len(path)}")
                        self.epsilon = original_epsilon
                        return path

        # 恢复原始epsilon
        self.epsilon = original_epsilon

        # 即使未找到完整路径，也返回探索的部分路径
        if len(path) > 1:
            distance_to_goal = np.linalg.norm(np.array(path[-1]) - np.array(self.goal))
            print(f"⚠️ DQN未找到完整路径，返回部分探索路径")
            print(f"   路径长度: {len(path)}, 最终距离目标: {distance_to_goal:.2f}")
            return path
        else:
            print("❌ DQN未能进行有效探索")
            return None

    def _get_heuristic_action(self, current_pos):
        """
        获取启发式动作：朝向目标方向
        """
        dx = self.goal[0] - current_pos[0]
        dy = self.goal[1] - current_pos[1]

        # 归一化方向
        if abs(dx) > abs(dy):
            dx = 1 if dx > 0 else -1
            dy = 0 if abs(dy) < abs(dx) / 2 else (1 if dy > 0 else -1)
        else:
            dy = 1 if dy > 0 else -1
            dx = 0 if abs(dx) < abs(dy) / 2 else (1 if dx > 0 else -1)

        # 找到对应的动作索引
        direction = (dx, dy)
        try:
            return self.actions.index(direction)
        except ValueError:
            # 如果找不到精确匹配，返回最接近的动作
            return 0

    def _get_escape_action(self, current_pos, visited):
        """
        获取逃离动作：选择访问次数最少的方向
        """
        best_action = 0
        min_visits = float('inf')

        for i, (dx, dy) in enumerate(self.actions):
            next_pos = (current_pos[0] + dx, current_pos[1] + dy)
            if self.is_valid_position(next_pos):
                visits = visited.get(next_pos, 0)
                if visits < min_visits:
                    min_visits = visits
                    best_action = i

        return best_action

    def _try_direct_path(self, start_pos, goal_pos):
        """
        尝试从当前位置到目标的直接路径
        """
        path = []
        current = list(start_pos)
        goal = list(goal_pos)

        max_attempts = 50
        for _ in range(max_attempts):
            if tuple(current) == tuple(goal):
                path.append(tuple(current))
                return path

            # 计算方向
            dx = goal[0] - current[0]
            dy = goal[1] - current[1]

            # 选择移动方向
            if abs(dx) > abs(dy):
                step_x = 1 if dx > 0 else -1
                step_y = 0
            else:
                step_x = 0
                step_y = 1 if dy > 0 else -1

            next_pos = (current[0] + step_x, current[1] + step_y)

            if not self.is_valid_position(next_pos):
                return None  # 直接路径被阻挡

            path.append(tuple(current))
            current = list(next_pos)

        return None  # 超过最大尝试次数

    def visualize_training(self):
        if not self.episode_rewards:
            return

        plt.figure(figsize=(10, 6))
        plt.plot(self.episode_rewards)
        plt.title('DQN训练奖励')
        plt.xlabel('回合')
        plt.ylabel('总奖励')
        plt.grid(True)
        plt.show()
