%!PS-Adobe-3.0 EPSF-3.0
%%Title: Q_learning_Accumulated_Reward.eps
%%Creator: Matplotlib v3.7.5, https://matplotlib.org/
%%CreationDate: Sat Feb 24 16:04:50 2024
%%Orientation: portrait
%%BoundingBox: 75 223 537 569
%%HiResBoundingBox: 75.600000 223.200000 536.400000 568.800000
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Matplotlib.
10 dict begin
/FontName /DejaVuS<PERSON> def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-2090 -948 3673 2524] def
/FontType 3 def
/Encoding [/minus /space /zero /one /two /three /four /five /six /eight /A /E /a /c /d /e /i /l /m /o /p /r /s /t /u /w] def
/CharStrings 27 dict dup begin
/.notdef 0 def
/minus{1716 0 217 557 1499 727 sc
217 727 m
1499 727 l
1499 557 l
217 557 l
217 727 l

ce} _d
/space{651 0 0 0 0 0 sc
ce} _d
/zero{1303 0 135 -29 1167 1520 sc
651 1360 m
547 1360 469 1309 416 1206 c
364 1104 338 950 338 745 c
338 540 364 387 416 284 c
469 182 547 131 651 131 c
756 131 834 182 886 284 c
939 387 965 540 965 745 c
965 950 939 1104 886 1206 c
834 1309 756 1360 651 1360 c

651 1520 m
818 1520 946 1454 1034 1321 c
1123 1189 1167 997 1167 745 c
1167 494 1123 302 1034 169 c
946 37 818 -29 651 -29 c
484 -29 356 37 267 169 c
179 302 135 494 135 745 c
135 997 179 1189 267 1321 c
356 1454 484 1520 651 1520 c

ce} _d
/one{1303 0 225 0 1114 1493 sc
254 170 m
584 170 l
584 1309 l
225 1237 l
225 1421 l
582 1493 l
784 1493 l
784 170 l
1114 170 l
1114 0 l
254 0 l
254 170 l

ce} _d
/two{1303 0 150 0 1098 1520 sc
393 170 m
1098 170 l
1098 0 l
150 0 l
150 170 l
227 249 331 356 463 489 c
596 623 679 709 713 748 c
778 821 823 882 848 932 c
874 983 887 1032 887 1081 c
887 1160 859 1225 803 1275 c
748 1325 675 1350 586 1350 c
523 1350 456 1339 385 1317 c
315 1295 240 1262 160 1217 c
160 1421 l
241 1454 317 1478 388 1495 c
459 1512 523 1520 582 1520 c
737 1520 860 1481 952 1404 c
1044 1327 1090 1223 1090 1094 c
1090 1033 1078 974 1055 919 c
1032 864 991 800 930 725 c
913 706 860 650 771 557 c
682 465 556 336 393 170 c

ce} _d
/three{1303 0 156 -29 1139 1520 sc
831 805 m
928 784 1003 741 1057 676 c
1112 611 1139 530 1139 434 c
1139 287 1088 173 987 92 c
886 11 742 -29 555 -29 c
492 -29 428 -23 361 -10 c
295 2 227 20 156 45 c
156 240 l
212 207 273 183 340 166 c
407 149 476 141 549 141 c
676 141 772 166 838 216 c
905 266 938 339 938 434 c
938 522 907 591 845 640 c
784 690 698 715 588 715 c
414 715 l
414 881 l
596 881 l
695 881 771 901 824 940 c
877 980 903 1037 903 1112 c
903 1189 876 1247 821 1288 c
767 1329 689 1350 588 1350 c
533 1350 473 1344 410 1332 c
347 1320 277 1301 201 1276 c
201 1456 l
278 1477 349 1493 416 1504 c
483 1515 547 1520 606 1520 c
759 1520 881 1485 970 1415 c
1059 1346 1104 1252 1104 1133 c
1104 1050 1080 980 1033 923 c
986 866 918 827 831 805 c

ce} _d
/four{1303 0 100 0 1188 1493 sc
774 1317 m
264 520 l
774 520 l
774 1317 l

721 1493 m
975 1493 l
975 520 l
1188 520 l
1188 352 l
975 352 l
975 0 l
774 0 l
774 352 l
100 352 l
100 547 l
721 1493 l

ce} _d
/five{1303 0 158 -29 1124 1493 sc
221 1493 m
1014 1493 l
1014 1323 l
406 1323 l
406 957 l
435 967 465 974 494 979 c
523 984 553 987 582 987 c
749 987 881 941 978 850 c
1075 759 1124 635 1124 479 c
1124 318 1074 193 974 104 c
874 15 733 -29 551 -29 c
488 -29 424 -24 359 -13 c
294 -2 227 14 158 35 c
158 238 l
218 205 280 181 344 165 c
408 149 476 141 547 141 c
662 141 754 171 821 232 c
888 293 922 375 922 479 c
922 583 888 665 821 726 c
754 787 662 817 547 817 c
493 817 439 811 385 799 c
332 787 277 768 221 743 c
221 1493 l

ce} _d
/six{1303 0 143 -29 1174 1520 sc
676 827 m
585 827 513 796 460 734 c
407 672 381 587 381 479 c
381 372 407 287 460 224 c
513 162 585 131 676 131 c
767 131 838 162 891 224 c
944 287 971 372 971 479 c
971 587 944 672 891 734 c
838 796 767 827 676 827 c

1077 1460 m
1077 1276 l
1026 1300 975 1318 923 1331 c
872 1344 821 1350 770 1350 c
637 1350 535 1305 464 1215 c
394 1125 354 989 344 807 c
383 865 433 909 492 940 c
551 971 617 987 688 987 c
838 987 956 941 1043 850 c
1130 759 1174 636 1174 479 c
1174 326 1129 203 1038 110 c
947 17 827 -29 676 -29 c
503 -29 371 37 280 169 c
189 302 143 494 143 745 c
143 981 199 1169 311 1309 c
423 1450 573 1520 762 1520 c
813 1520 864 1515 915 1505 c
967 1495 1021 1480 1077 1460 c

ce} _d
/eight{1303 0 139 -29 1163 1520 sc
651 709 m
555 709 479 683 424 632 c
369 581 342 510 342 420 c
342 330 369 259 424 208 c
479 157 555 131 651 131 c
747 131 823 157 878 208 c
933 260 961 331 961 420 c
961 510 933 581 878 632 c
823 683 748 709 651 709 c

449 795 m
362 816 295 857 246 916 c
198 975 174 1048 174 1133 c
174 1252 216 1347 301 1416 c
386 1485 503 1520 651 1520 c
800 1520 916 1485 1001 1416 c
1086 1347 1128 1252 1128 1133 c
1128 1048 1104 975 1055 916 c
1007 857 940 816 854 795 c
951 772 1027 728 1081 662 c
1136 596 1163 515 1163 420 c
1163 275 1119 164 1030 87 c
942 10 816 -29 651 -29 c
486 -29 360 10 271 87 c
183 164 139 275 139 420 c
139 515 166 596 221 662 c
276 728 352 772 449 795 c

375 1114 m
375 1037 399 976 447 933 c
496 890 564 868 651 868 c
738 868 805 890 854 933 c
903 976 928 1037 928 1114 c
928 1191 903 1252 854 1295 c
805 1338 738 1360 651 1360 c
564 1360 496 1338 447 1295 c
399 1252 375 1191 375 1114 c

ce} _d
/A{1401 0 16 0 1384 1493 sc
700 1294 m
426 551 l
975 551 l
700 1294 l

586 1493 m
815 1493 l
1384 0 l
1174 0 l
1038 383 l
365 383 l
229 0 l
16 0 l
586 1493 l

ce} _d
/E{1294 0 201 0 1163 1493 sc
201 1493 m
1145 1493 l
1145 1323 l
403 1323 l
403 881 l
1114 881 l
1114 711 l
403 711 l
403 170 l
1163 170 l
1163 0 l
201 0 l
201 1493 l

ce} _d
/a{1255 0 123 -29 1069 1147 sc
702 563 m
553 563 450 546 393 512 c
336 478 307 420 307 338 c
307 273 328 221 371 182 c
414 144 473 125 547 125 c
649 125 731 161 792 233 c
854 306 885 402 885 522 c
885 563 l
702 563 l

1069 639 m
1069 0 l
885 0 l
885 170 l
843 102 791 52 728 19 c
665 -13 589 -29 498 -29 c
383 -29 292 3 224 67 c
157 132 123 218 123 326 c
123 452 165 547 249 611 c
334 675 460 707 627 707 c
885 707 l
885 725 l
885 810 857 875 801 921 c
746 968 668 991 567 991 c
503 991 441 983 380 968 c
319 953 261 930 205 899 c
205 1069 l
272 1095 338 1114 401 1127 c
464 1140 526 1147 586 1147 c
748 1147 869 1105 949 1021 c
1029 937 1069 810 1069 639 c

ce} _d
/c{1126 0 113 -29 999 1147 sc
999 1077 m
999 905 l
947 934 895 955 842 969 c
790 984 737 991 684 991 c
565 991 472 953 406 877 c
340 802 307 696 307 559 c
307 422 340 316 406 240 c
472 165 565 127 684 127 c
737 127 790 134 842 148 c
895 163 947 184 999 213 c
999 43 l
948 19 894 1 839 -11 c
784 -23 726 -29 664 -29 c
495 -29 361 24 262 130 c
163 236 113 379 113 559 c
113 742 163 885 263 990 c
364 1095 501 1147 676 1147 c
733 1147 788 1141 842 1129 c
896 1118 948 1100 999 1077 c

ce} _d
/d{1300 0 113 -29 1114 1556 sc
930 950 m
930 1556 l
1114 1556 l
1114 0 l
930 0 l
930 168 l
891 101 842 52 783 19 c
724 -13 654 -29 571 -29 c
436 -29 325 25 240 133 c
155 241 113 383 113 559 c
113 735 155 877 240 985 c
325 1093 436 1147 571 1147 c
654 1147 724 1131 783 1098 c
842 1066 891 1017 930 950 c

303 559 m
303 424 331 317 386 240 c
442 163 519 125 616 125 c
713 125 790 163 846 240 c
902 317 930 424 930 559 c
930 694 902 800 846 877 c
790 954 713 993 616 993 c
519 993 442 954 386 877 c
331 800 303 694 303 559 c

ce} _d
/e{1260 0 113 -29 1151 1147 sc
1151 606 m
1151 516 l
305 516 l
313 389 351 293 419 226 c
488 160 583 127 705 127 c
776 127 844 136 910 153 c
977 170 1043 196 1108 231 c
1108 57 l
1042 29 974 8 905 -7 c
836 -22 765 -29 694 -29 c
515 -29 374 23 269 127 c
165 231 113 372 113 549 c
113 732 162 878 261 985 c
360 1093 494 1147 662 1147 c
813 1147 932 1098 1019 1001 c
1107 904 1151 773 1151 606 c

967 660 m
966 761 937 841 882 901 c
827 961 755 991 664 991 c
561 991 479 962 417 904 c
356 846 320 764 311 659 c
967 660 l

ce} _d
/i{569 0 193 0 377 1556 sc
193 1120 m
377 1120 l
377 0 l
193 0 l
193 1120 l

193 1556 m
377 1556 l
377 1323 l
193 1323 l
193 1556 l

ce} _d
/l{569 0 193 0 377 1556 sc
193 1556 m
377 1556 l
377 0 l
193 0 l
193 1556 l

ce} _d
/m{1995 0 186 0 1821 1147 sc
1065 905 m
1111 988 1166 1049 1230 1088 c
1294 1127 1369 1147 1456 1147 c
1573 1147 1663 1106 1726 1024 c
1789 943 1821 827 1821 676 c
1821 0 l
1636 0 l
1636 670 l
1636 777 1617 857 1579 909 c
1541 961 1483 987 1405 987 c
1310 987 1234 955 1179 892 c
1124 829 1096 742 1096 633 c
1096 0 l
911 0 l
911 670 l
911 778 892 858 854 909 c
816 961 757 987 678 987 c
584 987 509 955 454 891 c
399 828 371 742 371 633 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
413 1015 463 1065 522 1098 c
581 1131 650 1147 731 1147 c
812 1147 881 1126 938 1085 c
995 1044 1038 984 1065 905 c

ce} _d
/o{1253 0 113 -29 1141 1147 sc
627 991 m
528 991 450 952 393 875 c
336 798 307 693 307 559 c
307 425 335 319 392 242 c
449 165 528 127 627 127 c
725 127 803 166 860 243 c
917 320 946 426 946 559 c
946 692 917 797 860 874 c
803 952 725 991 627 991 c

627 1147 m
787 1147 913 1095 1004 991 c
1095 887 1141 743 1141 559 c
1141 376 1095 232 1004 127 c
913 23 787 -29 627 -29 c
466 -29 340 23 249 127 c
158 232 113 376 113 559 c
113 743 158 887 249 991 c
340 1095 466 1147 627 1147 c

ce} _d
/p{1300 0 186 -426 1188 1147 sc
371 168 m
371 -426 l
186 -426 l
186 1120 l
371 1120 l
371 950 l
410 1017 458 1066 517 1098 c
576 1131 647 1147 729 1147 c
865 1147 975 1093 1060 985 c
1145 877 1188 735 1188 559 c
1188 383 1145 241 1060 133 c
975 25 865 -29 729 -29 c
647 -29 576 -13 517 19 c
458 52 410 101 371 168 c

997 559 m
997 694 969 800 913 877 c
858 954 781 993 684 993 c
587 993 510 954 454 877 c
399 800 371 694 371 559 c
371 424 399 317 454 240 c
510 163 587 125 684 125 c
781 125 858 163 913 240 c
969 317 997 424 997 559 c

ce} _d
/r{842 0 186 0 842 1147 sc
842 948 m
821 960 799 969 774 974 c
750 980 723 983 694 983 c
590 983 510 949 454 881 c
399 814 371 717 371 590 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
410 1014 460 1064 522 1097 c
584 1130 659 1147 748 1147 c
761 1147 775 1146 790 1144 c
805 1143 822 1140 841 1137 c
842 948 l

ce} _d
/s{1067 0 111 -29 967 1147 sc
907 1087 m
907 913 l
855 940 801 960 745 973 c
689 986 631 993 571 993 c
480 993 411 979 365 951 c
320 923 297 881 297 825 c
297 782 313 749 346 724 c
379 700 444 677 543 655 c
606 641 l
737 613 829 573 884 522 c
939 471 967 400 967 309 c
967 205 926 123 843 62 c
761 1 648 -29 504 -29 c
444 -29 381 -23 316 -11 c
251 0 183 18 111 41 c
111 231 l
179 196 246 169 312 151 c
378 134 443 125 508 125 c
595 125 661 140 708 169 c
755 199 778 241 778 295 c
778 345 761 383 727 410 c
694 437 620 462 506 487 c
442 502 l
328 526 246 563 195 612 c
144 662 119 730 119 817 c
119 922 156 1004 231 1061 c
306 1118 412 1147 549 1147 c
617 1147 681 1142 741 1132 c
801 1122 856 1107 907 1087 c

ce} _d
/t{803 0 55 0 754 1438 sc
375 1438 m
375 1120 l
754 1120 l
754 977 l
375 977 l
375 369 l
375 278 387 219 412 193 c
437 167 488 154 565 154 c
754 154 l
754 0 l
565 0 l
423 0 325 26 271 79 c
217 132 190 229 190 369 c
190 977 l
55 977 l
55 1120 l
190 1120 l
190 1438 l
375 1438 l

ce} _d
/u{1298 0 174 -29 1112 1147 sc
174 442 m
174 1120 l
358 1120 l
358 449 l
358 343 379 263 420 210 c
461 157 523 131 606 131 c
705 131 784 163 841 226 c
899 289 928 376 928 485 c
928 1120 l
1112 1120 l
1112 0 l
928 0 l
928 172 l
883 104 831 53 772 20 c
713 -13 645 -29 567 -29 c
438 -29 341 11 274 91 c
207 171 174 288 174 442 c

637 1147 m
637 1147 l

ce} _d
/w{1675 0 86 0 1589 1120 sc
86 1120 m
270 1120 l
500 246 l
729 1120 l
946 1120 l
1176 246 l
1405 1120 l
1589 1120 l
1296 0 l
1079 0 l
838 918 l
596 0 l
379 0 l
86 1120 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
75.6 223.2 translate
460.8 345.6 0 0 clipbox
gsave
0 0 m
460.8 0 l
460.8 345.6 l
0 345.6 l
cl
1.000 setgray
fill
grestore
gsave
75.971875 46.971875 m
450 46.971875 l
450 334.8 l
75.971875 334.8 l
cl
1.000 setgray
fill
grestore
0.800 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
92.9732 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

89.1528 30.8469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
161.115 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

149.654 30.8469 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
229.256 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

217.795 30.8469 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
297.397 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

285.936 30.8469 translate
0 rotate
0 0 m /three glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
365.539 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

354.078 30.8469 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
433.68 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

422.219 30.8469 translate
0 rotate
0 0 m /five glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

235.494 13.7063 translate
0 rotate
0 0 m /E glyphshow
8.8457 0 m /p glyphshow
17.7324 0 m /i glyphshow
21.6221 0 m /s glyphshow
28.916 0 m /o glyphshow
37.4814 0 m /d glyphshow
46.3682 0 m /e glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 58.8747 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 54.3122 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /four glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 94.4175 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 89.855 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /two glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 129.96 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 125.398 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /one glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
32.959 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 165.503 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 160.941 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /eight glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 201.046 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 196.483 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /six glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 236.589 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 232.026 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /four glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 272.131 o
grestore
/DejaVuSans 12.000 selectfont
gsave

35.9875 267.569 translate
0 rotate
0 0 m /minus glyphshow
10.0547 0 m /two glyphshow
17.6895 0 m /zero glyphshow
25.3242 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
75.9719 307.674 o
grestore
/DejaVuSans 12.000 selectfont
gsave

61.3312 303.112 translate
0 rotate
0 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

21.4406 118.605 translate
90 rotate
0 0 m /A glyphshow
9.32715 0 m /c glyphshow
17.0244 0 m /c glyphshow
24.7217 0 m /u glyphshow
33.5947 0 m /m glyphshow
47.2324 0 m /u glyphshow
56.1055 0 m /l glyphshow
59.9951 0 m /a glyphshow
68.5742 0 m /t glyphshow
74.0635 0 m /e glyphshow
82.6768 0 m /d glyphshow
91.5635 0 m /space glyphshow
96.0137 0 m /r glyphshow
101.52 0 m /e glyphshow
110.133 0 m /w glyphshow
121.583 0 m /a glyphshow
130.162 0 m /r glyphshow
135.668 0 m /d glyphshow
grestore
1.500 setlinewidth
2 setlinecap
0.000 0.000 1.000 setrgbcolor
gsave
374.028 287.828 75.972 46.972 clipbox
92.973153 296.800779 m
93.654567 287.931964 l
94.335981 303.443904 l
95.017395 296.362607 l
95.698809 257.850846 l
96.380223 258.354079 l
97.061637 288.919407 l
97.743051 191.311902 l
98.424465 198.75941 l
99.105879 203.454555 l
99.787293 190.570115 l
100.468707 217.743747 l
101.150121 230.059119 l
101.831535 255.082342 l
102.512949 215.813855 l
103.194363 156.219557 l
103.875777 293.403028 l
104.557191 197.717859 l
105.238605 214.454373 l
105.920019 201.55337 l
106.601433 223.101977 l
107.282847 194.735535 l
107.964261 213.858886 l
108.645675 178.433538 l
109.327089 247.450983 l
110.008503 231.094102 l
110.689916 60.054972 l
111.37133 177.166447 l
112.052744 261.192128 l
113.415572 238.952816 l
114.096986 204.701607 l
114.7784 177.521267 l
115.459814 262.293698 l
116.141228 198.609271 l
116.822642 164.592241 l
117.504056 165.952421 l
118.18547 128.709196 l
118.866884 224.595343 l
119.548298 217.443005 l
120.229712 213.449835 l
120.911126 189.918449 l
121.59254 200.191193 l
122.273954 295.019097 l
122.955368 209.839752 l
123.636782 253.983218 l
124.318196 245.650684 l
124.99961 171.541373 l
125.681024 129.637162 l
126.362438 167.353298 l
127.043852 181.816449 l
127.725266 225.781833 l
128.40668 194.354895 l
129.088094 216.28572 l
129.769507 246.681595 l
130.450921 227.689818 l
131.132335 152.164515 l
131.813749 236.791703 l
133.176577 133.211103 l
133.857991 276.417033 l
134.539405 273.289725 l
135.220819 274.906213 l
135.902233 211.097168 l
136.583647 159.92846 l
137.265061 202.064085 l
137.946475 263.050827 l
138.627889 198.282113 l
139.309303 254.556659 l
140.672131 82.511405 l
141.353545 289.62164 l
142.034959 265.241044 l
142.716373 267.520864 l
143.397787 176.907935 l
144.079201 251.308281 l
144.760615 303.486975 l
145.442029 293.04836 l
146.123443 276.549311 l
146.804857 139.798347 l
147.486271 263.798438 l
148.167685 159.407977 l
148.849098 283.545709 l
149.530512 219.630363 l
150.211926 68.161217 l
150.89334 293.727939 l
151.574754 285.085096 l
152.256168 249.270412 l
152.937582 247.080523 l
153.618996 202.301508 l
154.30041 310.448791 l
155.663238 218.834417 l
156.344652 200.388663 l
157.026066 262.385496 l
157.70748 236.913052 l
158.388894 184.506163 l
159.751722 283.138755 l
160.433136 301.369454 l
161.11455 288.854071 l
161.795964 208.291646 l
162.477378 212.077003 l
163.158792 234.286556 l
163.840206 233.635962 l
164.52162 249.740301 l
165.203034 290.766142 l
165.884448 238.149441 l
166.565862 269.229935 l
167.247276 283.650988 l
167.928689 249.569221 l
168.610103 194.605579 l
169.291517 259.903493 l
169.972931 286.309458 l
170.654345 212.397913 l
171.335759 216.747868 l
172.017173 185.824059 l
172.698587 297.876695 l
173.380001 303.578278 l
174.061415 285.139594 l
174.742829 304.096041 l
175.424243 253.170625 l
176.105657 176.917363 l
176.787071 288.969605 l
177.468485 272.019748 l
178.149899 284.696701 l
178.831313 274.092824 l
179.512727 296.404593 l
180.194141 260.991507 l
180.875555 214.597024 l
181.556969 286.2859 l
182.238383 277.329238 l
182.919797 277.449427 l
183.601211 304.216406 l
184.282625 243.105348 l
184.964039 261.55932 l
185.645453 283.53603 l
186.326867 295.286763 l
187.00828 290.664249 l
187.689694 292.877406 l
188.371108 293.133599 l
189.052522 258.23897 l
189.733936 280.964392 l
190.41535 252.283813 l
191.096764 303.160249 l
191.778178 308.585028 l
192.459592 304.192661 l
193.141006 248.731585 l
193.82242 274.888844 l
194.503834 268.562528 l
195.185248 265.382947 l
195.866662 295.002223 l
196.548076 305.670577 l
197.22949 286.774133 l
197.910904 283.005677 l
198.592318 305.830576 l
199.273732 315.27612 l
199.955146 298.490387 l
200.63656 303.818372 l
201.317974 299.376948 l
201.999388 298.687562 l
202.680802 289.074564 l
203.362216 299.563304 l
204.04363 267.282611 l
204.725044 303.681936 l
205.406458 308.571959 l
206.087871 303.452823 l
206.769285 312.756982 l
207.450699 304.337119 l
208.132113 290.822658 l
208.813527 304.59336 l
209.494941 302.277088 l
210.176355 297.799607 l
210.857769 302.03478 l
211.539183 312.953336 l
212.902011 297.714264 l
213.583425 303.224343 l
214.264839 315.290143 l
215.627667 289.466543 l
216.309081 295.569817 l
216.990495 312.946261 l
217.671909 306.624514 l
218.353323 308.464576 l
219.034737 309.68041 l
219.716151 300.90237 l
220.397565 300.216115 l
221.078979 313.07681 l
221.760393 306.924491 l
222.441807 286.061708 l
223.123221 310.547191 l
223.804635 312.598904 l
224.486049 303.632076 l
225.167462 304.162529 l
225.848876 305.030541 l
226.53029 313.258307 l
227.211704 312.025008 l
227.893118 313.247231 l
228.574532 308.473939 l
229.255946 314.573922 l
229.93736 315.793362 l
230.618774 307.371421 l
231.300188 312.46164 l
231.981602 303.57342 l
232.663016 315.878553 l
233.34443 318.637706 l
234.025844 317.435098 l
234.707258 315.407921 l
235.388672 315.44772 l
236.070086 306.234248 l
236.7515 309.738741 l
237.432914 308.268044 l
238.114328 312.767832 l
238.795742 313.673269 l
239.477156 307.148886 l
240.15857 312.739141 l
240.839984 316.954156 l
241.521398 311.044077 l
242.202812 313.288835 l
242.884226 312.918762 l
243.56564 308.288503 l
244.247053 317.111558 l
244.928467 316.21867 l
245.609881 314.077227 l
246.291295 308.89852 l
246.972709 316.275645 l
248.335537 316.312346 l
249.016951 311.684085 l
249.698365 313.83872 l
250.379779 303.09735 l
251.061193 313.366035 l
251.742607 313.976876 l
252.424021 316.409698 l
253.105435 310.449438 l
253.786849 313.191146 l
254.468263 320.616965 l
255.149677 316.201348 l
255.831091 314.411693 l
256.512505 316.479652 l
257.193919 316.493996 l
257.875333 314.767707 l
258.556747 317.200077 l
259.919575 311.391264 l
260.600989 313.203715 l
261.282403 316.57101 l
261.963817 315.517935 l
262.645231 315.047856 l
263.326644 313.954824 l
264.008058 316.6176 l
264.689472 312.908457 l
265.370886 313.80655 l
266.0523 316.6476 l
266.733714 315.433605 l
267.415128 315.417375 l
268.096542 315.890845 l
268.777956 316.683955 l
269.45937 314.300653 l
270.140784 312.72729 l
270.822198 314.931167 l
271.503612 315.44345 l
272.185026 316.47521 l
272.86644 312.672097 l
273.547854 316.736925 l
274.229268 314.153562 l
274.910682 316.75148 l
275.592096 313.805852 l
276.27351 315.198043 l
276.954924 315.171515 l
277.636338 310.815278 l
278.999166 313.323736 l
279.68058 314.554599 l
280.361994 313.195373 l
281.043408 315.753677 l
281.724822 315.31903 l
282.406235 320.185708 l
283.087649 314.528148 l
283.769063 321.716903 l
284.450477 306.211888 l
285.131891 315.669441 l
285.813305 316.827717 l
286.494719 315.469171 l
287.176133 312.013194 l
287.857547 315.603094 l
288.538961 306.692293 l
289.220375 316.876357 l
289.901789 317.599311 l
290.583203 315.501141 l
291.264617 315.461772 l
291.946031 316.857355 l
292.627445 313.015848 l
293.308859 314.419033 l
293.990273 310.656175 l
294.671687 314.942518 l
295.353101 312.155278 l
296.034515 315.80369 l
296.715929 307.430805 l
297.397343 317.508851 l
298.078757 316.877983 l
298.760171 312.968285 l
299.441585 311.62947 l
300.122999 314.657879 l
301.485826 316.887151 l
302.16724 315.981705 l
302.848654 316.54378 l
303.530068 316.89178 l
304.211482 316.893313 l
305.57431 310.80554 l
306.255724 315.306767 l
306.937138 316.897526 l
307.618552 314.661616 l
308.299966 316.899943 l
308.98138 318.594422 l
309.662794 317.85599 l
310.344208 313.096609 l
311.025622 314.577243 l
311.707036 316.904424 l
315.114106 316.90875 l
315.79552 316.071818 l
316.476934 315.664957 l
317.158348 266.922414 l
317.839762 315.707327 l
318.521176 316.911786 l
319.20259 316.912384 l
319.884004 311.981542 l
320.565417 316.913446 l
321.246831 316.765403 l
321.928245 313.371932 l
322.609659 314.804903 l
323.291073 316.915391 l
323.972487 316.915819 l
324.653901 315.680704 l
325.335315 315.562643 l
326.016729 316.916879 l
326.698143 314.611275 l
327.379557 314.283208 l
328.060971 316.917887 l
329.423799 316.918484 l
330.105213 319.688202 l
331.468041 315.670735 l
332.149455 316.919513 l
332.830869 313.826384 l
333.512283 315.021439 l
334.193697 313.61291 l
334.875111 315.101435 l
335.556525 316.920591 l
336.919353 316.920926 l
337.600767 315.735771 l
338.282181 310.852661 l
338.963595 315.413841 l
339.645008 316.921399 l
340.326422 314.454937 l
341.007836 319.478858 l
341.68925 316.921762 l
342.370664 316.776205 l
343.052078 316.921977 l
343.733492 315.314812 l
344.414906 314.459997 l
345.09632 312.455113 l
345.777734 316.922335 l
346.459148 314.638589 l
347.140562 320.028985 l
347.821976 313.678948 l
348.50339 317.346521 l
349.184804 315.424845 l
349.866218 316.922694 l
350.547632 316.922753 l
351.229046 317.818861 l
351.91046 316.922861 l
352.591874 313.497777 l
353.273288 315.048251 l
354.636116 316.923042 l
355.998944 316.923118 l
356.680358 316.012091 l
357.361772 318.765376 l
358.043186 314.203914 l
358.724599 316.923245 l
359.406013 316.923272 l
360.087427 313.349085 l
360.768841 316.923322 l
361.450255 314.353887 l
362.131669 316.923367 l
366.220153 316.923471 l
366.901567 316.602475 l
367.582981 316.923496 l
368.264395 316.923509 l
368.945809 319.599378 l
369.627223 316.923531 l
373.034293 316.923577 l
373.715707 318.803248 l
374.397121 316.923592 l
375.078535 308.251326 l
375.759949 316.881508 l
376.441363 312.223837 l
377.122777 316.923617 l
378.485604 316.923627 l
379.167018 317.796454 l
379.848432 316.923636 l
381.892674 316.923648 l
382.574088 314.818959 l
383.255502 314.746487 l
383.936916 316.911001 l
384.61833 314.715629 l
385.299744 318.413714 l
385.981158 313.305515 l
386.662572 315.811373 l
387.343986 316.92367 l
388.0254 316.923672 l
388.706814 316.65696 l
389.388228 316.923674 l
390.069642 316.923676 l
390.751056 312.685597 l
391.43247 316.923679 l
392.113884 315.724959 l
392.795298 316.234986 l
393.476712 318.874808 l
394.158126 316.923684 l
396.202368 316.923687 l
396.883781 315.585535 l
397.565195 316.923689 l
402.335093 316.923694 l
403.016507 315.662478 l
403.697921 316.406514 l
404.379335 306.49431 l
405.060749 316.923695 l
407.104991 316.923696 l
407.786405 312.10242 l
408.467819 316.923697 l
411.193475 316.923698 l
411.874889 313.725785 l
412.556303 316.923698 l
421.414684 316.9237 l
422.096098 317.465464 l
422.777512 316.9237 l
426.865996 316.923701 l
427.54741 315.99914 l
428.228824 316.923701 l
432.998722 316.923701 l
432.998722 316.923701 l
stroke
grestore
0.800 setlinewidth
0 setlinejoin
0.000 setgray
gsave
75.971875 46.971875 m
75.971875 334.8 l
stroke
grestore
gsave
450 46.971875 m
450 334.8 l
stroke
grestore
gsave
75.971875 46.971875 m
450 46.971875 l
stroke
grestore
gsave
75.971875 334.8 m
450 334.8 l
stroke
grestore

end
showpage
