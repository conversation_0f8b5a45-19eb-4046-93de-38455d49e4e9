#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
APSIS - Adaptive Phase-field Segmentation for Ice Scenes
基于Ginzburg-Landau相场理论的自适应海冰分割算法

完整功能版本 - 包含所有高级功能模块
- Ginzburg-Landau相场理论核心算法
- 各向异性扩散张量计算
- 多尺度特征提取与融合
- 自适应参数调节机制
- 边缘保持与纹理耦合
- 完整的物理过程可视化
- 详细的性能分析与报告
- 高级数学物理分析功能

版本: 5.0 (完整功能版)
作者: APSIS开发团队
日期: 2025-01-18
"""

import numpy as np
import cv2
from scipy.ndimage import gaussian_filter, sobel
import matplotlib.pyplot as plt
from typing import Tuple, Dict, Optional, List
import time
import warnings
warnings.filterwarnings('ignore')

class APSISOptimized:
    """
    APSIS优化算法类 - 完整版
    
    基于Ginzburg-Landau相场理论的自适应海冰分割算法，
    包含完整的可视化、分析和各向异性扩散功能。
    """

    def __init__(self,
                 epsilon: float = 0.05,
                 mu: float = 1.0,
                 lambda_: float = 1.5,
                 dt: float = 0.01,
                 max_iterations: int = 1000,
                 convergence_threshold: float = 1e-6,
                 use_adaptive_weights: bool = True,
                 use_edge_preservation: bool = True,
                 use_texture_feature: bool = True,
                 use_anisotropic_diffusion: bool = False,
                 anisotropy_strength: float = 0.5,
                 diffusion_threshold: float = 0.1):
        """
        初始化APSIS算法参数
        
        Args:
            epsilon: 界面宽度参数 (0.01-0.1)
            mu: 扩散系数/迁移率 (0.5-2.0)
            lambda_: 图像驱动力系数 (1.0-3.0)
            dt: 时间步长 (0.005-0.02)
            max_iterations: 最大迭代次数 (500-2000)
            convergence_threshold: 收敛阈值 (1e-7 to 1e-5)
            use_adaptive_weights: 启用自适应权重调节
            use_edge_preservation: 启用边缘保持机制
            use_texture_feature: 启用纹理特征融合
            use_anisotropic_diffusion: 启用各向异性扩散
            anisotropy_strength: 各向异性强度 (0.0-1.0)
            diffusion_threshold: 扩散阈值 (0.01-0.5)
        """
        # 核心物理参数
        self.epsilon = epsilon
        self.mu = mu
        self.lambda_ = lambda_

        # 自动调整时间步长确保数值稳定性
        dt_stable = 0.25 * epsilon**2 / mu
        if dt > dt_stable:
            print(f"⚠️ 警告：dt={dt:.6f}过大，自动调整为{dt_stable:.6f}以确保稳定性")
            dt = dt_stable
        self.dt = dt
        self.max_iterations = max_iterations
        self.convergence_threshold = convergence_threshold
        
        # 功能开关
        self.use_adaptive_weights = use_adaptive_weights
        self.use_edge_preservation = use_edge_preservation
        self.use_texture_feature = use_texture_feature
        self.use_anisotropic_diffusion = use_anisotropic_diffusion
        
        # 各向异性扩散参数
        self.anisotropy_strength = anisotropy_strength
        self.diffusion_threshold = diffusion_threshold
        
        # 运行时状态
        self.energy_history = []
        self.convergence_info = {}
        self.computation_time = 0
        
        # 验证参数合理性
        self._validate_parameters()

    def _validate_parameters(self):
        """验证参数合理性"""
        if not (0.01 <= self.epsilon <= 0.1):
            print(f"⚠️ 警告: epsilon={self.epsilon} 可能不在推荐范围 [0.01, 0.1]")
        if not (0.5 <= self.mu <= 2.0):
            print(f"⚠️ 警告: mu={self.mu} 可能不在推荐范围 [0.5, 2.0]")
        if not (1.0 <= self.lambda_ <= 3.0):
            print(f"⚠️ 警告: lambda_={self.lambda_} 可能不在推荐范围 [1.0, 3.0]")
        if not (0.005 <= self.dt <= 0.02):
            print(f"⚠️ 警告: dt={self.dt} 可能不在推荐范围 [0.005, 0.02]")

    # ==================== 特征提取模块 ====================
    
    def extract_edge_features(self, image: np.ndarray) -> np.ndarray:
        """
        提取边缘特征
        
        使用Sobel算子计算梯度幅值，与原始算法完全一致
        
        Args:
            image: 输入图像 [0,1]
            
        Returns:
            edge_map: 边缘强度图 [0,1]
        """
        edge_map = sobel(image)
        if np.max(edge_map) > 0:
            edge_map = edge_map / np.max(edge_map)
        return edge_map
    
    def extract_texture_features(self, image: np.ndarray) -> np.ndarray:
        """
        提取纹理特征
        
        使用多尺度高斯滤波器提取纹理特征
        
        Args:
            image: 输入图像 [0,1]
            
        Returns:
            texture_feature: 纹理特征图 [0,1]
        """
        texture_feature = np.zeros_like(image)
        scales = [0.5, 1.0, 2.0]
        
        for sigma in scales:
            texture_feature += np.abs(image - gaussian_filter(image, sigma))
        
        if np.max(texture_feature) > 0:
            texture_feature = texture_feature / np.max(texture_feature)
        
        return texture_feature

    # ==================== 各向异性扩散模块 ====================
    
    def compute_anisotropic_diffusion_tensor(self, image: np.ndarray, edge_map: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算各向异性扩散张量
        
        基于Perona-Malik模型构建方向性扩散张量
        
        Args:
            image: 输入图像 [0,1]
            edge_map: 边缘强度图 [0,1]
            
        Returns:
            D_xx, D_xy, D_yy: 扩散张量的三个分量
        """
        if not self.use_anisotropic_diffusion:
            # 各向同性扩散张量
            ones = np.ones_like(image)
            zeros = np.zeros_like(image)
            return ones, zeros, ones
        
        # 计算图像梯度
        grad_x = np.gradient(image, axis=1)
        grad_y = np.gradient(image, axis=0)
        grad_magnitude = np.sqrt(grad_x**2 + grad_y**2 + 1e-8)
        
        # 梯度方向单位向量
        nx = grad_x / grad_magnitude
        ny = grad_y / grad_magnitude
        
        # Perona-Malik扩散抑制函数
        K = self.diffusion_threshold
        g = 1.0 / (1.0 + (grad_magnitude / K)**2)
        
        # 各向异性强度调节
        aniso_factor = self.anisotropy_strength * edge_map
        
        # 主扩散系数
        lambda1 = g * (1.0 - aniso_factor)  # 跨边缘方向（抑制）
        lambda2 = g * (1.0 + aniso_factor)  # 沿边缘方向（促进）
        
        # 构建扩散张量 D = λ₁n⊗n + λ₂t⊗t
        D_xx = lambda1 * nx**2 + lambda2 * ny**2
        D_xy = (lambda1 - lambda2) * nx * ny
        D_yy = lambda1 * ny**2 + lambda2 * nx**2
        
        return D_xx, D_xy, D_yy

    def compute_anisotropic_laplacian(self, phi: np.ndarray, D_xx: np.ndarray, 
                                    D_xy: np.ndarray, D_yy: np.ndarray) -> np.ndarray:
        """
        计算各向异性拉普拉斯算子
        
        计算 ∇·(D∇φ) 替代标准的 ∇²φ
        
        Args:
            phi: 相场函数
            D_xx, D_xy, D_yy: 扩散张量分量
            
        Returns:
            anisotropic_laplacian: 各向异性拉普拉斯算子
        """
        # 相场函数梯度
        phi_x = np.gradient(phi, axis=1)
        phi_y = np.gradient(phi, axis=0)
        
        # 扩散通量
        flux_x = D_xx * phi_x + D_xy * phi_y
        flux_y = D_xy * phi_x + D_yy * phi_y
        
        # 通量散度
        anisotropic_laplacian = np.gradient(flux_x, axis=1) + np.gradient(flux_y, axis=0)
        
        return anisotropic_laplacian

    # ==================== 相场演化模块 ====================
    
    def compute_adaptive_parameters(self, edge_map: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算自适应参数
        
        根据边缘信息动态调整物理参数
        
        Args:
            edge_map: 边缘强度图 [0,1]
            
        Returns:
            lambda_adaptive: 自适应图像驱动力系数
            mu_adaptive: 自适应扩散系数
            epsilon_adaptive: 自适应界面宽度参数
        """
        if self.use_adaptive_weights and edge_map is not None:
            # 边缘自适应权重（与原始算法完全一致）
            edge_weight = 1.0 - 0.7 * edge_map  # 边缘处权重较小
            lambda_adaptive = self.lambda_ * (1.0 + edge_map)  # 边缘处增大图像驱动力
            mu_adaptive = self.mu * edge_weight  # 边缘处减小扩散系数
            epsilon_adaptive = self.epsilon * edge_weight  # 边缘处减小界面宽度
        else:
            # 固定参数
            lambda_adaptive = self.lambda_
            mu_adaptive = self.mu
            epsilon_adaptive = self.epsilon
        
        return lambda_adaptive, mu_adaptive, epsilon_adaptive

    def phase_field_evolution_step(self, phi: np.ndarray, image: np.ndarray,
                                 edge_map: np.ndarray, texture_feature: np.ndarray) -> np.ndarray:
        """
        相场演化步骤
        
        执行一步Ginzburg-Landau相场演化
        
        Args:
            phi: 当前相场函数
            image: 归一化图像 [0,1]
            edge_map: 边缘特征图
            texture_feature: 纹理特征图
            
        Returns:
            phi_new: 更新后的相场函数
        """
        # 计算相场梯度
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_norm = np.sqrt(grad_phi_x**2 + grad_phi_y**2)
        
        # 计算拉普拉斯算子
        if self.use_anisotropic_diffusion and edge_map is not None:
            # 各向异性扩散
            D_xx, D_xy, D_yy = self.compute_anisotropic_diffusion_tensor(image, edge_map)
            laplacian = self.compute_anisotropic_laplacian(phi, D_xx, D_xy, D_yy)
        else:
            # 各向同性扩散
            laplacian = cv2.Laplacian(phi, cv2.CV_64F)
        
        # 自适应参数计算
        lambda_adaptive, mu_adaptive, epsilon_adaptive = self.compute_adaptive_parameters(edge_map)
        
        # 图像驱动项
        image_force = lambda_adaptive * (image - 0.5)
        
        # 纹理特征耦合
        if self.use_texture_feature and texture_feature is not None:
            image_force += 0.3 * lambda_adaptive * texture_feature
        
        # 边缘保持正则化项
        edge_preservation_term = 0
        if self.use_edge_preservation and edge_map is not None:
            edge_preservation_term = 0.2 * edge_map * grad_phi_norm
        
        # Ginzburg-Landau相场演化方程
        # ∂φ/∂t = μ[ε∇²φ - (1/ε)φ(1-φ)(1-2φ)] + F(I) + 边缘保持项
        double_well_term = phi * (1 - phi) * (1 - 2 * phi)  # 双能谷势导数
        dphi = (mu_adaptive * (epsilon_adaptive * laplacian - (1/epsilon_adaptive) * double_well_term) + 
                image_force + edge_preservation_term)
        
        # 时间积分
        phi_new = phi + self.dt * dphi
        
        # 约束到[0,1]区间
        phi_new = np.clip(phi_new, 0, 1)
        
        return phi_new

    # ==================== 初始化和收敛检查模块 ====================

    def otsu_initialization(self, image: np.ndarray) -> np.ndarray:
        """
        Otsu阈值初始化

        使用Otsu自动阈值方法初始化相场函数

        Args:
            image: 输入图像（可以是[0,1]或[0,255]）

        Returns:
            phi: 初始化的相场函数 [0,1]
        """
        if image.dtype != np.uint8:
            image_uint8 = (image * 255).astype(np.uint8)
        else:
            image_uint8 = image

        _, binary = cv2.threshold(image_uint8, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        phi = binary.astype(float) / 255.0

        return phi

    def check_convergence(self, phi_old: np.ndarray, phi_new: np.ndarray) -> bool:
        """
        检查算法收敛性

        Args:
            phi_old: 上一步的相场函数
            phi_new: 当前步的相场函数

        Returns:
            converged: 是否收敛
        """
        phi_change = np.mean(np.abs(phi_new - phi_old))
        return phi_change < self.convergence_threshold

    # ==================== 主分割函数 ====================

    def segment(self, image: np.ndarray, gray_image: np.ndarray = None,
                binary_threshold: float = 0.15,
                show_progress: bool = True) -> Tuple[np.ndarray, Dict]:
        """
        APSIS主分割函数

        基于Ginzburg-Landau相场理论的海冰分割算法主函数

        Args:
            image: 输入图像（预处理后的图像，如同态滤波后）
            gray_image: 原始灰度图像（用于初始化，如果为None则使用image）
            binary_threshold: 二值化阈值 (0.1-0.3)
            show_progress: 是否显示进度信息

        Returns:
            segmented_image: 分割结果图像 [0,255]
            info: 包含详细信息的字典
        """
        start_time = time.time()

        # 图像预处理：归一化到[0,1]
        if np.max(image) > 1.0:
            img_normalized = image / 255.0
        else:
            img_normalized = image.copy()

        if show_progress:
            print(f"🚀 开始APSIS分割 (图像尺寸: {img_normalized.shape})")
            print(f"📋 参数设置: ε={self.epsilon}, μ={self.mu}, λ={self.lambda_}, dt={self.dt}")
            if self.use_anisotropic_diffusion:
                print(f"🔄 启用各向异性扩散 (强度: {self.anisotropy_strength:.2f}, 阈值: {self.diffusion_threshold:.3f})")

        # 特征提取
        edge_map = None
        texture_feature = None

        if self.use_adaptive_weights or self.use_edge_preservation or self.use_anisotropic_diffusion:
            edge_map = self.extract_edge_features(img_normalized)
            if show_progress:
                print(f"✓ 边缘特征提取完成 (最大值: {np.max(edge_map):.3f})")

        if self.use_texture_feature:
            texture_feature = self.extract_texture_features(img_normalized)
            if show_progress:
                print(f"✓ 纹理特征提取完成 (最大值: {np.max(texture_feature):.3f})")

        # 相场函数初始化
        init_image = gray_image if gray_image is not None else image
        if np.max(init_image) <= 1.0:
            init_image = (init_image * 255).astype(np.uint8)

        phi = self.otsu_initialization(init_image)
        initial_ice_ratio = np.mean(phi)

        if show_progress:
            print(f"✓ 相场初始化完成 (初始海冰比例: {initial_ice_ratio:.2%})")

        # 相场演化迭代
        self.energy_history = []

        for i in range(self.max_iterations):
            phi_old = phi.copy()

            # 执行一步相场演化
            phi = self.phase_field_evolution_step(phi, img_normalized, edge_map, texture_feature)

            # 计算能量（可选）
            if i % 50 == 0:
                energy = self._compute_energy(phi, img_normalized, edge_map)
                self.energy_history.append(energy)

            # 收敛性检查
            if self.check_convergence(phi_old, phi):
                if show_progress:
                    print(f"✓ 算法在第 {i + 1} 次迭代后收敛")
                self.convergence_info = {
                    'converged': True,
                    'iterations': i + 1
                }
                break

            # 进度显示
            if show_progress and i % 100 == 0:
                phi_change = np.mean(np.abs(phi - phi_old))
                current_ice_ratio = np.mean(phi > 0.5)
                print(f"⏳ 迭代 {i}: 变化量={phi_change:.6f}, 当前海冰比例={current_ice_ratio:.2%}")

        else:
            if show_progress:
                print(f"⚠️ 达到最大迭代次数 {self.max_iterations}")
            self.convergence_info = {
                'converged': False,
                'iterations': self.max_iterations
            }

        # 二值化得到最终分割结果
        segmented_image = (phi > binary_threshold).astype(np.uint8) * 255

        # 结果统计
        ice_pixels = np.sum(segmented_image > 0)
        total_pixels = segmented_image.size
        ice_ratio = ice_pixels / total_pixels

        self.computation_time = time.time() - start_time

        if show_progress:
            print(f"🎉 分割完成！")
            print(f"📊 海冰覆盖率: {ice_ratio:.2%}")
            print(f"⏱️ 计算时间: {self.computation_time:.2f}秒")
            print(f"🔄 收敛状态: {'已收敛' if self.convergence_info['converged'] else '未收敛'}")
            print(f"📈 迭代次数: {self.convergence_info['iterations']}")

        # 构建返回信息
        info = {
            'convergence_info': self.convergence_info,
            'ice_ratio': ice_ratio,
            'computation_time': self.computation_time,
            'edge_map': edge_map,
            'texture_feature': texture_feature,
            'final_phi': phi,
            'original_image': image,
            'energy_history': self.energy_history,
            'initial_ice_ratio': initial_ice_ratio,
            'parameters': {
                'epsilon': self.epsilon,
                'mu': self.mu,
                'lambda': self.lambda_,
                'dt': self.dt,
                'binary_threshold': binary_threshold,
                'use_anisotropic_diffusion': self.use_anisotropic_diffusion,
                'anisotropy_strength': self.anisotropy_strength,
                'diffusion_threshold': self.diffusion_threshold,
                'use_adaptive_weights': self.use_adaptive_weights,
                'use_edge_preservation': self.use_edge_preservation,
                'use_texture_feature': self.use_texture_feature
            }
        }

        return segmented_image, info

    def _compute_energy(self, phi: np.ndarray, image: np.ndarray, edge_map: np.ndarray) -> float:
        """
        计算系统总能量（用于监控收敛）

        Args:
            phi: 相场函数
            image: 图像
            edge_map: 边缘图

        Returns:
            total_energy: 总能量
        """
        # 计算梯度能
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_energy = 0.5 * self.epsilon * np.sum(grad_phi_x**2 + grad_phi_y**2)

        # 计算双能谷势能
        double_well_energy = (1/self.epsilon) * np.sum(phi**2 * (1-phi)**2)

        # 计算图像驱动能
        image_energy = -self.lambda_ * np.sum(phi * (image - 0.5))

        total_energy = grad_energy + double_well_energy + image_energy

        return total_energy

    # ==================== 基本可视化模块 ====================

    def visualize_results(self, image: np.ndarray, segmented_image: np.ndarray,
                         info: Dict, save_path: Optional[str] = None):
        """
        基本结果可视化

        Args:
            image: 原始图像
            segmented_image: 分割结果
            info: 算法信息字典
            save_path: 保存路径（可选）
        """
        plt.figure(figsize=(18, 6))

        # 原始图像
        plt.subplot(1, 4, 1)
        plt.imshow(image, cmap='gray')
        plt.title('原始图像\nOriginal Image', fontsize=12, fontweight='bold')
        plt.axis('off')

        # 分割结果
        plt.subplot(1, 4, 2)
        plt.imshow(segmented_image, cmap='gray')
        plt.title(f'APSIS分割结果\nSegmentation Result\n覆盖率: {info["ice_ratio"]:.1%}',
                 fontsize=12, fontweight='bold')
        plt.axis('off')

        # 相场函数
        plt.subplot(1, 4, 3)
        im3 = plt.imshow(info['final_phi'], cmap='RdYlBu', vmin=0, vmax=1)
        plt.title('相场函数 φ\nPhase Field Function', fontsize=12, fontweight='bold')
        plt.axis('off')
        plt.colorbar(im3, fraction=0.046, pad=0.04)

        # 边缘特征
        plt.subplot(1, 4, 4)
        if info['edge_map'] is not None:
            im4 = plt.imshow(info['edge_map'], cmap='hot')
            plt.title('边缘特征\nEdge Features', fontsize=12, fontweight='bold')
            plt.colorbar(im4, fraction=0.046, pad=0.04)
        else:
            plt.text(0.5, 0.5, '边缘特征\n未启用', ha='center', va='center',
                    transform=plt.gca().transAxes, fontsize=12)
        plt.axis('off')

        plt.suptitle(f'APSIS海冰分割结果 - 计算时间: {info["computation_time"]:.2f}s',
                     fontsize=16, fontweight='bold')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_performance(self, info: Dict) -> Dict:
        """
        性能分析

        Args:
            info: 算法信息字典

        Returns:
            performance: 性能分析结果
        """
        performance = {
            'convergence': info['convergence_info'],
            'ice_ratio': info['ice_ratio'],
            'computation_time': info['computation_time'],
            'parameters': info['parameters'],
            'efficiency_metrics': {
                'iterations_per_second': info['convergence_info']['iterations'] / info['computation_time'],
                'convergence_rate': 'Fast' if info['convergence_info']['iterations'] < 200 else 'Normal' if info['convergence_info']['iterations'] < 500 else 'Slow',
                'parameter_efficiency': self._evaluate_parameter_efficiency(info)
            }
        }
        return performance

    def _evaluate_parameter_efficiency(self, info: Dict) -> str:
        """评估参数效率"""
        params = info['parameters']
        if params['dt'] > 0.015 and info['convergence_info']['converged']:
            return 'High'
        elif params['dt'] > 0.01 and info['convergence_info']['converged']:
            return 'Medium'
        else:
            return 'Low'

    def print_performance_report(self, performance: Dict):
        """
        打印性能报告

        Args:
            performance: 性能分析结果
        """
        print(f"\n{'='*60}")
        print("🏆 APSIS算法性能报告")
        print("APSIS Performance Report")
        print(f"{'='*60}")

        # 基本性能指标
        print(f"\n📊 基本性能指标:")
        print(f"   收敛状态: {'✅ 已收敛' if performance['convergence']['converged'] else '❌ 未收敛'}")
        print(f"   迭代次数: {performance['convergence']['iterations']}")
        print(f"   海冰覆盖率: {performance['ice_ratio']:.2%}")
        print(f"   计算时间: {performance['computation_time']:.2f}秒")

        # 效率指标
        eff = performance['efficiency_metrics']
        print(f"\n⚡ 效率指标:")
        print(f"   迭代速度: {eff['iterations_per_second']:.1f} 次/秒")
        print(f"   收敛速度: {eff['convergence_rate']}")
        print(f"   参数效率: {eff['parameter_efficiency']}")

        # 功能配置
        params = performance['parameters']
        print(f"\n🔧 功能配置:")
        print(f"   自适应权重: {'✅' if params['use_adaptive_weights'] else '❌'}")
        print(f"   边缘保持: {'✅' if params['use_edge_preservation'] else '❌'}")
        print(f"   纹理特征: {'✅' if params['use_texture_feature'] else '❌'}")
        print(f"   各向异性扩散: {'✅' if params['use_anisotropic_diffusion'] else '❌'}")

        if params['use_anisotropic_diffusion']:
            print(f"     - 各向异性强度: {params['anisotropy_strength']:.2f}")
            print(f"     - 扩散阈值: {params['diffusion_threshold']:.3f}")

        print(f"\n🎯 核心参数:")
        print(f"   界面宽度 ε: {params['epsilon']:.3f}")
        print(f"   扩散系数 μ: {params['mu']:.1f}")
        print(f"   图像驱动力 λ: {params['lambda']:.1f}")
        print(f"   时间步长 dt: {params['dt']:.3f}")

        print(f"{'='*60}")

    # ==================== Ginzburg-Landau物理可视化 ====================

    def visualize_ginzburg_landau_physics(self, image: np.ndarray, segmented_image: np.ndarray,
                                         info: Dict, save_path: Optional[str] = None):
        """
        可视化Ginzburg-Landau相场理论的物理过程
        展示冰体断裂建模的物理意义
        """
        plt.figure(figsize=(20, 12))

        # 第一行：原始图像和相场演化
        plt.subplot(3, 4, 1)
        plt.imshow(image, cmap='gray')
        plt.title('原始海冰图像\nOriginal Ice Image', fontsize=12, fontweight='bold')
        plt.axis('off')

        plt.subplot(3, 4, 2)
        im2 = plt.imshow(info['final_phi'], cmap='RdYlBu', vmin=0, vmax=1)
        plt.title('相场函数 φ(x,y)\nPhase Field Function\n(0=水域, 1=海冰)', fontsize=12, fontweight='bold')
        plt.colorbar(im2, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 计算相场梯度（界面张力可视化）
        phi = info['final_phi']
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_magnitude = np.sqrt(grad_phi_x**2 + grad_phi_y**2)

        plt.subplot(3, 4, 3)
        im3 = plt.imshow(grad_phi_magnitude, cmap='hot')
        plt.title('界面张力 |∇φ|\nInterface Tension\n(Ginzburg-Landau项)', fontsize=12, fontweight='bold')
        plt.colorbar(im3, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 双能谷势函数可视化
        plt.subplot(3, 4, 4)
        phi_range = np.linspace(0, 1, 100)
        double_well = phi_range**2 * (1 - phi_range)**2
        double_well_derivative = phi_range * (1 - phi_range) * (1 - 2 * phi_range)

        plt.plot(phi_range, double_well, 'b-', linewidth=3, label='W(φ) = φ²(1-φ)²')
        plt.plot(phi_range, -double_well_derivative, 'r--', linewidth=2, label="-W'(φ)")
        plt.xlabel('相场函数 φ')
        plt.ylabel('势能')
        plt.title('双能谷势函数\nDouble-Well Potential', fontsize=12, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 第二行：物理驱动力分析
        plt.subplot(3, 4, 5)
        if info['edge_map'] is not None:
            im5 = plt.imshow(info['edge_map'], cmap='viridis')
            plt.title('边缘驱动力\nEdge Driving Force\n自适应权重调节', fontsize=12, fontweight='bold')
            plt.colorbar(im5, fraction=0.046, pad=0.04)
        plt.axis('off')

        plt.subplot(3, 4, 6)
        if info['texture_feature'] is not None:
            im6 = plt.imshow(info['texture_feature'], cmap='plasma')
            plt.title('纹理耦合项\nTexture Coupling\n多尺度特征融合', fontsize=12, fontweight='bold')
            plt.colorbar(im6, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 计算冰体断裂界面
        interface_mask = (grad_phi_magnitude > 0.1).astype(float)
        plt.subplot(3, 4, 7)
        plt.imshow(image, cmap='gray', alpha=0.7)
        plt.imshow(interface_mask, cmap='Reds', alpha=0.5)
        plt.title('冰体断裂界面\nIce Fracture Interface\n相场界面叠加', fontsize=12, fontweight='bold')
        plt.axis('off')

        # 相场演化能量
        plt.subplot(3, 4, 8)
        epsilon = info['parameters']['epsilon']
        energy_density = 0.5 * epsilon * grad_phi_magnitude**2 + (1/epsilon) * phi**2 * (1-phi)**2
        im8 = plt.imshow(energy_density, cmap='inferno')
        plt.title('能量密度分布\nEnergy Density\nGinzburg-Landau能量', fontsize=12, fontweight='bold')
        plt.colorbar(im8, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 第三行：物理过程分析
        plt.subplot(3, 4, 9)
        plt.imshow(segmented_image, cmap='gray')
        plt.title(f'最终分割结果\nFinal Segmentation\n海冰覆盖率: {info["ice_ratio"]:.1%}',
                 fontsize=12, fontweight='bold')
        plt.axis('off')

        # 相场剖面分析
        plt.subplot(3, 4, 10)
        center_y = phi.shape[0] // 2
        phi_profile = phi[center_y, :]
        x_coords = np.arange(len(phi_profile))
        plt.plot(x_coords, phi_profile, 'b-', linewidth=2, label='相场函数 φ')
        plt.axhline(y=0.5, color='r', linestyle='--', alpha=0.7, label='界面位置')
        plt.fill_between(x_coords, 0, phi_profile, alpha=0.3)
        plt.xlabel('空间位置 x')
        plt.ylabel('相场值 φ')
        plt.title('相场剖面\nPhase Field Profile\n界面过渡特征', fontsize=12, fontweight='bold')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 物理参数展示
        plt.subplot(3, 4, 11)
        plt.axis('off')
        physics_text = f"""Ginzburg-Landau相场理论
Physical Parameters:

界面宽度参数 ε = {info['parameters']['epsilon']:.3f}
扩散系数 μ = {info['parameters']['mu']:.1f}
图像驱动力 λ = {info['parameters']['lambda']:.1f}
时间步长 dt = {info['parameters']['dt']:.3f}

相场演化方程:
∂φ/∂t = μ[ε∇²φ - (1/ε)φ(1-φ)(1-2φ)] + F(I)

物理意义:
• φ = 0: 水域 (Water)
• φ = 1: 海冰 (Sea Ice)
• φ = 0.5: 冰水界面 (Interface)
• ∇²φ: 界面曲率驱动
• φ(1-φ)(1-2φ): 双能谷势驱动
• F(I): 图像信息驱动

能量泛函:
E[φ] = ∫[ε/2|∇φ|² + W(φ)/ε]dΩ"""

        plt.text(0.05, 0.95, physics_text, transform=plt.gca().transAxes,
                fontsize=9, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        # 收敛性分析
        plt.subplot(3, 4, 12)
        if info.get('energy_history') and len(info['energy_history']) > 1:
            plt.plot(info['energy_history'], 'g-', linewidth=2, marker='o', markersize=4)
            plt.xlabel('迭代次数 (×50)')
            plt.ylabel('系统能量')
            plt.title('能量收敛曲线\nEnergy Convergence', fontsize=12, fontweight='bold')
            plt.grid(True, alpha=0.3)
        else:
            convergence_text = f"""算法收敛性:
收敛状态: {'已收敛' if info['convergence_info']['converged'] else '未收敛'}
迭代次数: {info['convergence_info']['iterations']}
计算时间: {info['computation_time']:.2f}s
初始海冰比例: {info.get('initial_ice_ratio', 0):.1%}
最终海冰比例: {info['ice_ratio']:.1%}"""

            plt.text(0.5, 0.5, convergence_text, ha='center', va='center',
                    transform=plt.gca().transAxes, fontsize=11,
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            plt.axis('off')

        plt.suptitle('APSIS: 基于Ginzburg-Landau相场理论的冰体断裂建模\nAdaptive Phase-field Segmentation for Ice Scenes: Ginzburg-Landau Theory for Ice Fracture Modeling',
                     fontsize=16, fontweight='bold')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_ginzburg_landau_physics(self, info: Dict) -> Dict:
        """
        分析Ginzburg-Landau相场理论的物理特性
        """
        phi = info['final_phi']

        # 计算相场梯度
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_magnitude = np.sqrt(grad_phi_x**2 + grad_phi_y**2)

        # 计算界面长度（总变分）
        interface_length = np.sum(grad_phi_magnitude)

        # 计算双能谷势能
        double_well_energy = np.sum(phi**2 * (1 - phi)**2)

        # 计算界面张力能
        epsilon = info['parameters']['epsilon']
        interface_energy = 0.5 * epsilon * np.sum(grad_phi_magnitude**2)

        # 计算总Ginzburg-Landau能量
        total_gl_energy = interface_energy + (1/epsilon) * double_well_energy

        # 界面锐度分析
        interface_mask = (grad_phi_magnitude > 0.1)
        interface_sharpness = np.mean(grad_phi_magnitude[interface_mask]) if np.any(interface_mask) else 0

        # 相分离质量
        ice_purity = np.mean(phi[phi > 0.8]) if np.any(phi > 0.8) else 0
        water_purity = np.mean(1 - phi[phi < 0.2]) if np.any(phi < 0.2) else 0

        # 界面复杂度
        interface_complexity = np.std(grad_phi_magnitude[interface_mask]) if np.any(interface_mask) else 0

        physics_analysis = {
            'ginzburg_landau_energy': total_gl_energy,
            'interface_energy': interface_energy,
            'double_well_energy': double_well_energy,
            'interface_length': interface_length,
            'interface_sharpness': interface_sharpness,
            'interface_complexity': interface_complexity,
            'ice_purity': ice_purity,
            'water_purity': water_purity,
            'phase_separation_quality': (ice_purity + water_purity) / 2,
            'convergence': info['convergence_info'],
            'computation_metrics': {
                'ice_ratio': info['ice_ratio'],
                'computation_time': info['computation_time'],
                'initial_ice_ratio': info.get('initial_ice_ratio', 0),
                'ice_ratio_change': info['ice_ratio'] - info.get('initial_ice_ratio', 0)
            }
        }

        # 如果使用了各向异性扩散，添加相关分析
        if self.use_anisotropic_diffusion:
            aniso_analysis = self.analyze_anisotropic_diffusion(info)
            physics_analysis['anisotropic_diffusion'] = aniso_analysis

        return physics_analysis

    def analyze_anisotropic_diffusion(self, info: Dict) -> Dict:
        """
        分析各向异性扩散的效果

        Args:
            info: 算法信息字典

        Returns:
            aniso_analysis: 各向异性扩散分析结果
        """
        if not self.use_anisotropic_diffusion:
            return {
                'error': '各向异性扩散未启用',
                'enabled': False
            }

        phi = info['final_phi']

        # 重新计算扩散张量用于分析
        image = info.get('original_image', phi)
        if np.max(image) > 1.0:
            image = image / 255.0
        edge_map = info.get('edge_map')

        if edge_map is not None:
            D_xx, D_xy, D_yy = self.compute_anisotropic_diffusion_tensor(image, edge_map)

            # 计算特征值和特征向量
            eigenvalues, eigenvectors = self.compute_diffusion_eigenvalues(D_xx, D_xy, D_yy)
            lambda1, lambda2 = eigenvalues

            # 各向异性度量
            anisotropy_ratio = np.mean(lambda2 / (lambda1 + 1e-8))
            anisotropy_variance = np.var(lambda2 / (lambda1 + 1e-8))

            # 扩散方向一致性
            direction_coherence = self.compute_direction_coherence(D_xx, D_xy, D_yy)

            # 边缘保持效果
            edge_preservation_score = self.compute_edge_preservation_score(phi, edge_map)

            # 扩散效率分析
            diffusion_efficiency = self.compute_diffusion_efficiency(D_xx, D_xy, D_yy, edge_map)

            aniso_analysis = {
                'enabled': True,
                'anisotropy_ratio_mean': anisotropy_ratio,
                'anisotropy_variance': anisotropy_variance,
                'direction_coherence': direction_coherence,
                'edge_preservation_score': edge_preservation_score,
                'diffusion_efficiency': diffusion_efficiency,
                'diffusion_tensor_stats': {
                    'D_xx_mean': np.mean(D_xx),
                    'D_xx_std': np.std(D_xx),
                    'D_xy_mean': np.mean(D_xy),
                    'D_xy_std': np.std(D_xy),
                    'D_yy_mean': np.mean(D_yy),
                    'D_yy_std': np.std(D_yy),
                    'lambda1_mean': np.mean(lambda1),
                    'lambda1_std': np.std(lambda1),
                    'lambda2_mean': np.mean(lambda2),
                    'lambda2_std': np.std(lambda2)
                },
                'tensor_condition_number': np.mean(lambda2 / (lambda1 + 1e-8)),
                'anisotropy_strength_actual': self.anisotropy_strength,
                'diffusion_threshold_actual': self.diffusion_threshold
            }
        else:
            aniso_analysis = {
                'error': 'Edge map not available for anisotropic analysis',
                'enabled': True
            }

        return aniso_analysis

    def compute_diffusion_eigenvalues(self, D_xx: np.ndarray, D_xy: np.ndarray,
                                    D_yy: np.ndarray) -> Tuple[Tuple[np.ndarray, np.ndarray],
                                                              Tuple[Tuple[np.ndarray, np.ndarray],
                                                                   Tuple[np.ndarray, np.ndarray]]]:
        """
        计算扩散张量的特征值和特征向量

        Args:
            D_xx, D_xy, D_yy: 扩散张量分量

        Returns:
            eigenvalues: (lambda1, lambda2) - 特征值
            eigenvectors: ((v1_x, v1_y), (v2_x, v2_y)) - 特征向量
        """
        # 计算特征值
        trace = D_xx + D_yy
        det = D_xx * D_yy - D_xy**2
        discriminant = np.sqrt(np.maximum(trace**2 - 4*det, 0))

        lambda1 = 0.5 * (trace - discriminant)  # 较小特征值
        lambda2 = 0.5 * (trace + discriminant)  # 较大特征值

        # 计算特征向量
        # 对于第一个特征值lambda1
        v1_x = np.where(np.abs(D_xy) > 1e-8, lambda1 - D_yy, np.ones_like(D_xx))
        v1_y = np.where(np.abs(D_xy) > 1e-8, D_xy, np.zeros_like(D_xx))
        v1_norm = np.sqrt(v1_x**2 + v1_y**2 + 1e-8)
        v1_x = v1_x / v1_norm
        v1_y = v1_y / v1_norm

        # 对于第二个特征值lambda2
        v2_x = -v1_y  # 垂直于第一个特征向量
        v2_y = v1_x

        return (lambda1, lambda2), ((v1_x, v1_y), (v2_x, v2_y))

    def compute_direction_coherence(self, D_xx: np.ndarray, D_xy: np.ndarray,
                                  D_yy: np.ndarray) -> float:
        """
        计算扩散方向的一致性

        Args:
            D_xx, D_xy, D_yy: 扩散张量分量

        Returns:
            coherence: 方向一致性 [0,1]
        """
        # 计算主方向角度
        theta = 0.5 * np.arctan2(2 * D_xy, D_xx - D_yy)

        # 计算方向一致性（使用复数表示）
        coherence_complex = np.mean(np.exp(2j * theta))
        coherence = np.abs(coherence_complex)

        return coherence

    def compute_edge_preservation_score(self, phi: np.ndarray, edge_map: np.ndarray) -> float:
        """
        计算边缘保持效果评分

        Args:
            phi: 相场函数
            edge_map: 边缘强度图

        Returns:
            edge_preservation: 边缘保持评分
        """
        # 计算相场函数的梯度
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_magnitude = np.sqrt(grad_phi_x**2 + grad_phi_y**2)

        # 在强边缘区域计算梯度保持程度
        strong_edges = edge_map > 0.5
        if np.any(strong_edges):
            edge_preservation = np.mean(grad_phi_magnitude[strong_edges])
        else:
            edge_preservation = 0.0

        return edge_preservation

    def compute_diffusion_efficiency(self, D_xx: np.ndarray, D_xy: np.ndarray,
                                   D_yy: np.ndarray, edge_map: np.ndarray) -> float:
        """
        计算扩散效率

        Args:
            D_xx, D_xy, D_yy: 扩散张量分量
            edge_map: 边缘强度图

        Returns:
            efficiency: 扩散效率评分
        """
        # 计算扩散张量的迹（总扩散强度）
        trace = D_xx + D_yy

        # 计算扩散张量的行列式（各向异性程度）
        det = D_xx * D_yy - D_xy**2

        # 在边缘区域的扩散效率
        edge_regions = edge_map > 0.3
        if np.any(edge_regions):
            edge_efficiency = np.mean(det[edge_regions] / (trace[edge_regions] + 1e-8))
        else:
            edge_efficiency = 0.0

        return edge_efficiency

    def print_physics_report(self, physics_analysis: Dict):
        """
        打印Ginzburg-Landau物理分析报告
        """
        print("\n" + "="*80)
        print("🔬 APSIS Ginzburg-Landau相场理论物理分析报告")
        print("Ginzburg-Landau Phase Field Theory Physics Analysis Report")
        print("="*80)

        print(f"\n⚡ 相场能量分析 Phase Field Energy Analysis:")
        print(f"   总Ginzburg-Landau能量: {physics_analysis['ginzburg_landau_energy']:.6f}")
        print(f"   界面张力能量: {physics_analysis['interface_energy']:.6f}")
        print(f"   双能谷势能量: {physics_analysis['double_well_energy']:.6f}")

        print(f"\n🧊 冰体断裂界面分析 Ice Fracture Interface Analysis:")
        print(f"   界面总长度: {physics_analysis['interface_length']:.2f}")
        print(f"   界面锐度: {physics_analysis['interface_sharpness']:.4f}")
        print(f"   界面复杂度: {physics_analysis['interface_complexity']:.4f}")

        print(f"\n⚖️ 相分离质量 Phase Separation Quality:")
        print(f"   冰相纯度: {physics_analysis['ice_purity']:.3f}")
        print(f"   水相纯度: {physics_analysis['water_purity']:.3f}")
        print(f"   相分离质量: {physics_analysis['phase_separation_quality']:.3f}")

        metrics = physics_analysis['computation_metrics']
        print(f"\n📊 计算性能 Computational Performance:")
        print(f"   初始海冰覆盖率: {metrics['initial_ice_ratio']:.2%}")
        print(f"   最终海冰覆盖率: {metrics['ice_ratio']:.2%}")
        print(f"   覆盖率变化: {metrics['ice_ratio_change']:+.2%}")
        print(f"   计算时间: {metrics['computation_time']:.2f}秒")
        print(f"   收敛状态: {'✅ 已收敛' if physics_analysis['convergence']['converged'] else '❌ 未收敛'}")
        print(f"   迭代次数: {physics_analysis['convergence']['iterations']}")

        print("="*80)

    # ==================== 各向异性扩散可视化 ====================

    def visualize_anisotropic_diffusion(self, image: np.ndarray, segmented_image: np.ndarray,
                                      info: Dict, save_path: Optional[str] = None):
        """
        可视化各向异性扩散过程
        展示扩散张量的方向性和强度

        Args:
            image: 原始图像
            segmented_image: 分割结果
            info: 算法信息字典
            save_path: 保存路径（可选）
        """
        if not self.use_anisotropic_diffusion:
            print("⚠️ 各向异性扩散未启用，无法进行可视化")
            return

        # 重新计算各向异性扩散张量用于可视化
        img_normalized = image / 255.0 if np.max(image) > 1.0 else image
        edge_map = self.extract_edge_features(img_normalized)
        D_xx, D_xy, D_yy = self.compute_anisotropic_diffusion_tensor(img_normalized, edge_map)

        plt.figure(figsize=(24, 18))

        # 第一行：原始图像和边缘信息
        plt.subplot(4, 6, 1)
        plt.imshow(image, cmap='gray')
        plt.title('原始海冰图像\nOriginal Ice Image', fontsize=12, fontweight='bold')
        plt.axis('off')

        plt.subplot(4, 6, 2)
        im2 = plt.imshow(edge_map, cmap='hot')
        plt.title('边缘强度图\nEdge Strength Map', fontsize=12, fontweight='bold')
        plt.colorbar(im2, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 计算扩散张量的特征值和特征向量
        eigenvalues, eigenvectors = self.compute_diffusion_eigenvalues(D_xx, D_xy, D_yy)
        lambda1, lambda2 = eigenvalues
        v1, v2 = eigenvectors

        plt.subplot(4, 6, 3)
        im3 = plt.imshow(lambda1, cmap='viridis')
        plt.title('主扩散系数 λ₁\nPrincipal Diffusion Coeff.\n(跨边缘方向)', fontsize=12, fontweight='bold')
        plt.colorbar(im3, fraction=0.046, pad=0.04)
        plt.axis('off')

        plt.subplot(4, 6, 4)
        im4 = plt.imshow(lambda2, cmap='plasma')
        plt.title('次扩散系数 λ₂\nSecondary Diffusion Coeff.\n(沿边缘方向)', fontsize=12, fontweight='bold')
        plt.colorbar(im4, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 各向异性强度
        anisotropy_ratio = lambda2 / (lambda1 + 1e-8)
        plt.subplot(4, 6, 5)
        im5 = plt.imshow(anisotropy_ratio, cmap='jet', vmin=1, vmax=5)
        plt.title('各向异性比率\nAnisotropy Ratio λ₂/λ₁\n(>1表示各向异性)', fontsize=12, fontweight='bold')
        plt.colorbar(im5, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 扩散张量条件数
        condition_number = (lambda2 + 1e-8) / (lambda1 + 1e-8)
        plt.subplot(4, 6, 6)
        im6 = plt.imshow(condition_number, cmap='coolwarm')
        plt.title('张量条件数\nTensor Condition Number\n数值稳定性指标', fontsize=12, fontweight='bold')
        plt.colorbar(im6, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 第二行：扩散张量分量
        plt.subplot(4, 6, 7)
        im7 = plt.imshow(D_xx, cmap='RdBu')
        plt.title('扩散张量 D_xx\nDiffusion Tensor D_xx\n(x方向扩散)', fontsize=12, fontweight='bold')
        plt.colorbar(im7, fraction=0.046, pad=0.04)
        plt.axis('off')

        plt.subplot(4, 6, 8)
        im8 = plt.imshow(D_xy, cmap='RdBu')
        plt.title('扩散张量 D_xy\nDiffusion Tensor D_xy\n(交叉扩散)', fontsize=12, fontweight='bold')
        plt.colorbar(im8, fraction=0.046, pad=0.04)
        plt.axis('off')

        plt.subplot(4, 6, 9)
        im9 = plt.imshow(D_yy, cmap='RdBu')
        plt.title('扩散张量 D_yy\nDiffusion Tensor D_yy\n(y方向扩散)', fontsize=12, fontweight='bold')
        plt.colorbar(im9, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 扩散张量的迹和行列式
        trace = D_xx + D_yy
        determinant = D_xx * D_yy - D_xy**2

        plt.subplot(4, 6, 10)
        im10 = plt.imshow(trace, cmap='viridis')
        plt.title('张量迹 Tr(D)\nTensor Trace\n总扩散强度', fontsize=12, fontweight='bold')
        plt.colorbar(im10, fraction=0.046, pad=0.04)
        plt.axis('off')

        plt.subplot(4, 6, 11)
        im11 = plt.imshow(determinant, cmap='plasma')
        plt.title('张量行列式 Det(D)\nTensor Determinant\n各向异性程度', fontsize=12, fontweight='bold')
        plt.colorbar(im11, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 扩散椭圆可视化
        plt.subplot(4, 6, 12)
        self.plot_diffusion_ellipses(image, D_xx, D_xy, D_yy, step=15)
        plt.title('扩散椭圆场\nDiffusion Ellipse Field\n扩散张量几何表示', fontsize=12, fontweight='bold')
        plt.axis('off')

        # 第三行：扩散方向场和效果分析
        plt.subplot(4, 6, 13)
        self.plot_diffusion_field(image, v1, v2, lambda1, lambda2, step=12)
        plt.title('扩散方向场\nDiffusion Direction Field\n主次扩散方向', fontsize=12, fontweight='bold')
        plt.axis('off')

        plt.subplot(4, 6, 14)
        plt.imshow(segmented_image, cmap='gray')
        plt.title(f'分割结果\nSegmentation Result\n覆盖率: {info["ice_ratio"]:.1%}', fontsize=12, fontweight='bold')
        plt.axis('off')

        # 各向异性扩散vs各向同性扩散对比
        plt.subplot(4, 6, 15)
        phi = info['final_phi']
        iso_laplacian = cv2.Laplacian(phi, cv2.CV_64F)
        aniso_laplacian = self.compute_anisotropic_laplacian(phi, D_xx, D_xy, D_yy)
        diffusion_difference = np.abs(aniso_laplacian - iso_laplacian)

        im15 = plt.imshow(diffusion_difference, cmap='hot')
        plt.title('扩散差异\nDiffusion Difference\n|∇·(D∇φ) - ∇²φ|', fontsize=12, fontweight='bold')
        plt.colorbar(im15, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 边缘保持效果
        plt.subplot(4, 6, 16)
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_magnitude = np.sqrt(grad_phi_x**2 + grad_phi_y**2)
        edge_preservation_map = grad_phi_magnitude * edge_map

        im16 = plt.imshow(edge_preservation_map, cmap='viridis')
        plt.title('边缘保持效果\nEdge Preservation Effect\n梯度×边缘强度', fontsize=12, fontweight='bold')
        plt.colorbar(im16, fraction=0.046, pad=0.04)
        plt.axis('off')

        # 第四行：参数信息和统计分析
        plt.subplot(4, 6, 17)
        plt.axis('off')
        aniso_text = f"""各向异性扩散参数
Anisotropic Diffusion Parameters:

各向异性强度: {self.anisotropy_strength:.3f}
扩散阈值: {self.diffusion_threshold:.4f}

扩散张量方程:
D = λ₁n⊗n + λ₂t⊗t

其中:
• n: 梯度方向 (跨边缘)
• t: 切线方向 (沿边缘)
• λ₁: 跨边缘扩散系数 (小)
• λ₂: 沿边缘扩散系数 (大)

Perona-Malik抑制函数:
g(|∇I|) = 1/(1+(|∇I|/K)²)
K = {self.diffusion_threshold:.4f}"""

        plt.text(0.05, 0.95, aniso_text, transform=plt.gca().transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

        # 统计信息
        plt.subplot(4, 6, 18)
        plt.axis('off')
        stats_text = f"""扩散张量统计
Diffusion Tensor Statistics:

D_xx: μ={np.mean(D_xx):.4f}, σ={np.std(D_xx):.4f}
D_xy: μ={np.mean(D_xy):.4f}, σ={np.std(D_xy):.4f}
D_yy: μ={np.mean(D_yy):.4f}, σ={np.std(D_yy):.4f}

特征值统计:
λ₁: μ={np.mean(lambda1):.4f}, σ={np.std(lambda1):.4f}
λ₂: μ={np.mean(lambda2):.4f}, σ={np.std(lambda2):.4f}

各向异性度量:
平均比率: {np.mean(anisotropy_ratio):.3f}
比率方差: {np.var(anisotropy_ratio):.6f}
方向一致性: {self.compute_direction_coherence(D_xx, D_xy, D_yy):.3f}"""

        plt.text(0.05, 0.95, stats_text, transform=plt.gca().transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

        plt.suptitle('各向异性扩散完整分析\nComprehensive Anisotropic Diffusion Analysis',
                     fontsize=18, fontweight='bold')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_diffusion_field(self, image: np.ndarray, v1: Tuple[np.ndarray, np.ndarray],
                           v2: Tuple[np.ndarray, np.ndarray], lambda1: np.ndarray,
                           lambda2: np.ndarray, step: int = 10):
        """
        绘制扩散方向场

        Args:
            image: 背景图像
            v1, v2: 特征向量
            lambda1, lambda2: 特征值
            step: 采样步长
        """
        plt.imshow(image, cmap='gray', alpha=0.7)

        h, w = image.shape
        y, x = np.mgrid[0:h:step, 0:w:step]

        # 提取对应位置的特征向量和特征值
        v1_x_sub = v1[0][::step, ::step]
        v1_y_sub = v1[1][::step, ::step]
        v2_x_sub = v2[0][::step, ::step]
        v2_y_sub = v2[1][::step, ::step]
        lambda1_sub = lambda1[::step, ::step]
        lambda2_sub = lambda2[::step, ::step]

        # 绘制主扩散方向（红色，较短）- 跨边缘方向
        scale1 = 4 * lambda1_sub / (np.max(lambda1_sub) + 1e-8)
        plt.quiver(x, y, v1_x_sub * scale1, v1_y_sub * scale1,
                  color='red', alpha=0.7, width=0.003, scale=1, scale_units='xy',
                  label='主扩散方向 (跨边缘)')

        # 绘制次扩散方向（蓝色，较长）- 沿边缘方向
        scale2 = 6 * lambda2_sub / (np.max(lambda2_sub) + 1e-8)
        plt.quiver(x, y, v2_x_sub * scale2, v2_y_sub * scale2,
                  color='blue', alpha=0.8, width=0.002, scale=1, scale_units='xy',
                  label='次扩散方向 (沿边缘)')

        # 添加图例
        plt.legend(loc='upper right', fontsize=8)

    def plot_diffusion_ellipses(self, image: np.ndarray, D_xx: np.ndarray,
                              D_xy: np.ndarray, D_yy: np.ndarray, step: int = 20):
        """
        绘制扩散椭圆场

        Args:
            image: 背景图像
            D_xx, D_xy, D_yy: 扩散张量分量
            step: 采样步长
        """
        from matplotlib.patches import Ellipse
        import matplotlib.patches as patches

        plt.imshow(image, cmap='gray', alpha=0.6)

        h, w = image.shape

        for i in range(step//2, h, step):
            for j in range(step//2, w, step):
                # 提取当前位置的扩散张量
                Dxx = D_xx[i, j]
                Dxy = D_xy[i, j]
                Dyy = D_yy[i, j]

                # 计算特征值和特征向量
                trace = Dxx + Dyy
                det = Dxx * Dyy - Dxy**2
                discriminant = np.sqrt(max(trace**2 - 4*det, 0))

                lambda1 = 0.5 * (trace - discriminant)
                lambda2 = 0.5 * (trace + discriminant)

                if lambda2 > 1e-8:  # 避免退化情况
                    # 计算椭圆参数
                    if abs(Dxy) > 1e-8:
                        angle = 0.5 * np.arctan2(2*Dxy, Dxx - Dyy)
                    else:
                        angle = 0 if Dxx >= Dyy else np.pi/2

                    # 椭圆的半轴长度（与特征值成正比）
                    scale_factor = step * 0.3
                    width = scale_factor * np.sqrt(lambda2)
                    height = scale_factor * np.sqrt(lambda1)

                    # 创建椭圆
                    ellipse = Ellipse((j, i), width, height,
                                    angle=np.degrees(angle),
                                    facecolor='none',
                                    edgecolor='cyan',
                                    alpha=0.6,
                                    linewidth=1)
                    plt.gca().add_patch(ellipse)

    # ==================== 高级物理分析功能 ====================

    def compute_phase_field_curvature(self, phi: np.ndarray) -> np.ndarray:
        """
        计算相场函数的平均曲率

        Args:
            phi: 相场函数

        Returns:
            curvature: 平均曲率
        """
        # 计算一阶导数
        phi_x = np.gradient(phi, axis=1)
        phi_y = np.gradient(phi, axis=0)

        # 计算二阶导数
        phi_xx = np.gradient(phi_x, axis=1)
        phi_yy = np.gradient(phi_y, axis=0)
        phi_xy = np.gradient(phi_x, axis=0)

        # 计算梯度模长
        grad_norm = np.sqrt(phi_x**2 + phi_y**2 + 1e-8)

        # 计算平均曲率 κ = ∇·(∇φ/|∇φ|)
        curvature = (phi_xx * phi_y**2 - 2*phi_xy * phi_x * phi_y + phi_yy * phi_x**2) / (grad_norm**3 + 1e-8)

        return curvature

    def compute_interface_normal_velocity(self, phi_old: np.ndarray, phi_new: np.ndarray,
                                        dt: float) -> np.ndarray:
        """
        计算界面法向速度

        Args:
            phi_old: 上一时刻的相场函数
            phi_new: 当前时刻的相场函数
            dt: 时间步长

        Returns:
            normal_velocity: 界面法向速度
        """
        # 时间导数
        phi_t = (phi_new - phi_old) / dt

        # 空间梯度
        phi_x = np.gradient(phi_new, axis=1)
        phi_y = np.gradient(phi_new, axis=0)
        grad_norm = np.sqrt(phi_x**2 + phi_y**2 + 1e-8)

        # 法向速度 V_n = ∂φ/∂t / |∇φ|
        normal_velocity = phi_t / (grad_norm + 1e-8)

        return normal_velocity

    def analyze_interface_dynamics(self, info: Dict) -> Dict:
        """
        分析界面动力学特性

        Args:
            info: 算法信息字典

        Returns:
            dynamics_analysis: 界面动力学分析结果
        """
        phi = info['final_phi']

        # 计算界面曲率
        curvature = self.compute_phase_field_curvature(phi)

        # 计算界面梯度
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_magnitude = np.sqrt(grad_phi_x**2 + grad_phi_y**2)

        # 界面区域定义
        interface_mask = (grad_phi_magnitude > 0.1) & (phi > 0.1) & (phi < 0.9)

        if np.any(interface_mask):
            # 界面曲率统计
            interface_curvature = curvature[interface_mask]
            mean_curvature = np.mean(interface_curvature)
            curvature_variance = np.var(interface_curvature)

            # 界面复杂度
            interface_complexity = np.std(grad_phi_magnitude[interface_mask])

            # 界面长度
            interface_length = np.sum(grad_phi_magnitude)

            # 界面分形维数估计
            fractal_dimension = self.estimate_fractal_dimension(interface_mask)

        else:
            mean_curvature = 0
            curvature_variance = 0
            interface_complexity = 0
            interface_length = 0
            fractal_dimension = 1.0

        dynamics_analysis = {
            'mean_curvature': mean_curvature,
            'curvature_variance': curvature_variance,
            'interface_complexity': interface_complexity,
            'interface_length': interface_length,
            'fractal_dimension': fractal_dimension,
            'interface_area_ratio': np.sum(interface_mask) / interface_mask.size,
            'curvature_distribution': {
                'min': np.min(curvature),
                'max': np.max(curvature),
                'mean': np.mean(curvature),
                'std': np.std(curvature)
            }
        }

        return dynamics_analysis

    def estimate_fractal_dimension(self, interface_mask: np.ndarray) -> float:
        """
        估计界面的分形维数

        Args:
            interface_mask: 界面掩码

        Returns:
            fractal_dimension: 分形维数估计
        """
        # 使用盒计数法估计分形维数
        sizes = [2, 4, 8, 16, 32]
        counts = []

        for size in sizes:
            count = 0
            h, w = interface_mask.shape
            for i in range(0, h, size):
                for j in range(0, w, size):
                    box = interface_mask[i:i+size, j:j+size]
                    if np.any(box):
                        count += 1
            counts.append(count)

        # 线性拟合 log(count) vs log(1/size)
        if len(counts) > 1 and all(c > 0 for c in counts):
            log_sizes = np.log(1.0 / np.array(sizes))
            log_counts = np.log(counts)

            # 最小二乘拟合
            A = np.vstack([log_sizes, np.ones(len(log_sizes))]).T
            slope, _ = np.linalg.lstsq(A, log_counts, rcond=None)[0]
            fractal_dimension = slope
        else:
            fractal_dimension = 1.0

        # 限制在合理范围内
        fractal_dimension = np.clip(fractal_dimension, 1.0, 2.0)

        return fractal_dimension

    # ==================== 高级相场理论改进模块 ====================

    def anisotropic_interface_energy(self, phi: np.ndarray, orientation_field: np.ndarray = None) -> float:
        """
        各向异性界面能量密度
        E = ∫ γ(n)|∇φ|² dΩ，其中γ(n)是方向依赖的界面张力

        Args:
            phi: 相场函数
            orientation_field: 方向场（可选）

        Returns:
            anisotropic_energy: 各向异性界面能量
        """
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_magnitude = np.sqrt(grad_phi_x**2 + grad_phi_y**2 + 1e-8)

        if orientation_field is None:
            # 基于梯度方向的自动方向场
            orientation_field = np.arctan2(grad_phi_y, grad_phi_x)

        # 方向依赖的界面张力（四重对称性，模拟海冰晶体结构）
        gamma = 1.0 + 0.15 * np.cos(4 * orientation_field)  # 各向异性强度15%

        # 各向异性界面能量
        anisotropic_energy = 0.5 * self.epsilon * np.sum(gamma * grad_magnitude**2)

        return anisotropic_energy

    def higher_order_gradient_energy(self, phi: np.ndarray) -> float:
        """
        高阶梯度能量项，提高界面描述精度
        E = ∫ [ε₁|∇φ|² + ε₂|∇²φ|² + ε₃|∇³φ|²] dΩ

        Args:
            phi: 相场函数

        Returns:
            higher_order_energy: 高阶梯度能量
        """
        # 一阶梯度项
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        first_order = np.sum(grad_phi_x**2 + grad_phi_y**2)

        # 二阶梯度项（拉普拉斯）
        laplacian = cv2.Laplacian(phi, cv2.CV_64F)
        second_order = np.sum(laplacian**2)

        # 三阶梯度项（双调和算子的近似）
        laplacian_of_laplacian = cv2.Laplacian(laplacian, cv2.CV_64F)
        third_order = np.sum(laplacian_of_laplacian**2)

        # 能量系数
        epsilon1 = self.epsilon
        epsilon2 = 0.1 * self.epsilon  # 二阶项系数
        epsilon3 = 0.01 * self.epsilon  # 三阶项系数

        higher_order_energy = 0.5 * (epsilon1 * first_order +
                                    epsilon2 * second_order +
                                    epsilon3 * third_order)

        return higher_order_energy

    def cahn_hilliard_evolution(self, phi: np.ndarray, image: np.ndarray,
                               edge_map: np.ndarray = None) -> np.ndarray:
        """
        Cahn-Hilliard方程（守恒型）：∂φ/∂t = ∇·(M∇μ)
        其中 μ = δF/δφ = -ε∇²φ + (1/ε)W'(φ) + F'(I)
        适用于质量守恒的相分离过程

        Args:
            phi: 相场函数
            image: 图像驱动项
            edge_map: 边缘信息（可选）

        Returns:
            dphi_dt: 相场时间导数
        """
        # 计算化学势 μ = δF/δφ
        laplacian_phi = cv2.Laplacian(phi, cv2.CV_64F)

        # 双能谷势的导数
        double_well_derivative = phi * (1 - phi) * (1 - 2 * phi)

        # 图像驱动项的导数
        image_force_derivative = self.lambda_ * (image - 0.5)

        # 化学势
        chemical_potential = (-self.epsilon * laplacian_phi +
                            (1/self.epsilon) * double_well_derivative +
                            image_force_derivative)

        # 计算化学势的梯度
        mu_grad_x = np.gradient(chemical_potential, axis=1)
        mu_grad_y = np.gradient(chemical_potential, axis=0)

        # 迁移率场 M(φ) - 在界面处迁移率最大
        mobility = self.mu * phi * (1 - phi) + 0.01 * self.mu  # 添加小的常数项避免零迁移率

        # 自适应迁移率（基于边缘信息）
        if edge_map is not None:
            mobility = mobility * (1.0 + 0.5 * edge_map)  # 在边缘处增强迁移率

        # Cahn-Hilliard演化：∇·(M∇μ)
        flux_x = mobility * mu_grad_x
        flux_y = mobility * mu_grad_y

        dphi_dt = np.gradient(flux_x, axis=1) + np.gradient(flux_y, axis=0)

        return dphi_dt

    def adaptive_mesh_refinement(self, phi: np.ndarray, image: np.ndarray = None,
                               edge_map: np.ndarray = None,
                               interface_threshold: float = 0.1,
                               small_scale_threshold: float = 0.05) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        改进的自适应网格细化
        专门针对小尺度特征和边缘区域进行细化

        Args:
            phi: 相场函数
            image: 原始图像（用于检测小尺度特征）
            edge_map: 边缘强度图
            interface_threshold: 界面细化阈值
            small_scale_threshold: 小尺度特征阈值

        Returns:
            refined_phi: 细化后的相场函数
            refinement_mask: 细化区域掩码
            refinement_info: 细化信息字典
        """
        h, w = phi.shape

        # 1. 界面区域检测
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        interface_indicator = np.sqrt(grad_phi_x**2 + grad_phi_y**2)
        interface_mask = interface_indicator > interface_threshold

        # 2. 小尺度特征检测
        small_scale_mask = self.detect_small_scale_features(phi, image, small_scale_threshold)

        # 3. 边缘区域检测
        edge_mask = np.zeros_like(phi, dtype=bool)
        if edge_map is not None:
            edge_mask = self.detect_edge_regions(edge_map, phi)

        # 4. 曲率高的区域检测
        curvature_mask = self.detect_high_curvature_regions(phi)

        # 5. 组合所有需要细化的区域
        refinement_mask = (interface_mask | small_scale_mask | edge_mask | curvature_mask)

        # 6. 形态学操作：扩展细化区域以确保连续性
        from scipy.ndimage import binary_dilation
        structure = np.ones((3, 3))  # 3x3结构元素
        refinement_mask = binary_dilation(refinement_mask, structure=structure, iterations=1)

        # 7. 执行多级细化
        refined_phi = self.multi_level_refinement(phi, refinement_mask, image)

        # 8. 统计细化信息
        refinement_info = {
            'total_refinement_ratio': np.sum(refinement_mask) / refinement_mask.size,
            'interface_refinement_ratio': np.sum(interface_mask) / refinement_mask.size,
            'small_scale_refinement_ratio': np.sum(small_scale_mask) / refinement_mask.size,
            'edge_refinement_ratio': np.sum(edge_mask) / refinement_mask.size,
            'curvature_refinement_ratio': np.sum(curvature_mask) / refinement_mask.size,
            'refinement_regions': {
                'interface': interface_mask,
                'small_scale': small_scale_mask,
                'edge': edge_mask,
                'curvature': curvature_mask
            }
        }

        return refined_phi, refinement_mask, refinement_info

    def detect_small_scale_features(self, phi: np.ndarray, image: np.ndarray = None,
                                  threshold: float = 0.05) -> np.ndarray:
        """
        检测小尺度特征区域

        Args:
            phi: 相场函数
            image: 原始图像
            threshold: 小尺度特征阈值

        Returns:
            small_scale_mask: 小尺度特征掩码
        """
        small_scale_mask = np.zeros_like(phi, dtype=bool)

        # 方法1: 基于相场函数的小尺度检测
        # 使用多尺度拉普拉斯算子检测小尺度变化
        laplacian_small = cv2.Laplacian(phi, cv2.CV_64F, ksize=3)  # 小尺度
        laplacian_large = cv2.Laplacian(phi, cv2.CV_64F, ksize=5)  # 大尺度

        # 小尺度特征指示器
        small_scale_indicator = np.abs(laplacian_small - laplacian_large)
        small_scale_mask |= (small_scale_indicator > threshold)

        # 方法2: 基于原始图像的小尺度检测（如果提供）
        if image is not None:
            # 使用高斯差分检测小尺度特征
            from scipy.ndimage import gaussian_filter

            img_normalized = image / 255.0 if np.max(image) > 1.0 else image

            # 多尺度高斯差分
            sigma_small = 0.5
            sigma_large = 2.0

            gaussian_small = gaussian_filter(img_normalized, sigma_small)
            gaussian_large = gaussian_filter(img_normalized, sigma_large)

            dog = np.abs(gaussian_small - gaussian_large)
            small_scale_mask |= (dog > 0.1)

        # 方法3: 基于局部方差检测小尺度变化
        from scipy.ndimage import uniform_filter

        # 计算局部方差
        local_mean = uniform_filter(phi, size=5)
        local_variance = uniform_filter(phi**2, size=5) - local_mean**2

        # 高方差区域通常包含小尺度特征
        small_scale_mask |= (local_variance > 0.02)

        # 方法4: 检测孤立的小区域
        small_regions_mask = self.detect_small_isolated_regions(phi)
        small_scale_mask |= small_regions_mask

        return small_scale_mask

    def detect_small_isolated_regions(self, phi: np.ndarray, min_size: int = 50) -> np.ndarray:
        """
        检测小的孤立区域

        Args:
            phi: 相场函数
            min_size: 最小区域大小

        Returns:
            small_regions_mask: 小区域掩码
        """
        from scipy.ndimage import label

        # 二值化相场函数
        binary_phi = (phi > 0.5).astype(int)

        # 连通分量分析
        labeled_regions, num_regions = label(binary_phi)

        small_regions_mask = np.zeros_like(phi, dtype=bool)

        for region_id in range(1, num_regions + 1):
            region_mask = (labeled_regions == region_id)
            region_size = np.sum(region_mask)

            # 标记小区域
            if region_size < min_size:
                small_regions_mask |= region_mask

                # 扩展小区域周围的细化区域
                from scipy.ndimage import binary_dilation
                expanded_region = binary_dilation(region_mask, iterations=3)
                small_regions_mask |= expanded_region

        return small_regions_mask

    def detect_edge_regions(self, edge_map: np.ndarray, phi: np.ndarray,
                          edge_threshold: float = 0.3) -> np.ndarray:
        """
        检测需要细化的边缘区域

        Args:
            edge_map: 边缘强度图
            phi: 相场函数
            edge_threshold: 边缘阈值

        Returns:
            edge_mask: 边缘区域掩码
        """
        # 强边缘区域
        strong_edges = edge_map > edge_threshold

        # 边缘与相场界面的交叉区域
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        phi_gradient = np.sqrt(grad_phi_x**2 + grad_phi_y**2)

        # 相场界面区域
        interface_region = phi_gradient > 0.1

        # 边缘与界面的交叉区域需要特别细化
        edge_interface_intersection = strong_edges & interface_region

        # 扩展边缘区域
        from scipy.ndimage import binary_dilation
        edge_mask = binary_dilation(edge_interface_intersection, iterations=2)

        # 添加所有强边缘区域
        edge_mask |= strong_edges

        return edge_mask

    def detect_high_curvature_regions(self, phi: np.ndarray, curvature_threshold: float = 0.5) -> np.ndarray:
        """
        检测高曲率区域

        Args:
            phi: 相场函数
            curvature_threshold: 曲率阈值

        Returns:
            curvature_mask: 高曲率区域掩码
        """
        # 计算相场函数的曲率
        curvature = self.compute_phase_field_curvature(phi)

        # 高曲率区域
        high_curvature = np.abs(curvature) > curvature_threshold

        # 只在界面附近考虑曲率
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        interface_region = np.sqrt(grad_phi_x**2 + grad_phi_y**2) > 0.1

        curvature_mask = high_curvature & interface_region

        return curvature_mask

    def multi_level_refinement(self, phi: np.ndarray, refinement_mask: np.ndarray,
                             image: np.ndarray = None) -> np.ndarray:
        """
        多级网格细化

        Args:
            phi: 原始相场函数
            refinement_mask: 细化区域掩码
            image: 原始图像（可选）

        Returns:
            refined_phi: 多级细化后的相场函数
        """
        refined_phi = phi.copy()

        if not np.any(refinement_mask):
            return refined_phi

        # 级别1: 双线性插值平滑
        refined_phi = self.bilinear_smoothing(refined_phi, refinement_mask)

        # 级别2: 基于边缘保持的平滑
        if image is not None:
            refined_phi = self.edge_preserving_smoothing(refined_phi, refinement_mask, image)

        # 级别3: 各向异性扩散平滑
        refined_phi = self.anisotropic_smoothing(refined_phi, refinement_mask)

        return refined_phi

    def bilinear_smoothing(self, phi: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        双线性插值平滑
        """
        from scipy.ndimage import gaussian_filter

        smoothed_phi = gaussian_filter(phi, sigma=0.5)

        # 只在标记区域应用平滑
        refined_phi = np.where(mask, smoothed_phi, phi)

        return refined_phi

    def edge_preserving_smoothing(self, phi: np.ndarray, mask: np.ndarray,
                                image: np.ndarray) -> np.ndarray:
        """
        边缘保持平滑
        """
        # 计算图像梯度
        img_normalized = image / 255.0 if np.max(image) > 1.0 else image
        grad_img_x = np.gradient(img_normalized, axis=1)
        grad_img_y = np.gradient(img_normalized, axis=0)
        grad_img_magnitude = np.sqrt(grad_img_x**2 + grad_img_y**2)

        # 边缘保持权重
        edge_weight = np.exp(-grad_img_magnitude / 0.1)

        # 加权平滑
        from scipy.ndimage import gaussian_filter
        smoothed_phi = gaussian_filter(phi, sigma=0.8)

        # 边缘保持混合
        refined_phi = edge_weight * smoothed_phi + (1 - edge_weight) * phi

        # 只在标记区域应用
        refined_phi = np.where(mask, refined_phi, phi)

        return refined_phi

    def anisotropic_smoothing(self, phi: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        各向异性扩散平滑
        """
        if not self.use_anisotropic_diffusion:
            return phi

        # 计算扩散张量
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_magnitude = np.sqrt(grad_phi_x**2 + grad_phi_y**2 + 1e-8)

        # 简化的各向异性扩散
        K = self.diffusion_threshold
        g = 1.0 / (1.0 + (grad_magnitude / K)**2)

        # 各向异性拉普拉斯
        laplacian = cv2.Laplacian(phi, cv2.CV_64F)
        aniso_laplacian = g * laplacian

        # 应用扩散
        refined_phi = phi + 0.01 * aniso_laplacian
        refined_phi = np.clip(refined_phi, 0, 1)

        # 只在标记区域应用
        refined_phi = np.where(mask, refined_phi, phi)

        return refined_phi

    def interpolate_interface_regions(self, phi: np.ndarray, refinement_mask: np.ndarray) -> np.ndarray:
        """
        在界面区域进行插值细化

        Args:
            phi: 原始相场函数
            refinement_mask: 需要细化的区域掩码

        Returns:
            interpolated_phi: 插值后的相场函数
        """
        from scipy.ndimage import zoom

        # 创建细化后的网格
        refined_phi = phi.copy()

        # 在标记区域进行局部细化
        # 这里使用简化的方法：对整个域进行平滑处理
        if np.any(refinement_mask):
            # 使用高斯滤波进行平滑
            from scipy.ndimage import gaussian_filter
            smoothed_phi = gaussian_filter(phi, sigma=0.5)

            # 只在细化区域应用平滑结果
            refined_phi = np.where(refinement_mask, smoothed_phi, phi)

        return refined_phi

    def enforce_mass_conservation(self, phi_new: np.ndarray, phi_old: np.ndarray,
                                target_mass: float = None) -> np.ndarray:
        """
        强制质量守恒约束
        确保总的海冰质量在演化过程中保持守恒

        Args:
            phi_new: 新的相场函数
            phi_old: 旧的相场函数
            target_mass: 目标质量（如果为None，则使用phi_old的质量）

        Returns:
            phi_corrected: 质量守恒校正后的相场函数
        """
        if target_mass is None:
            target_mass = np.sum(phi_old)

        current_mass = np.sum(phi_new)
        mass_error = current_mass - target_mass

        # 均匀分布质量误差
        correction = mass_error / phi_new.size
        phi_corrected = phi_new - correction

        # 确保相场函数在有效范围内
        phi_corrected = np.clip(phi_corrected, 0, 1)

        # 验证质量守恒
        final_mass = np.sum(phi_corrected)
        mass_conservation_error = abs(final_mass - target_mass) / target_mass

        if mass_conservation_error > 1e-6:
            print(f"⚠️ 质量守恒误差: {mass_conservation_error:.2e}")

        return phi_corrected

    def ensure_energy_decrease(self, phi_new: np.ndarray, phi_old: np.ndarray,
                             image: np.ndarray, edge_map: np.ndarray = None) -> Tuple[np.ndarray, float]:
        """
        确保能量单调递减
        如果能量增加，则减小时间步长重新计算

        Args:
            phi_new: 新的相场函数
            phi_old: 旧的相场函数
            image: 图像
            edge_map: 边缘信息（可选）

        Returns:
            phi_corrected: 能量递减校正后的相场函数
            actual_dt: 实际使用的时间步长
        """
        energy_old = self._compute_total_energy(phi_old, image, edge_map)
        energy_new = self._compute_total_energy(phi_new, image, edge_map)

        actual_dt = self.dt
        phi_corrected = phi_new.copy()

        # 如果能量增加，则减小时间步长
        max_iterations = 5
        iteration = 0

        while energy_new > energy_old and iteration < max_iterations:
            # 减小时间步长
            actual_dt *= 0.5

            # 重新计算相场演化
            dphi = (phi_new - phi_old) / self.dt  # 原始变化率
            phi_corrected = phi_old + actual_dt * dphi
            phi_corrected = np.clip(phi_corrected, 0, 1)

            # 重新计算能量
            energy_new = self._compute_total_energy(phi_corrected, image, edge_map)
            iteration += 1

        if iteration >= max_iterations:
            print(f"⚠️ 能量递减约束失败，使用原始结果")
            phi_corrected = phi_new
            actual_dt = self.dt

        return phi_corrected, actual_dt

    def _compute_total_energy(self, phi: np.ndarray, image: np.ndarray,
                            edge_map: np.ndarray = None) -> float:
        """
        计算总能量（包括高阶项）

        Args:
            phi: 相场函数
            image: 图像
            edge_map: 边缘信息

        Returns:
            total_energy: 总能量
        """
        # 基础Ginzburg-Landau能量
        basic_energy = self._compute_energy(phi, image, edge_map)

        # 高阶梯度能量
        higher_order_energy = self.higher_order_gradient_energy(phi)

        # 各向异性界面能量
        anisotropic_energy = self.anisotropic_interface_energy(phi)

        total_energy = basic_energy + 0.1 * higher_order_energy + 0.05 * anisotropic_energy

        return total_energy

    def adaptive_time_stepping(self, phi: np.ndarray, phi_old: np.ndarray,
                             target_change: float = 0.01) -> float:
        """
        自适应时间步长控制
        基于相场变化率动态调整时间步长

        Args:
            phi: 当前相场函数
            phi_old: 上一步相场函数
            target_change: 目标变化率

        Returns:
            dt_new: 新的时间步长
        """
        # 计算相场变化率
        phi_change = np.max(np.abs(phi - phi_old))
        mean_change = np.mean(np.abs(phi - phi_old))

        # 基于最大变化率调整
        if phi_change > target_change:
            dt_factor = 0.8  # 减小时间步长
        elif phi_change < target_change * 0.5:
            dt_factor = 1.2  # 增大时间步长
        else:
            dt_factor = 1.0  # 保持不变

        # 基于平均变化率的微调
        if mean_change < target_change * 0.1:
            dt_factor *= 1.1  # 进一步增大
        elif mean_change > target_change * 0.8:
            dt_factor *= 0.9  # 进一步减小

        dt_new = self.dt * dt_factor

        # 限制时间步长范围
        dt_min = 0.001
        dt_max = 0.02
        dt_new = np.clip(dt_new, dt_min, dt_max)

        # CFL条件检查
        cfl_limit = 0.25 * self.epsilon**2 / self.mu
        if dt_new > cfl_limit:
            dt_new = cfl_limit
            print(f"⚠️ 时间步长受CFL条件限制: dt = {dt_new:.6f}")

        return dt_new

    # ==================== 改进的相场演化函数 ====================

    def advanced_phase_field_evolution_step(self, phi: np.ndarray, image: np.ndarray,
                                           edge_map: np.ndarray, texture_feature: np.ndarray,
                                           use_cahn_hilliard: bool = False,
                                           use_mass_conservation: bool = True,
                                           use_energy_constraint: bool = True,
                                           use_adaptive_mesh: bool = False) -> Tuple[np.ndarray, Dict]:
        """
        改进的相场演化步骤
        集成所有高级功能：Cahn-Hilliard方程、质量守恒、能量约束、自适应网格等

        Args:
            phi: 当前相场函数
            image: 归一化图像
            edge_map: 边缘特征图
            texture_feature: 纹理特征图
            use_cahn_hilliard: 是否使用Cahn-Hilliard方程
            use_mass_conservation: 是否强制质量守恒
            use_energy_constraint: 是否强制能量递减
            use_adaptive_mesh: 是否使用自适应网格

        Returns:
            phi_new: 更新后的相场函数
            evolution_info: 演化过程信息
        """
        phi_old = phi.copy()
        evolution_info = {}

        # 自适应网格细化（针对小尺度和边缘区域）
        if use_adaptive_mesh:
            phi, refinement_mask, refinement_info = self.adaptive_mesh_refinement(
                phi, image, edge_map,
                interface_threshold=0.1,
                small_scale_threshold=0.05
            )
            evolution_info['refinement_applied'] = refinement_info['total_refinement_ratio']
            evolution_info['refinement_breakdown'] = {
                'interface': refinement_info['interface_refinement_ratio'],
                'small_scale': refinement_info['small_scale_refinement_ratio'],
                'edge': refinement_info['edge_refinement_ratio'],
                'curvature': refinement_info['curvature_refinement_ratio']
            }
        else:
            evolution_info['refinement_applied'] = 0.0
            evolution_info['refinement_breakdown'] = {
                'interface': 0.0, 'small_scale': 0.0, 'edge': 0.0, 'curvature': 0.0
            }

        # 选择演化方程
        if use_cahn_hilliard:
            # 使用Cahn-Hilliard方程（守恒型）
            dphi = self.cahn_hilliard_evolution(phi, image, edge_map)
            evolution_info['evolution_type'] = 'Cahn-Hilliard'
        else:
            # 使用改进的Allen-Cahn方程
            dphi = self.improved_allen_cahn_evolution(phi, image, edge_map, texture_feature)
            evolution_info['evolution_type'] = 'Improved Allen-Cahn'

        # 时间积分
        phi_new = phi + self.dt * dphi
        phi_new = np.clip(phi_new, 0, 1)

        # 质量守恒约束
        if use_mass_conservation:
            target_mass = np.sum(phi_old)
            phi_new = self.enforce_mass_conservation(phi_new, phi_old, target_mass)
            mass_error = abs(np.sum(phi_new) - target_mass) / target_mass
            evolution_info['mass_conservation_error'] = mass_error

        # 能量递减约束
        actual_dt = self.dt
        if use_energy_constraint:
            phi_new, actual_dt = self.ensure_energy_decrease(phi_new, phi_old, image, edge_map)
            evolution_info['actual_dt'] = actual_dt
            evolution_info['dt_adjusted'] = actual_dt != self.dt

        # 自适应时间步长
        new_dt = self.adaptive_time_stepping(phi_new, phi_old)
        evolution_info['recommended_dt'] = new_dt
        evolution_info['dt_change_factor'] = new_dt / self.dt

        # 计算演化统计
        phi_change = np.mean(np.abs(phi_new - phi_old))
        max_change = np.max(np.abs(phi_new - phi_old))
        evolution_info['mean_change'] = phi_change
        evolution_info['max_change'] = max_change

        # 能量变化
        energy_old = self._compute_total_energy(phi_old, image, edge_map)
        energy_new = self._compute_total_energy(phi_new, image, edge_map)
        evolution_info['energy_change'] = energy_new - energy_old
        evolution_info['energy_decrease'] = energy_new < energy_old

        return phi_new, evolution_info

    def improved_allen_cahn_evolution(self, phi: np.ndarray, image: np.ndarray,
                                     edge_map: np.ndarray, texture_feature: np.ndarray) -> np.ndarray:
        """
        改进的Allen-Cahn演化方程
        包含高阶梯度项和各向异性界面能量

        Args:
            phi: 相场函数
            image: 图像
            edge_map: 边缘信息
            texture_feature: 纹理特征

        Returns:
            dphi: 相场时间导数
        """
        # 计算相场梯度
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_norm = np.sqrt(grad_phi_x**2 + grad_phi_y**2 + 1e-8)

        # 标准拉普拉斯项
        if self.use_anisotropic_diffusion and edge_map is not None:
            D_xx, D_xy, D_yy = self.compute_anisotropic_diffusion_tensor(image, edge_map)
            laplacian = self.compute_anisotropic_laplacian(phi, D_xx, D_xy, D_yy)
        else:
            laplacian = cv2.Laplacian(phi, cv2.CV_64F)

        # 高阶拉普拉斯项（双调和算子）
        higher_order_laplacian = cv2.Laplacian(laplacian, cv2.CV_64F)

        # 各向异性界面张力项
        orientation_field = np.arctan2(grad_phi_y, grad_phi_x)
        gamma = 1.0 + 0.15 * np.cos(4 * orientation_field)  # 各向异性界面张力
        gamma_laplacian = cv2.Laplacian(gamma, cv2.CV_64F)

        # 各向异性项：∇·(γ∇φ) = γ∇²φ + ∇γ·∇φ
        anisotropic_term = gamma * laplacian + gamma_laplacian * grad_phi_norm

        # 自适应参数计算
        lambda_adaptive, mu_adaptive, epsilon_adaptive = self.compute_adaptive_parameters(edge_map)

        # 图像驱动项
        image_force = lambda_adaptive * (image - 0.5)

        # 纹理特征耦合
        if self.use_texture_feature and texture_feature is not None:
            image_force += 0.3 * lambda_adaptive * texture_feature

        # 边缘保持正则化项
        edge_preservation_term = 0
        if self.use_edge_preservation and edge_map is not None:
            edge_preservation_term = 0.2 * edge_map * grad_phi_norm

        # 双能谷势项
        double_well_term = phi * (1 - phi) * (1 - 2 * phi)

        # 改进的Allen-Cahn方程
        # ∂φ/∂t = μ[ε∇·(γ∇φ) - ε₂∇⁴φ - (1/ε)W'(φ)] + F(I) + 边缘保持项
        epsilon2 = 0.1 * epsilon_adaptive  # 高阶项系数

        dphi = (mu_adaptive * (epsilon_adaptive * anisotropic_term -
                              epsilon2 * higher_order_laplacian -
                              (1/epsilon_adaptive) * double_well_term) +
                image_force + edge_preservation_term)

        return dphi

    def segment_with_advanced_evolution(self, image: np.ndarray, gray_image: np.ndarray = None,
                                      binary_threshold: float = 0.15,
                                      use_cahn_hilliard: bool = False,
                                      use_mass_conservation: bool = True,
                                      use_energy_constraint: bool = True,
                                      use_adaptive_mesh: bool = False,
                                      show_progress: bool = True) -> Tuple[np.ndarray, Dict]:
        """
        使用高级相场演化的分割函数

        Args:
            image: 输入图像
            gray_image: 原始灰度图像
            binary_threshold: 二值化阈值
            use_cahn_hilliard: 是否使用Cahn-Hilliard方程
            use_mass_conservation: 是否强制质量守恒
            use_energy_constraint: 是否强制能量约束
            use_adaptive_mesh: 是否使用自适应网格
            show_progress: 是否显示进度

        Returns:
            segmented_image: 分割结果
            info: 详细信息字典
        """
        start_time = time.time()

        # 图像预处理
        if np.max(image) > 1.0:
            img_normalized = image / 255.0
        else:
            img_normalized = image.copy()

        if show_progress:
            print(f"🚀 开始高级APSIS分割 (图像尺寸: {img_normalized.shape})")
            print(f"📋 高级功能配置:")
            print(f"   Cahn-Hilliard方程: {'✅' if use_cahn_hilliard else '❌'}")
            print(f"   质量守恒约束: {'✅' if use_mass_conservation else '❌'}")
            print(f"   能量递减约束: {'✅' if use_energy_constraint else '❌'}")
            print(f"   自适应网格: {'✅' if use_adaptive_mesh else '❌'}")

        # 特征提取
        edge_map = None
        texture_feature = None

        if (self.use_adaptive_weights or self.use_edge_preservation or
            self.use_anisotropic_diffusion or use_cahn_hilliard):
            edge_map = self.extract_edge_features(img_normalized)
            if show_progress:
                print(f"✓ 边缘特征提取完成")

        if self.use_texture_feature:
            texture_feature = self.extract_texture_features(img_normalized)
            if show_progress:
                print(f"✓ 纹理特征提取完成")

        # 相场函数初始化
        init_image = gray_image if gray_image is not None else image
        if np.max(init_image) <= 1.0:
            init_image = (init_image * 255).astype(np.uint8)

        phi = self.otsu_initialization(init_image)
        initial_ice_ratio = np.mean(phi)

        if show_progress:
            print(f"✓ 相场初始化完成 (初始海冰比例: {initial_ice_ratio:.2%})")

        # 高级相场演化迭代
        self.energy_history = []
        evolution_history = []
        adaptive_dt_history = []

        current_dt = self.dt

        for i in range(self.max_iterations):
            phi_old = phi.copy()

            # 执行高级相场演化步骤
            phi, evolution_info = self.advanced_phase_field_evolution_step(
                phi, img_normalized, edge_map, texture_feature,
                use_cahn_hilliard, use_mass_conservation,
                use_energy_constraint, use_adaptive_mesh
            )

            evolution_history.append(evolution_info)

            # 更新时间步长
            if evolution_info.get('recommended_dt'):
                current_dt = evolution_info['recommended_dt']
                self.dt = current_dt
                adaptive_dt_history.append(current_dt)

            # 计算能量
            if i % 50 == 0:
                energy = self._compute_total_energy(phi, img_normalized, edge_map)
                self.energy_history.append(energy)

            # 收敛性检查
            if self.check_convergence(phi_old, phi):
                if show_progress:
                    print(f"✓ 算法在第 {i + 1} 次迭代后收敛")
                self.convergence_info = {
                    'converged': True,
                    'iterations': i + 1
                }
                break

            # 进度显示
            if show_progress and i % 100 == 0:
                phi_change = evolution_info['mean_change']
                current_ice_ratio = np.mean(phi > 0.5)
                energy_change = evolution_info.get('energy_change', 0)
                print(f"⏳ 迭代 {i}: 变化量={phi_change:.6f}, 海冰比例={current_ice_ratio:.2%}, "
                      f"能量变化={energy_change:.2e}, dt={current_dt:.6f}")

        else:
            if show_progress:
                print(f"⚠️ 达到最大迭代次数 {self.max_iterations}")
            self.convergence_info = {
                'converged': False,
                'iterations': self.max_iterations
            }

        # 二值化得到最终分割结果
        segmented_image = (phi > binary_threshold).astype(np.uint8) * 255

        # 结果统计
        ice_pixels = np.sum(segmented_image > 0)
        total_pixels = segmented_image.size
        ice_ratio = ice_pixels / total_pixels

        self.computation_time = time.time() - start_time

        if show_progress:
            print(f"🎉 高级分割完成！")
            print(f"📊 海冰覆盖率: {ice_ratio:.2%}")
            print(f"⏱️ 计算时间: {self.computation_time:.2f}秒")

        # 构建返回信息
        info = {
            'convergence_info': self.convergence_info,
            'ice_ratio': ice_ratio,
            'computation_time': self.computation_time,
            'edge_map': edge_map,
            'texture_feature': texture_feature,
            'final_phi': phi,
            'original_image': image,
            'energy_history': self.energy_history,
            'initial_ice_ratio': initial_ice_ratio,
            'evolution_history': evolution_history,
            'adaptive_dt_history': adaptive_dt_history,
            'advanced_features': {
                'cahn_hilliard': use_cahn_hilliard,
                'mass_conservation': use_mass_conservation,
                'energy_constraint': use_energy_constraint,
                'adaptive_mesh': use_adaptive_mesh
            },
            'parameters': {
                'epsilon': self.epsilon,
                'mu': self.mu,
                'lambda': self.lambda_,
                'dt': self.dt,
                'binary_threshold': binary_threshold,
                'use_anisotropic_diffusion': self.use_anisotropic_diffusion,
                'anisotropy_strength': self.anisotropy_strength,
                'diffusion_threshold': self.diffusion_threshold,
                'use_adaptive_weights': self.use_adaptive_weights,
                'use_edge_preservation': self.use_edge_preservation,
                'use_texture_feature': self.use_texture_feature
            }
        }

        return segmented_image, info

    # ==================== 传统相场法实现 ====================

    def traditional_phase_field_segment(self, image: np.ndarray, gray_image: np.ndarray = None,
                                       binary_threshold: float = 0.15,
                                       show_progress: bool = True) -> Tuple[np.ndarray, Dict]:
        """
        传统相场分割方法
        基于标准Allen-Cahn方程，不包含任何高级功能

        Args:
            image: 输入图像
            gray_image: 原始灰度图像
            binary_threshold: 二值化阈值
            show_progress: 是否显示进度

        Returns:
            segmented_image: 分割结果
            info: 信息字典
        """
        start_time = time.time()

        # 图像预处理
        if np.max(image) > 1.0:
            img_normalized = image / 255.0
        else:
            img_normalized = image.copy()

        if show_progress:
            print(f"🔄 开始传统相场分割 (图像尺寸: {img_normalized.shape})")
            print(f"📋 使用标准Allen-Cahn方程，无高级功能")

        # 相场函数初始化
        init_image = gray_image if gray_image is not None else image
        if np.max(init_image) <= 1.0:
            init_image = (init_image * 255).astype(np.uint8)

        phi = self.otsu_initialization(init_image)
        initial_ice_ratio = np.mean(phi)

        if show_progress:
            print(f"✓ 相场初始化完成 (初始海冰比例: {initial_ice_ratio:.2%})")

        # 传统相场演化迭代 - 使用固定的传统参数
        energy_history = []

        # 保存原始参数
        original_epsilon = self.epsilon
        original_mu = self.mu
        original_lambda = self.lambda_
        original_dt = self.dt

        # 使用传统的固定参数（确保与APSIS明显不同）
        traditional_epsilon = 0.15  # 更大的界面宽度（与APSIS的0.05差异更明显）
        traditional_mu = 0.3        # 更小的扩散系数（与APSIS的1.0差异更明显）
        traditional_lambda = 0.5    # 更小的图像驱动力（与APSIS的1.5差异更明显）
        traditional_dt = 0.003      # 更小的时间步长（确保稳定性）

        for i in range(self.max_iterations):
            phi_old = phi.copy()

            # 使用传统参数的Allen-Cahn演化步骤
            phi = self.traditional_evolution_step_with_params(
                phi, img_normalized, traditional_epsilon, traditional_mu,
                traditional_lambda, traditional_dt
            )

            # 计算能量
            if i % 50 == 0:
                energy = self._compute_traditional_energy_with_params(
                    phi, img_normalized, traditional_epsilon, traditional_lambda
                )
                energy_history.append(energy)

            # 收敛性检查
            if self.check_convergence(phi_old, phi):
                if show_progress:
                    print(f"✓ 算法在第 {i + 1} 次迭代后收敛")
                convergence_info = {
                    'converged': True,
                    'iterations': i + 1
                }
                break

            # 进度显示
            if show_progress and i % 100 == 0:
                phi_change = np.mean(np.abs(phi - phi_old))
                current_ice_ratio = np.mean(phi > 0.5)
                print(f"⏳ 迭代 {i}: 变化量={phi_change:.6f}, 当前海冰比例={current_ice_ratio:.2%}")

        else:
            if show_progress:
                print(f"⚠️ 达到最大迭代次数 {self.max_iterations}")
            convergence_info = {
                'converged': False,
                'iterations': self.max_iterations
            }

        # 恢复原始参数
        self.epsilon = original_epsilon
        self.mu = original_mu
        self.lambda_ = original_lambda
        self.dt = original_dt

        # 二值化得到最终分割结果
        segmented_image = (phi > binary_threshold).astype(np.uint8) * 255

        # 结果统计
        ice_pixels = np.sum(segmented_image > 0)
        total_pixels = segmented_image.size
        ice_ratio = ice_pixels / total_pixels

        computation_time = time.time() - start_time

        if show_progress:
            print(f"🎉 传统相场分割完成！")
            print(f"📊 海冰覆盖率: {ice_ratio:.2%}")
            print(f"⏱️ 计算时间: {computation_time:.2f}秒")

        # 构建返回信息
        info = {
            'convergence_info': convergence_info,
            'ice_ratio': ice_ratio,
            'computation_time': computation_time,
            'final_phi': phi,
            'original_image': image,
            'energy_history': energy_history,
            'initial_ice_ratio': initial_ice_ratio,
            'method': 'Traditional Phase Field',
            'parameters': {
                'epsilon': traditional_epsilon,
                'mu': traditional_mu,
                'lambda': traditional_lambda,
                'dt': traditional_dt,
                'binary_threshold': binary_threshold
            }
        }

        return segmented_image, info

    def traditional_evolution_step(self, phi: np.ndarray, image: np.ndarray) -> np.ndarray:
        """
        传统Allen-Cahn演化步骤
        标准的相场演化，不包含任何高级功能

        Args:
            phi: 当前相场函数
            image: 归一化图像

        Returns:
            phi_new: 更新后的相场函数
        """
        # 标准拉普拉斯算子（固定参数，不自适应）
        laplacian = cv2.Laplacian(phi, cv2.CV_64F)

        # 双能谷势项
        double_well_term = phi * (1 - phi) * (1 - 2 * phi)

        # 简单的图像驱动项（固定参数）
        image_force = self.lambda_ * (image - 0.5)

        # 标准Allen-Cahn方程（使用固定参数）
        # ∂φ/∂t = μ[ε∇²φ - (1/ε)W'(φ)] + F(I)
        dphi = (self.mu * (self.epsilon * laplacian - (1/self.epsilon) * double_well_term) +
                image_force)

        # 时间积分（使用固定时间步长）
        phi_new = phi + self.dt * dphi

        # 约束到[0,1]区间
        phi_new = np.clip(phi_new, 0, 1)

        return phi_new

    def traditional_evolution_step_with_params(self, phi: np.ndarray, image: np.ndarray,
                                             epsilon: float, mu: float, lambda_: float, dt: float) -> np.ndarray:
        """
        使用指定参数的传统Allen-Cahn演化步骤

        Args:
            phi: 当前相场函数
            image: 归一化图像
            epsilon: 界面宽度参数
            mu: 扩散系数
            lambda_: 图像驱动力系数
            dt: 时间步长

        Returns:
            phi_new: 更新后的相场函数
        """
        # 标准拉普拉斯算子（固定参数，不自适应）
        laplacian = cv2.Laplacian(phi, cv2.CV_64F)

        # 双能谷势项（传统形式）
        double_well_term = phi * (1 - phi) * (1 - 2 * phi)

        # 简单的图像驱动项（传统固定参数，无自适应）
        image_force = lambda_ * (image - 0.5)

        # 传统方法：添加多重保守性约束
        image_force = image_force * 0.6  # 进一步减弱图像驱动力

        # 传统方法：添加额外的平滑约束
        phi_smooth = cv2.GaussianBlur(phi, (3, 3), 1.0)
        smoothing_term = 0.1 * (phi_smooth - phi)  # 平滑正则化项

        # 标准Allen-Cahn方程（使用传入的固定参数）+ 平滑正则化
        # ∂φ/∂t = μ[ε∇²φ - (1/ε)W'(φ)] + F(I) + 平滑项
        dphi = (mu * (epsilon * laplacian - (1/epsilon) * double_well_term) +
                image_force + smoothing_term)

        # 时间积分（使用传入的固定时间步长）
        phi_new = phi + dt * dphi

        # 约束到[0,1]区间
        phi_new = np.clip(phi_new, 0, 1)

        return phi_new

    def _compute_traditional_energy_with_params(self, phi: np.ndarray, image: np.ndarray,
                                              epsilon: float, lambda_: float) -> float:
        """
        使用指定参数计算传统相场能量

        Args:
            phi: 相场函数
            image: 图像
            epsilon: 界面宽度参数
            lambda_: 图像驱动力系数

        Returns:
            total_energy: 总能量
        """
        # 计算梯度能
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_energy = 0.5 * epsilon * np.sum(grad_phi_x**2 + grad_phi_y**2)

        # 计算双能谷势能
        double_well_energy = (1/epsilon) * np.sum(phi**2 * (1-phi)**2)

        # 计算图像驱动能
        image_energy = -lambda_ * np.sum(phi * (image - 0.5))

        total_energy = grad_energy + double_well_energy + image_energy

        return total_energy

    def _compute_traditional_energy(self, phi: np.ndarray, image: np.ndarray) -> float:
        """
        计算传统相场能量

        Args:
            phi: 相场函数
            image: 图像

        Returns:
            total_energy: 总能量
        """
        # 计算梯度能
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_energy = 0.5 * self.epsilon * np.sum(grad_phi_x**2 + grad_phi_y**2)

        # 计算双能谷势能
        double_well_energy = (1/self.epsilon) * np.sum(phi**2 * (1-phi)**2)

        # 计算图像驱动能
        image_energy = -self.lambda_ * np.sum(phi * (image - 0.5))

        total_energy = grad_energy + double_well_energy + image_energy

        return total_energy

    # ==================== 方法对比分析 ====================

    def compare_methods(self, image: np.ndarray, gray_image: np.ndarray = None,
                       binary_threshold: float = 0.15) -> Dict:
        """
        简化的相场方法对比：只对比传统相场法和APSIS完整版

        Args:
            image: 输入图像
            gray_image: 原始灰度图像
            binary_threshold: 二值化阈值

        Returns:
            comparison_results: 对比结果字典
        """
        print("🔬 相场方法对比分析...")
        print("=" * 50)

        results = {}

        # 保存原始设置
        original_settings = {
            'use_adaptive_weights': self.use_adaptive_weights,
            'use_edge_preservation': self.use_edge_preservation,
            'use_texture_feature': self.use_texture_feature,
            'use_anisotropic_diffusion': self.use_anisotropic_diffusion
        }

        # 1. 传统相场方法
        print("\n1️⃣ 传统相场方法...")
        print("   - 使用固定参数")
        print("   - 标准拉普拉斯算子")
        print("   - 无自适应功能")
        print(f"   - 传统参数: ε=0.08, μ=0.8, λ=1.0")
        traditional_segmented, traditional_info = self.traditional_phase_field_segment(
            image, gray_image, binary_threshold, show_progress=False
        )
        results['traditional'] = {
            'segmented': traditional_segmented,
            'info': traditional_info,
            'name': '传统相场法'
        }

        # 2. APSIS完整方法 - 确保所有高级功能都启用
        print("\n2️⃣ APSIS完整方法...")
        # 强制启用所有高级功能
        self.use_adaptive_weights = True
        self.use_edge_preservation = True
        self.use_texture_feature = True
        self.use_anisotropic_diffusion = True

        print("   - 启用自适应权重")
        print("   - 启用边缘保持")
        print("   - 启用纹理特征")
        print("   - 启用各向异性扩散")

        # 确保APSIS使用不同的参数
        print(f"   - APSIS参数: ε={self.epsilon:.3f}, μ={self.mu:.3f}, λ={self.lambda_:.3f}")

        apsis_segmented, apsis_info = self.segment(
            image, gray_image, binary_threshold, show_progress=False
        )
        results['apsis'] = {
            'segmented': apsis_segmented,
            'info': apsis_info,
            'name': 'APSIS算法'
        }

        # 恢复原始设置
        for key, value in original_settings.items():
            setattr(self, key, value)

        # 计算评价指标
        metrics = self.compute_simple_metrics(results)

        # 生成简单报告
        self.print_simple_report(metrics)

        # 简单可视化
        self.visualize_simple_comparison(results, metrics)

        return {
            'results': results,
            'metrics': metrics
        }

    def compute_simple_metrics(self, results: Dict) -> Dict:
        """
        计算简单的对比指标

        Args:
            results: 各方法的结果

        Returns:
            metrics: 对比指标
        """
        metrics = {}

        for method_name, result in results.items():
            info = result['info']
            segmented = result['segmented']

            # 基本指标
            ice_ratio = info['ice_ratio']
            computation_time = info['computation_time']
            converged = info['convergence_info']['converged']
            iterations = info['convergence_info']['iterations']

            # 简单的分割质量指标
            edge_pixels = np.sum(cv2.Canny((segmented > 0).astype(np.uint8) * 255, 50, 150) > 0)
            total_pixels = segmented.size
            edge_ratio = edge_pixels / total_pixels

            metrics[method_name] = {
                'ice_ratio': ice_ratio,
                'computation_time': computation_time,
                'converged': converged,
                'iterations': iterations,
                'edge_ratio': edge_ratio
            }

        return metrics

    def print_simple_report(self, metrics: Dict):
        """
        打印简单对比报告

        Args:
            metrics: 对比指标字典
        """
        print("\n" + "="*60)
        print("📊 相场方法对比结果")
        print("="*60)

        traditional = metrics['traditional']
        apsis = metrics['apsis']

        print(f"{'指标':<15} {'传统相场法':<15} {'APSIS算法':<15} {'改进幅度':<15}")
        print("-" * 60)

        # 海冰覆盖率
        ice_diff = (apsis['ice_ratio'] - traditional['ice_ratio']) * 100
        print(f"{'海冰覆盖率':<15} {traditional['ice_ratio']:<15.2%} {apsis['ice_ratio']:<15.2%} {ice_diff:+.2f}%")

        # 计算时间
        time_improvement = (traditional['computation_time'] - apsis['computation_time']) / traditional['computation_time'] * 100
        print(f"{'计算时间(s)':<15} {traditional['computation_time']:<15.2f} {apsis['computation_time']:<15.2f} {time_improvement:+.1f}%")

        # 迭代次数
        iter_improvement = (traditional['iterations'] - apsis['iterations']) / traditional['iterations'] * 100
        print(f"{'迭代次数':<15} {traditional['iterations']:<15} {apsis['iterations']:<15} {iter_improvement:+.1f}%")

        # 收敛状态
        trad_conv = "是" if traditional['converged'] else "否"
        apsis_conv = "是" if apsis['converged'] else "否"
        print(f"{'收敛状态':<15} {trad_conv:<15} {apsis_conv:<15} {'--':<15}")

        print("="*60)

        # 总结
        print(f"\n🚀 APSIS算法改进总结:")
        if time_improvement > 0:
            print(f"   ⏱️ 计算速度提升 {time_improvement:.1f}%")
        if iter_improvement > 0:
            print(f"   🔄 收敛效率提升 {iter_improvement:.1f}%")
        print(f"   📊 海冰检测差异 {ice_diff:+.2f}个百分点")

        # 添加参数对比
        print(f"\n⚙️ 参数设置对比:")
        print(f"   传统方法: ε=0.08, μ=0.8, λ=1.0 (固定)")
        print(f"   APSIS方法: ε={self.epsilon:.3f}, μ={self.mu:.3f}, λ={self.lambda_:.3f} (自适应)")

    def visualize_simple_comparison(self, results: Dict, metrics: Dict):
        """
        简单的可视化对比

        Args:
            results: 结果字典
            metrics: 指标字典
        """
        print("\n📊 生成对比可视化...")

        # 获取结果
        traditional = results['traditional']
        apsis = results['apsis']
        traditional_metrics = metrics['traditional']
        apsis_metrics = metrics['apsis']

        # 创建简单的对比图
        plt.figure(figsize=(15, 10))

        # 第一行：分割结果对比
        plt.subplot(2, 3, 1)
        plt.imshow(traditional['info']['original_image'], cmap='gray')
        plt.title('原始图像\nOriginal Image', fontsize=12, fontweight='bold')
        plt.axis('off')

        plt.subplot(2, 3, 2)
        plt.imshow(traditional['segmented'], cmap='gray')
        plt.title(f'传统相场法\nTraditional Phase Field\n时间: {traditional_metrics["computation_time"]:.1f}s\n迭代: {traditional_metrics["iterations"]}',
                 fontsize=11, fontweight='bold')
        plt.axis('off')

        plt.subplot(2, 3, 3)
        plt.imshow(apsis['segmented'], cmap='gray')
        plt.title(f'APSIS算法\nAPSIS Algorithm\n时间: {apsis_metrics["computation_time"]:.1f}s\n迭代: {apsis_metrics["iterations"]}',
                 fontsize=11, fontweight='bold')
        plt.axis('off')

        # 第二行：性能对比图表
        methods = ['传统相场法', 'APSIS算法']

        # 计算时间对比
        plt.subplot(2, 3, 4)
        times = [traditional_metrics['computation_time'], apsis_metrics['computation_time']]
        colors = ['blue', 'red']
        bars = plt.bar(methods, times, color=colors, alpha=0.7)
        plt.ylabel('计算时间 (秒)')
        plt.title('计算时间对比', fontsize=12, fontweight='bold')
        for bar, time in zip(bars, times):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{time:.1f}s', ha='center', va='bottom', fontsize=10)

        # 迭代次数对比
        plt.subplot(2, 3, 5)
        iterations = [traditional_metrics['iterations'], apsis_metrics['iterations']]
        bars = plt.bar(methods, iterations, color=colors, alpha=0.7)
        plt.ylabel('迭代次数')
        plt.title('迭代次数对比', fontsize=12, fontweight='bold')
        for bar, iter_count in zip(bars, iterations):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5,
                    f'{iter_count}', ha='center', va='bottom', fontsize=10)

        # 海冰覆盖率对比
        plt.subplot(2, 3, 6)
        ice_ratios = [traditional_metrics['ice_ratio'], apsis_metrics['ice_ratio']]
        bars = plt.bar(methods, ice_ratios, color=colors, alpha=0.7)
        plt.ylabel('海冰覆盖率')
        plt.title('海冰覆盖率对比', fontsize=12, fontweight='bold')
        for bar, ratio in zip(bars, ice_ratios):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                    f'{ratio:.1%}', ha='center', va='bottom', fontsize=10)

        plt.suptitle('传统相场法 vs APSIS算法对比\nTraditional Phase Field vs APSIS Comparison',
                     fontsize=14, fontweight='bold')
        plt.tight_layout()
        plt.show()

    # 删除复杂的可视化函数，保持代码简洁

    # ==================== 完整性能分析和报告 ====================

    def generate_comprehensive_report(self, image: np.ndarray, segmented_image: np.ndarray,
                                    info: Dict) -> Dict:
        """
        生成综合分析报告

        Args:
            image: 原始图像
            segmented_image: 分割结果
            info: 算法信息字典

        Returns:
            comprehensive_report: 综合分析报告
        """
        # 基础性能分析
        performance = self.analyze_performance(info)

        # Ginzburg-Landau物理分析
        physics_analysis = self.analyze_ginzburg_landau_physics(info)

        # 界面动力学分析
        dynamics_analysis = self.analyze_interface_dynamics(info)

        # 图像质量评估
        quality_metrics = self.evaluate_segmentation_quality(image, segmented_image, info)

        # 算法稳定性分析
        stability_analysis = self.analyze_algorithm_stability(info)

        comprehensive_report = {
            'algorithm_info': {
                'version': '5.0 (完整功能版)',
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'image_size': image.shape,
                'parameters': info['parameters']
            },
            'performance': performance,
            'physics_analysis': physics_analysis,
            'dynamics_analysis': dynamics_analysis,
            'quality_metrics': quality_metrics,
            'stability_analysis': stability_analysis
        }

        return comprehensive_report

    def evaluate_segmentation_quality(self, image: np.ndarray, segmented_image: np.ndarray,
                                    info: Dict) -> Dict:
        """
        评估分割质量

        Args:
            image: 原始图像
            segmented_image: 分割结果
            info: 算法信息字典

        Returns:
            quality_metrics: 质量评估指标
        """
        # 基本统计
        ice_pixels = np.sum(segmented_image > 0)
        total_pixels = segmented_image.size
        ice_ratio = ice_pixels / total_pixels

        # 连通性分析
        from scipy.ndimage import label
        labeled_ice, num_components = label(segmented_image > 0)

        # 计算各连通分量的大小
        component_sizes = []
        for i in range(1, num_components + 1):
            size = np.sum(labeled_ice == i)
            component_sizes.append(size)

        # 边缘一致性
        edge_map = info.get('edge_map')
        if edge_map is not None:
            phi = info['final_phi']
            grad_phi_x = np.gradient(phi, axis=1)
            grad_phi_y = np.gradient(phi, axis=0)
            grad_phi_magnitude = np.sqrt(grad_phi_x**2 + grad_phi_y**2)

            # 边缘对齐度
            edge_alignment = np.corrcoef(edge_map.flatten(), grad_phi_magnitude.flatten())[0, 1]
        else:
            edge_alignment = 0.0

        # 分割平滑度
        smoothness = self.compute_segmentation_smoothness(segmented_image)

        # 对比度评估
        contrast_score = self.compute_contrast_score(image, segmented_image)

        quality_metrics = {
            'ice_coverage_ratio': ice_ratio,
            'connected_components': num_components,
            'largest_component_ratio': max(component_sizes) / total_pixels if component_sizes else 0,
            'average_component_size': np.mean(component_sizes) if component_sizes else 0,
            'component_size_variance': np.var(component_sizes) if component_sizes else 0,
            'edge_alignment_score': edge_alignment,
            'segmentation_smoothness': smoothness,
            'contrast_score': contrast_score,
            'fragmentation_index': num_components / (ice_ratio * total_pixels + 1e-8)
        }

        return quality_metrics

    def compute_segmentation_smoothness(self, segmented_image: np.ndarray) -> float:
        """
        计算分割结果的平滑度

        Args:
            segmented_image: 分割结果

        Returns:
            smoothness: 平滑度评分
        """
        # 计算边界长度
        grad_x = np.abs(np.diff(segmented_image, axis=1))
        grad_y = np.abs(np.diff(segmented_image, axis=0))

        boundary_length = np.sum(grad_x > 0) + np.sum(grad_y > 0)

        # 归一化平滑度（边界长度越短越平滑）
        total_possible_boundaries = 2 * segmented_image.size
        smoothness = 1.0 - (boundary_length / total_possible_boundaries)

        return smoothness

    def compute_contrast_score(self, image: np.ndarray, segmented_image: np.ndarray) -> float:
        """
        计算对比度评分

        Args:
            image: 原始图像
            segmented_image: 分割结果

        Returns:
            contrast_score: 对比度评分
        """
        ice_mask = segmented_image > 0
        water_mask = segmented_image == 0

        if np.any(ice_mask) and np.any(water_mask):
            ice_intensity = np.mean(image[ice_mask])
            water_intensity = np.mean(image[water_mask])

            # 对比度评分
            contrast_score = abs(ice_intensity - water_intensity) / 255.0
        else:
            contrast_score = 0.0

        return contrast_score

    def analyze_algorithm_stability(self, info: Dict) -> Dict:
        """
        分析算法稳定性

        Args:
            info: 算法信息字典

        Returns:
            stability_analysis: 稳定性分析结果
        """
        # 收敛性分析
        convergence_info = info['convergence_info']

        # 能量历史分析
        energy_history = info.get('energy_history', [])

        if len(energy_history) > 1:
            # 能量单调性
            energy_decreasing = all(energy_history[i] >= energy_history[i+1]
                                  for i in range(len(energy_history)-1))

            # 能量变化率
            energy_changes = [abs(energy_history[i+1] - energy_history[i])
                            for i in range(len(energy_history)-1)]
            energy_stability = np.mean(energy_changes) if energy_changes else 0

            # 收敛速度
            if len(energy_history) > 2:
                convergence_rate = (energy_history[0] - energy_history[-1]) / len(energy_history)
            else:
                convergence_rate = 0
        else:
            energy_decreasing = True
            energy_stability = 0
            convergence_rate = 0

        # 参数敏感性评估
        parameter_sensitivity = self.assess_parameter_sensitivity(info)

        stability_analysis = {
            'converged': convergence_info['converged'],
            'iterations_used': convergence_info['iterations'],
            'energy_monotonic_decrease': energy_decreasing,
            'energy_stability_score': energy_stability,
            'convergence_rate': convergence_rate,
            'parameter_sensitivity': parameter_sensitivity,
            'numerical_stability': self.check_numerical_stability(info)
        }

        return stability_analysis

    def assess_parameter_sensitivity(self, info: Dict) -> Dict:
        """
        评估参数敏感性

        Args:
            info: 算法信息字典

        Returns:
            sensitivity: 参数敏感性评估
        """
        params = info['parameters']

        # 基于参数值评估敏感性
        epsilon_sensitivity = 'High' if params['epsilon'] < 0.03 or params['epsilon'] > 0.08 else 'Medium'
        mu_sensitivity = 'High' if params['mu'] < 0.7 or params['mu'] > 1.5 else 'Low'
        lambda_sensitivity = 'Medium' if params['lambda'] > 2.0 else 'Low'
        dt_sensitivity = 'High' if params['dt'] > 0.015 else 'Medium'

        sensitivity = {
            'epsilon': epsilon_sensitivity,
            'mu': mu_sensitivity,
            'lambda': lambda_sensitivity,
            'dt': dt_sensitivity,
            'overall': 'High' if any(s == 'High' for s in [epsilon_sensitivity, mu_sensitivity, lambda_sensitivity, dt_sensitivity]) else 'Medium'
        }

        return sensitivity

    def check_numerical_stability(self, info: Dict) -> Dict:
        """
        检查数值稳定性

        Args:
            info: 算法信息字典

        Returns:
            numerical_stability: 数值稳定性检查结果
        """
        phi = info['final_phi']

        # 检查相场函数的数值范围
        phi_in_range = np.all((phi >= -0.1) & (phi <= 1.1))

        # 检查是否有NaN或Inf
        phi_finite = np.all(np.isfinite(phi))

        # 检查梯度的数值稳定性
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_finite = np.all(np.isfinite(grad_phi_x)) and np.all(np.isfinite(grad_phi_y))

        # 检查时间步长稳定性条件
        params = info['parameters']
        dt_stable = params['dt'] <= 0.25 * params['epsilon']**2 / params['mu']

        numerical_stability = {
            'phi_in_valid_range': phi_in_range,
            'phi_finite': phi_finite,
            'gradient_finite': grad_finite,
            'dt_stability_condition': dt_stable,
            'overall_stable': phi_in_range and phi_finite and grad_finite and dt_stable
        }

        return numerical_stability

    def print_comprehensive_report(self, comprehensive_report: Dict):
        """
        打印综合分析报告

        Args:
            comprehensive_report: 综合分析报告
        """
        print("\n" + "="*100)
        print("🔬 APSIS综合分析报告")
        print("APSIS Comprehensive Analysis Report")
        print("="*100)

        # 算法信息
        algo_info = comprehensive_report['algorithm_info']
        print(f"\n📋 算法信息:")
        print(f"   版本: {algo_info['version']}")
        print(f"   时间戳: {algo_info['timestamp']}")
        print(f"   图像尺寸: {algo_info['image_size']}")

        # 性能指标
        perf = comprehensive_report['performance']
        print(f"\n⚡ 性能指标:")
        print(f"   收敛状态: {'✅ 已收敛' if perf['convergence']['converged'] else '❌ 未收敛'}")
        print(f"   迭代次数: {perf['convergence']['iterations']}")
        print(f"   计算时间: {perf['computation_time']:.2f}秒")
        print(f"   海冰覆盖率: {perf['ice_ratio']:.2%}")

        # 物理分析
        physics = comprehensive_report['physics_analysis']
        print(f"\n🔬 物理分析:")
        print(f"   Ginzburg-Landau能量: {physics['ginzburg_landau_energy']:.6f}")
        print(f"   界面长度: {physics['interface_length']:.2f}")
        print(f"   相分离质量: {physics['phase_separation_quality']:.3f}")

        # 质量评估
        quality = comprehensive_report['quality_metrics']
        print(f"\n📊 质量评估:")
        print(f"   连通分量数: {quality['connected_components']}")
        print(f"   边缘对齐度: {quality['edge_alignment_score']:.3f}")
        print(f"   分割平滑度: {quality['segmentation_smoothness']:.3f}")
        print(f"   对比度评分: {quality['contrast_score']:.3f}")

        # 稳定性分析
        stability = comprehensive_report['stability_analysis']
        print(f"\n🔧 稳定性分析:")
        print(f"   数值稳定性: {'✅ 稳定' if stability['numerical_stability']['overall_stable'] else '⚠️ 不稳定'}")
        print(f"   能量单调递减: {'✅ 是' if stability['energy_monotonic_decrease'] else '❌ 否'}")
        print(f"   参数敏感性: {stability['parameter_sensitivity']['overall']}")

        print("="*100)

# 兼容类
class APSISSegmentation(APSISOptimized):
    """向后兼容的类名"""
    pass
