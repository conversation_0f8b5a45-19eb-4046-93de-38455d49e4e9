%!PS-Adobe-3.0 EPSF-3.0
%%Title: SARSA_Steps_per_Episode.eps
%%Creator: Mat<PERSON>lotlib v3.7.5, https://matplotlib.org/
%%CreationDate: Mon Feb 26 10:57:43 2024
%%Orientation: portrait
%%BoundingBox: 75 223 537 569
%%HiResBoundingBox: 75.600000 223.200000 536.400000 568.800000
%%EndComments
%%BeginProlog
/mpldict 11 dict def
mpldict begin
/_d { bind def } bind def
/m { moveto } _d
/l { lineto } _d
/r { rlineto } _d
/c { curveto } _d
/cl { closepath } _d
/ce { closepath eofill } _d
/box {
      m
      1 index 0 r
      0 exch r
      neg 0 r
      cl
    } _d
/clipbox {
      box
      clip
      newpath
    } _d
/sc { setcachedevice } _d
%!PS-Adobe-3.0 Resource-Font
%%Creator: Converted from TrueType to Type 3 by Matplotlib.
10 dict begin
/FontName /DejaVuSans def
/PaintType 0 def
/FontMatrix [0.00048828125 0 0 0.00048828125 0 0] def
/FontBBox [-2090 -948 3673 2524] def
/FontType 3 def
/Encoding [/space /zero /one /two /three /four /five /six /seven /E /T /a /d /e /i /k /n /o /p /s /t] def
/CharStrings 22 dict dup begin
/.notdef 0 def
/space{651 0 0 0 0 0 sc
ce} _d
/zero{1303 0 135 -29 1167 1520 sc
651 1360 m
547 1360 469 1309 416 1206 c
364 1104 338 950 338 745 c
338 540 364 387 416 284 c
469 182 547 131 651 131 c
756 131 834 182 886 284 c
939 387 965 540 965 745 c
965 950 939 1104 886 1206 c
834 1309 756 1360 651 1360 c

651 1520 m
818 1520 946 1454 1034 1321 c
1123 1189 1167 997 1167 745 c
1167 494 1123 302 1034 169 c
946 37 818 -29 651 -29 c
484 -29 356 37 267 169 c
179 302 135 494 135 745 c
135 997 179 1189 267 1321 c
356 1454 484 1520 651 1520 c

ce} _d
/one{1303 0 225 0 1114 1493 sc
254 170 m
584 170 l
584 1309 l
225 1237 l
225 1421 l
582 1493 l
784 1493 l
784 170 l
1114 170 l
1114 0 l
254 0 l
254 170 l

ce} _d
/two{1303 0 150 0 1098 1520 sc
393 170 m
1098 170 l
1098 0 l
150 0 l
150 170 l
227 249 331 356 463 489 c
596 623 679 709 713 748 c
778 821 823 882 848 932 c
874 983 887 1032 887 1081 c
887 1160 859 1225 803 1275 c
748 1325 675 1350 586 1350 c
523 1350 456 1339 385 1317 c
315 1295 240 1262 160 1217 c
160 1421 l
241 1454 317 1478 388 1495 c
459 1512 523 1520 582 1520 c
737 1520 860 1481 952 1404 c
1044 1327 1090 1223 1090 1094 c
1090 1033 1078 974 1055 919 c
1032 864 991 800 930 725 c
913 706 860 650 771 557 c
682 465 556 336 393 170 c

ce} _d
/three{1303 0 156 -29 1139 1520 sc
831 805 m
928 784 1003 741 1057 676 c
1112 611 1139 530 1139 434 c
1139 287 1088 173 987 92 c
886 11 742 -29 555 -29 c
492 -29 428 -23 361 -10 c
295 2 227 20 156 45 c
156 240 l
212 207 273 183 340 166 c
407 149 476 141 549 141 c
676 141 772 166 838 216 c
905 266 938 339 938 434 c
938 522 907 591 845 640 c
784 690 698 715 588 715 c
414 715 l
414 881 l
596 881 l
695 881 771 901 824 940 c
877 980 903 1037 903 1112 c
903 1189 876 1247 821 1288 c
767 1329 689 1350 588 1350 c
533 1350 473 1344 410 1332 c
347 1320 277 1301 201 1276 c
201 1456 l
278 1477 349 1493 416 1504 c
483 1515 547 1520 606 1520 c
759 1520 881 1485 970 1415 c
1059 1346 1104 1252 1104 1133 c
1104 1050 1080 980 1033 923 c
986 866 918 827 831 805 c

ce} _d
/four{1303 0 100 0 1188 1493 sc
774 1317 m
264 520 l
774 520 l
774 1317 l

721 1493 m
975 1493 l
975 520 l
1188 520 l
1188 352 l
975 352 l
975 0 l
774 0 l
774 352 l
100 352 l
100 547 l
721 1493 l

ce} _d
/five{1303 0 158 -29 1124 1493 sc
221 1493 m
1014 1493 l
1014 1323 l
406 1323 l
406 957 l
435 967 465 974 494 979 c
523 984 553 987 582 987 c
749 987 881 941 978 850 c
1075 759 1124 635 1124 479 c
1124 318 1074 193 974 104 c
874 15 733 -29 551 -29 c
488 -29 424 -24 359 -13 c
294 -2 227 14 158 35 c
158 238 l
218 205 280 181 344 165 c
408 149 476 141 547 141 c
662 141 754 171 821 232 c
888 293 922 375 922 479 c
922 583 888 665 821 726 c
754 787 662 817 547 817 c
493 817 439 811 385 799 c
332 787 277 768 221 743 c
221 1493 l

ce} _d
/six{1303 0 143 -29 1174 1520 sc
676 827 m
585 827 513 796 460 734 c
407 672 381 587 381 479 c
381 372 407 287 460 224 c
513 162 585 131 676 131 c
767 131 838 162 891 224 c
944 287 971 372 971 479 c
971 587 944 672 891 734 c
838 796 767 827 676 827 c

1077 1460 m
1077 1276 l
1026 1300 975 1318 923 1331 c
872 1344 821 1350 770 1350 c
637 1350 535 1305 464 1215 c
394 1125 354 989 344 807 c
383 865 433 909 492 940 c
551 971 617 987 688 987 c
838 987 956 941 1043 850 c
1130 759 1174 636 1174 479 c
1174 326 1129 203 1038 110 c
947 17 827 -29 676 -29 c
503 -29 371 37 280 169 c
189 302 143 494 143 745 c
143 981 199 1169 311 1309 c
423 1450 573 1520 762 1520 c
813 1520 864 1515 915 1505 c
967 1495 1021 1480 1077 1460 c

ce} _d
/seven{1303 0 168 0 1128 1493 sc
168 1493 m
1128 1493 l
1128 1407 l
586 0 l
375 0 l
885 1323 l
168 1323 l
168 1493 l

ce} _d
/E{1294 0 201 0 1163 1493 sc
201 1493 m
1145 1493 l
1145 1323 l
403 1323 l
403 881 l
1114 881 l
1114 711 l
403 711 l
403 170 l
1163 170 l
1163 0 l
201 0 l
201 1493 l

ce} _d
/T{1251 0 -6 0 1257 1493 sc
-6 1493 m
1257 1493 l
1257 1323 l
727 1323 l
727 0 l
524 0 l
524 1323 l
-6 1323 l
-6 1493 l

ce} _d
/a{1255 0 123 -29 1069 1147 sc
702 563 m
553 563 450 546 393 512 c
336 478 307 420 307 338 c
307 273 328 221 371 182 c
414 144 473 125 547 125 c
649 125 731 161 792 233 c
854 306 885 402 885 522 c
885 563 l
702 563 l

1069 639 m
1069 0 l
885 0 l
885 170 l
843 102 791 52 728 19 c
665 -13 589 -29 498 -29 c
383 -29 292 3 224 67 c
157 132 123 218 123 326 c
123 452 165 547 249 611 c
334 675 460 707 627 707 c
885 707 l
885 725 l
885 810 857 875 801 921 c
746 968 668 991 567 991 c
503 991 441 983 380 968 c
319 953 261 930 205 899 c
205 1069 l
272 1095 338 1114 401 1127 c
464 1140 526 1147 586 1147 c
748 1147 869 1105 949 1021 c
1029 937 1069 810 1069 639 c

ce} _d
/d{1300 0 113 -29 1114 1556 sc
930 950 m
930 1556 l
1114 1556 l
1114 0 l
930 0 l
930 168 l
891 101 842 52 783 19 c
724 -13 654 -29 571 -29 c
436 -29 325 25 240 133 c
155 241 113 383 113 559 c
113 735 155 877 240 985 c
325 1093 436 1147 571 1147 c
654 1147 724 1131 783 1098 c
842 1066 891 1017 930 950 c

303 559 m
303 424 331 317 386 240 c
442 163 519 125 616 125 c
713 125 790 163 846 240 c
902 317 930 424 930 559 c
930 694 902 800 846 877 c
790 954 713 993 616 993 c
519 993 442 954 386 877 c
331 800 303 694 303 559 c

ce} _d
/e{1260 0 113 -29 1151 1147 sc
1151 606 m
1151 516 l
305 516 l
313 389 351 293 419 226 c
488 160 583 127 705 127 c
776 127 844 136 910 153 c
977 170 1043 196 1108 231 c
1108 57 l
1042 29 974 8 905 -7 c
836 -22 765 -29 694 -29 c
515 -29 374 23 269 127 c
165 231 113 372 113 549 c
113 732 162 878 261 985 c
360 1093 494 1147 662 1147 c
813 1147 932 1098 1019 1001 c
1107 904 1151 773 1151 606 c

967 660 m
966 761 937 841 882 901 c
827 961 755 991 664 991 c
561 991 479 962 417 904 c
356 846 320 764 311 659 c
967 660 l

ce} _d
/i{569 0 193 0 377 1556 sc
193 1120 m
377 1120 l
377 0 l
193 0 l
193 1120 l

193 1556 m
377 1556 l
377 1323 l
193 1323 l
193 1556 l

ce} _d
/k{1186 0 186 0 1180 1556 sc
186 1556 m
371 1556 l
371 637 l
920 1120 l
1155 1120 l
561 596 l
1180 0 l
940 0 l
371 547 l
371 0 l
186 0 l
186 1556 l

ce} _d
/n{1298 0 186 0 1124 1147 sc
1124 676 m
1124 0 l
940 0 l
940 670 l
940 776 919 855 878 908 c
837 961 775 987 692 987 c
593 987 514 955 457 892 c
400 829 371 742 371 633 c
371 0 l
186 0 l
186 1120 l
371 1120 l
371 946 l
415 1013 467 1064 526 1097 c
586 1130 655 1147 733 1147 c
862 1147 959 1107 1025 1027 c
1091 948 1124 831 1124 676 c

ce} _d
/o{1253 0 113 -29 1141 1147 sc
627 991 m
528 991 450 952 393 875 c
336 798 307 693 307 559 c
307 425 335 319 392 242 c
449 165 528 127 627 127 c
725 127 803 166 860 243 c
917 320 946 426 946 559 c
946 692 917 797 860 874 c
803 952 725 991 627 991 c

627 1147 m
787 1147 913 1095 1004 991 c
1095 887 1141 743 1141 559 c
1141 376 1095 232 1004 127 c
913 23 787 -29 627 -29 c
466 -29 340 23 249 127 c
158 232 113 376 113 559 c
113 743 158 887 249 991 c
340 1095 466 1147 627 1147 c

ce} _d
/p{1300 0 186 -426 1188 1147 sc
371 168 m
371 -426 l
186 -426 l
186 1120 l
371 1120 l
371 950 l
410 1017 458 1066 517 1098 c
576 1131 647 1147 729 1147 c
865 1147 975 1093 1060 985 c
1145 877 1188 735 1188 559 c
1188 383 1145 241 1060 133 c
975 25 865 -29 729 -29 c
647 -29 576 -13 517 19 c
458 52 410 101 371 168 c

997 559 m
997 694 969 800 913 877 c
858 954 781 993 684 993 c
587 993 510 954 454 877 c
399 800 371 694 371 559 c
371 424 399 317 454 240 c
510 163 587 125 684 125 c
781 125 858 163 913 240 c
969 317 997 424 997 559 c

ce} _d
/s{1067 0 111 -29 967 1147 sc
907 1087 m
907 913 l
855 940 801 960 745 973 c
689 986 631 993 571 993 c
480 993 411 979 365 951 c
320 923 297 881 297 825 c
297 782 313 749 346 724 c
379 700 444 677 543 655 c
606 641 l
737 613 829 573 884 522 c
939 471 967 400 967 309 c
967 205 926 123 843 62 c
761 1 648 -29 504 -29 c
444 -29 381 -23 316 -11 c
251 0 183 18 111 41 c
111 231 l
179 196 246 169 312 151 c
378 134 443 125 508 125 c
595 125 661 140 708 169 c
755 199 778 241 778 295 c
778 345 761 383 727 410 c
694 437 620 462 506 487 c
442 502 l
328 526 246 563 195 612 c
144 662 119 730 119 817 c
119 922 156 1004 231 1061 c
306 1118 412 1147 549 1147 c
617 1147 681 1142 741 1132 c
801 1122 856 1107 907 1087 c

ce} _d
/t{803 0 55 0 754 1438 sc
375 1438 m
375 1120 l
754 1120 l
754 977 l
375 977 l
375 369 l
375 278 387 219 412 193 c
437 167 488 154 565 154 c
754 154 l
754 0 l
565 0 l
423 0 325 26 271 79 c
217 132 190 229 190 369 c
190 977 l
55 977 l
55 1120 l
190 1120 l
190 1438 l
375 1438 l

ce} _d
end readonly def

/BuildGlyph {
 exch begin
 CharStrings exch
 2 copy known not {pop /.notdef} if
 true 3 1 roll get exec
 end
} _d

/BuildChar {
 1 index /Encoding get exch get
 1 index /BuildGlyph get exec
} _d

FontName currentdict end definefont pop
end
%%EndProlog
mpldict begin
75.6 223.2 translate
460.8 345.6 0 0 clipbox
gsave
0 0 m
460.8 0 l
460.8 345.6 l
0 345.6 l
cl
1.000 setgray
fill
grestore
gsave
58.26875 46.971875 m
450 46.971875 l
450 334.8 l
58.26875 334.8 l
cl
1.000 setgray
fill
grestore
0.800 setlinewidth
1 setlinejoin
0 setlinecap
[] 0 setdash
0.000 setgray
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
76.0747 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

72.2544 30.8469 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
147.441 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

135.98 30.8469 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
218.808 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

207.347 30.8469 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
290.175 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

278.714 30.8469 translate
0 rotate
0 0 m /three glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
361.541 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

350.08 30.8469 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

0 0 m
0 -3.5 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
432.908 46.9719 o
grestore
/DejaVuSans 12.000 selectfont
gsave

421.447 30.8469 translate
0 rotate
0 0 m /five glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

226.642 13.7063 translate
0 rotate
0 0 m /E glyphshow
8.8457 0 m /p glyphshow
17.7324 0 m /i glyphshow
21.6221 0 m /s glyphshow
28.916 0 m /o glyphshow
37.4814 0 m /d glyphshow
46.3682 0 m /e glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
58.2688 54.2322 o
grestore
/DejaVuSans 12.000 selectfont
gsave

43.6281 49.6697 translate
0 rotate
0 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
58.2688 90.6247 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 86.0622 translate
0 rotate
0 0 m /one glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
58.2688 127.017 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 122.455 translate
0 rotate
0 0 m /two glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
58.2688 163.41 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 158.847 translate
0 rotate
0 0 m /three glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
58.2688 199.802 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 195.24 translate
0 rotate
0 0 m /four glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
58.2688 236.195 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 231.632 translate
0 rotate
0 0 m /five glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
58.2688 272.587 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 268.025 translate
0 rotate
0 0 m /six glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
gsave
/o {
gsave
newpath
translate
0.8 setlinewidth
1 setlinejoin

0 setlinecap

-0 0 m
-3.5 0 l

gsave
0.000 setgray
fill
grestore
stroke
grestore
} bind def
58.2688 308.98 o
grestore
/DejaVuSans 12.000 selectfont
gsave

28.3469 304.417 translate
0 rotate
0 0 m /seven glyphshow
7.63477 0 m /zero glyphshow
15.2695 0 m /zero glyphshow
grestore
/DejaVuSans 14.000 selectfont
gsave

21.4406 149.948 translate
90 rotate
0 0 m /T glyphshow
6.17676 0 m /a glyphshow
14.7559 0 m /k glyphshow
22.3633 0 m /e glyphshow
30.9766 0 m /n glyphshow
39.8496 0 m /space glyphshow
44.2998 0 m /s glyphshow
51.5938 0 m /t glyphshow
57.083 0 m /e glyphshow
65.6963 0 m /p glyphshow
74.583 0 m /s glyphshow
grestore
1.500 setlinewidth
2 setlinecap
0.000 0.000 1.000 setrgbcolor
gsave
391.731 287.828 58.269 46.972 clipbox
76.074716 271.495281 m
76.788382 167.776713 l
77.502048 109.548745 l
78.215714 148.124773 l
78.92938 102.998098 l
79.643046 156.858969 l
80.356712 150.308322 l
81.070378 289.327596 l
81.784044 148.852623 l
82.49771 141.574127 l
83.211376 314.074483 l
83.925042 321.716903 l
84.638708 134.295631 l
85.352373 300.97319 l
86.066039 268.947807 l
86.779705 180.878005 l
87.493371 280.957326 l
88.207037 158.314668 l
88.920703 104.089872 l
89.634369 107.729121 l
90.348035 255.48259 l
91.061701 77.887287 l
91.775367 217.270486 l
92.489033 123.741812 l
93.202699 242.745222 l
93.916365 84.801858 l
94.630031 257.302214 l
95.343697 291.87507 l
96.057363 111.368369 l
96.771029 161.953916 l
97.484695 169.232412 l
98.198361 276.590228 l
98.912027 171.052036 l
99.625693 139.754503 l
100.339359 194.343223 l
101.053025 157.950743 l
101.766691 128.836759 l
102.480357 149.944397 l
103.194023 133.567781 l
103.907689 109.912669 l
104.621355 243.109146 l
105.335021 119.010789 l
106.048687 151.400097 l
106.762353 91.352504 l
107.476019 161.953916 l
108.189684 187.064727 l
108.90335 186.336877 l
109.617016 242.017372 l
110.330682 122.650037 l
111.044348 175.055209 l
111.758014 119.738639 l
112.47168 76.431588 l
113.899012 195.071073 l
114.612678 82.982234 l
115.326344 170.688111 l
116.04001 164.137465 l
116.753676 169.596337 l
117.467342 121.558263 l
118.181008 85.529708 l
118.894674 219.817959 l
119.60834 113.551917 l
120.322006 191.431825 l
121.035672 129.200684 l
121.749338 135.387405 l
122.463004 194.707148 l
123.17667 169.232412 l
123.890336 89.168956 l
124.604002 214.359087 l
125.317668 90.98858 l
126.031334 123.013962 l
126.745 123.741812 l
127.458666 89.168956 l
128.172332 105.545572 l
128.885998 135.387405 l
129.599664 143.029826 l
130.313329 100.450624 l
131.026995 102.634173 l
131.740661 114.279767 l
132.454327 98.267076 l
133.167993 121.194338 l
133.881659 78.979061 l
134.595325 117.55509 l
135.308991 80.43476 l
136.022657 96.811376 l
136.736323 155.039345 l
137.449989 96.811376 l
138.163655 122.650037 l
138.877321 81.526535 l
139.590987 93.899978 l
140.304653 152.127946 l
141.018319 135.75133 l
141.731985 200.16602 l
142.445651 73.156264 l
143.159317 76.431588 l
143.872983 187.792577 l
144.586649 114.279767 l
145.300315 116.827241 l
146.013981 114.279767 l
146.727647 68.789167 l
147.441313 74.975888 l
148.154979 144.485525 l
148.868645 88.077181 l
149.582311 93.899978 l
150.295977 76.431588 l
151.009643 97.175301 l
151.723309 89.168956 l
152.436975 125.925361 l
153.15064 99.35885 l
153.864306 137.207029 l
154.577972 136.47918 l
155.291638 160.498217 l
156.005304 143.757676 l
156.71897 92.444279 l
157.432636 156.858969 l
158.146302 140.846277 l
158.859968 84.074008 l
159.573634 86.985407 l
160.2873 109.912669 l
161.000966 72.428415 l
161.714632 102.270248 l
162.428298 82.982234 l
163.141964 98.631 l
163.85563 79.706911 l
164.569296 122.286113 l
165.996628 83.710084 l
166.710294 82.254384 l
167.42396 92.080354 l
168.137626 96.811376 l
168.851292 91.716429 l
169.564958 74.248039 l
170.278624 79.706911 l
170.99229 92.444279 l
172.419622 69.153092 l
173.133288 129.200684 l
173.846954 70.608791 l
174.56062 73.884114 l
175.274286 91.716429 l
175.987951 68.061317 l
176.701617 123.741812 l
177.415283 73.156264 l
178.128949 109.18482 l
179.556281 74.975888 l
180.269947 101.178474 l
180.983613 111.732293 l
181.697279 73.156264 l
182.410945 73.156264 l
183.124611 77.159437 l
183.838277 88.805031 l
184.551943 60.054972 l
185.265609 73.520189 l
185.979275 65.149919 l
186.692941 74.975888 l
187.406607 80.43476 l
188.120273 79.342986 l
188.833939 71.33664 l
189.547605 104.089872 l
190.261271 69.153092 l
190.974937 104.453797 l
191.688603 76.795512 l
192.402269 110.640519 l
193.115935 105.545572 l
193.829601 87.349332 l
194.543267 85.893632 l
195.256933 89.53288 l
195.970599 84.074008 l
196.684265 73.884114 l
197.397931 105.545572 l
198.111596 77.887287 l
198.825262 115.371541 l
199.538928 68.425242 l
200.252594 75.339813 l
200.96626 91.352504 l
201.679926 83.710084 l
202.393592 80.43476 l
203.107258 117.191165 l
203.820924 109.912669 l
204.53459 89.896805 l
205.248256 82.982234 l
205.961922 77.523362 l
206.675588 98.994925 l
207.389254 71.33664 l
208.10292 75.339813 l
208.816586 94.263903 l
209.530252 102.270248 l
210.243918 84.074008 l
210.957584 116.463316 l
211.67125 105.909496 l
212.384916 99.35885 l
213.098582 100.814549 l
213.812248 78.615136 l
214.525914 67.697392 l
215.23958 71.33664 l
215.953246 91.716429 l
216.666912 84.437933 l
217.380578 93.536053 l
218.094244 88.805031 l
218.80791 74.248039 l
219.521576 69.153092 l
220.235242 77.523362 l
220.948907 66.969543 l
221.662573 88.805031 l
222.376239 102.634173 l
223.089905 72.06449 l
223.803571 77.523362 l
224.517237 93.899978 l
225.230903 66.969543 l
225.944569 77.523362 l
226.658235 107.001271 l
227.371901 100.450624 l
228.085567 113.187993 l
228.799233 70.608791 l
230.226565 98.267076 l
230.940231 69.153092 l
231.653897 78.251212 l
232.367563 69.153092 l
233.081229 109.912669 l
233.794895 66.605618 l
234.508561 96.447452 l
235.222227 76.431588 l
235.935893 89.168956 l
236.649559 78.615136 l
237.363225 83.346159 l
238.076891 65.877768 l
238.790557 103.725948 l
239.504223 75.703738 l
240.217889 69.880941 l
240.931555 72.79234 l
241.645221 74.975888 l
242.358887 87.349332 l
243.072552 82.982234 l
243.786218 71.700565 l
244.499884 85.529708 l
245.21355 106.273421 l
245.927216 86.621482 l
246.640882 76.431588 l
247.354548 62.96637 l
248.068214 74.975888 l
248.78188 73.884114 l
249.495546 69.880941 l
250.209212 71.700565 l
250.922878 69.517016 l
251.636544 88.441106 l
252.35021 62.23852 l
253.063876 69.880941 l
253.777542 69.880941 l
254.491208 79.342986 l
255.204874 67.333468 l
255.91854 92.444279 l
256.632206 74.975888 l
257.345872 69.880941 l
258.059538 104.817722 l
258.773204 102.270248 l
259.48687 81.89046 l
260.200536 67.333468 l
260.914202 77.887287 l
261.627868 90.624655 l
262.341534 74.611964 l
263.0552 72.428415 l
263.768866 80.43476 l
264.482532 64.422069 l
265.196198 74.248039 l
265.909863 74.248039 l
266.623529 67.333468 l
267.337195 70.972716 l
268.050861 85.529708 l
268.764527 69.880941 l
269.478193 85.529708 l
270.191859 75.339813 l
270.905525 80.070836 l
271.619191 74.248039 l
272.332857 72.428415 l
273.046523 81.526535 l
273.760189 67.697392 l
275.187521 80.070836 l
275.901187 70.244866 l
276.614853 68.061317 l
277.328519 84.074008 l
278.042185 77.523362 l
278.755851 73.884114 l
279.469517 86.985407 l
280.183183 82.982234 l
280.896849 65.877768 l
281.610515 68.425242 l
282.324181 77.159437 l
283.037847 76.067663 l
283.751513 78.251212 l
284.465179 67.333468 l
285.178845 73.520189 l
285.892511 72.06449 l
286.606177 66.605618 l
287.319843 83.710084 l
288.033508 69.880941 l
288.747174 69.517016 l
289.46084 64.058144 l
290.174506 84.074008 l
290.888172 83.710084 l
292.315504 69.517016 l
293.02917 70.608791 l
293.742836 70.608791 l
294.456502 78.979061 l
295.170168 68.789167 l
295.883834 68.061317 l
296.5975 64.058144 l
297.311166 72.06449 l
298.024832 73.156264 l
298.738498 72.06449 l
299.452164 74.975888 l
300.16583 88.077181 l
300.879496 67.333468 l
301.593162 64.785994 l
302.306828 75.339813 l
303.020494 68.789167 l
303.73416 69.880941 l
304.447826 73.520189 l
305.161492 73.520189 l
305.875158 66.605618 l
306.588824 65.513844 l
307.30249 74.611964 l
308.016156 94.991752 l
308.729822 65.513844 l
309.443488 63.69422 l
310.157154 65.149919 l
310.870819 73.520189 l
311.584485 65.513844 l
312.298151 66.605618 l
313.011817 71.33664 l
313.725483 68.425242 l
314.439149 69.153092 l
315.152815 75.339813 l
315.866481 66.969543 l
316.580147 75.703738 l
317.293813 64.058144 l
318.007479 72.428415 l
318.721145 65.877768 l
319.434811 66.241693 l
320.148477 64.422069 l
320.862143 64.785994 l
321.575809 72.79234 l
322.289475 73.520189 l
323.003141 78.615136 l
323.716807 66.969543 l
324.430473 68.061317 l
325.144139 65.149919 l
325.857805 74.248039 l
326.571471 71.700565 l
327.285137 65.149919 l
327.998803 66.241693 l
328.712469 63.69422 l
329.426135 68.789167 l
330.139801 64.785994 l
330.853467 66.605618 l
331.567133 64.422069 l
332.280799 63.69422 l
332.994464 67.697392 l
333.70813 70.608791 l
334.421796 66.605618 l
335.135462 64.058144 l
335.849128 63.69422 l
336.562794 88.441106 l
337.27646 68.061317 l
337.990126 62.96637 l
338.703792 60.782821 l
339.417458 62.96637 l
340.131124 65.877768 l
340.84479 65.513844 l
341.558456 63.69422 l
342.272122 67.697392 l
342.985788 65.877768 l
343.699454 66.605618 l
344.41312 65.877768 l
345.126786 63.69422 l
345.840452 68.789167 l
346.554118 64.785994 l
347.267784 65.149919 l
347.98145 64.785994 l
348.695116 66.241693 l
349.408782 64.058144 l
350.122448 65.877768 l
350.836114 64.422069 l
351.54978 66.969543 l
352.263446 70.608791 l
352.977112 65.877768 l
353.690778 66.241693 l
354.404444 67.697392 l
355.11811 65.513844 l
355.831775 64.422069 l
356.545441 71.700565 l
357.259107 64.058144 l
357.972773 64.785994 l
358.686439 74.611964 l
359.400105 64.058144 l
360.113771 65.513844 l
360.827437 64.058144 l
361.541103 70.972716 l
362.254769 65.149919 l
362.968435 65.149919 l
363.682101 63.330295 l
364.395767 67.697392 l
365.109433 63.69422 l
365.823099 65.513844 l
366.536765 65.149919 l
367.250431 66.969543 l
367.964097 67.697392 l
368.677763 66.241693 l
369.391429 68.425242 l
370.105095 66.969543 l
370.818761 64.785994 l
371.532427 64.058144 l
372.246093 60.782821 l
373.673425 72.06449 l
374.387091 67.697392 l
375.100757 69.880941 l
375.814423 67.333468 l
376.528089 72.428415 l
377.241755 64.422069 l
378.669086 63.69422 l
379.382752 62.602445 l
380.096418 68.061317 l
380.810084 65.149919 l
381.52375 69.517016 l
382.237416 68.425242 l
382.951082 81.526535 l
383.664748 64.422069 l
384.378414 72.79234 l
385.09208 84.074008 l
385.805746 73.156264 l
386.519412 68.061317 l
387.233078 65.513844 l
387.946744 66.605618 l
388.66041 68.425242 l
389.374076 66.241693 l
390.087742 74.611964 l
390.801408 64.422069 l
391.515074 69.517016 l
392.22874 66.241693 l
392.942406 65.149919 l
393.656072 62.96637 l
394.369738 68.789167 l
395.083404 65.149919 l
395.79707 70.244866 l
396.510736 67.333468 l
397.224402 74.248039 l
397.938068 75.703738 l
398.651734 65.513844 l
399.3654 76.795512 l
400.079066 76.067663 l
400.792731 76.067663 l
401.506397 69.153092 l
402.220063 77.159437 l
402.933729 66.969543 l
403.647395 66.605618 l
404.361061 73.520189 l
405.074727 85.893632 l
405.788393 66.241693 l
407.215725 66.241693 l
407.929391 66.605618 l
408.643057 68.789167 l
409.356723 69.517016 l
410.070389 65.149919 l
410.784055 64.785994 l
411.497721 66.605618 l
412.211387 89.168956 l
412.925053 70.608791 l
413.638719 70.972716 l
414.352385 67.333468 l
415.066051 71.33664 l
415.779717 80.43476 l
416.493383 65.513844 l
417.207049 73.884114 l
417.920715 91.352504 l
418.634381 68.061317 l
419.348047 68.789167 l
420.061713 65.149919 l
420.775379 76.431588 l
421.489045 72.79234 l
422.202711 63.330295 l
422.916377 65.149919 l
423.630042 68.061317 l
424.343708 65.149919 l
425.057374 64.785994 l
425.77104 66.241693 l
426.484706 74.611964 l
427.198372 72.06449 l
427.912038 64.422069 l
428.625704 69.153092 l
429.33937 74.975888 l
430.053036 68.061317 l
430.766702 92.080354 l
431.480368 77.159437 l
432.194034 66.605618 l
432.194034 66.605618 l
stroke
grestore
0.800 setlinewidth
0 setlinejoin
0.000 setgray
gsave
58.26875 46.971875 m
58.26875 334.8 l
stroke
grestore
gsave
450 46.971875 m
450 334.8 l
stroke
grestore
gsave
58.26875 46.971875 m
450 46.971875 l
stroke
grestore
gsave
58.26875 334.8 m
450 334.8 l
stroke
grestore

end
showpage
