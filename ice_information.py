import cv2
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from skimage.segmentation import random_walker
from skimage.filters import gaussian, threshold_otsu
from skimage.measure import label, regionprops_table, regionprops
import pandas as pd
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler
from scipy.ndimage import binary_dilation, label as ndimage_label, sum as ndimage_sum, mean as ndimage_mean
import matplotlib.patches as patches
import time
from skimage.feature import graycomatrix, graycoprops
from matplotlib.colors import LinearSegmentedColormap
import os

# ===================== 函数定义 =====================

# 分形维数计算函数 - 用于量化表面粗糙度
def fractal_dimension(Z, threshold=0.9):
    """
    使用盒计数法计算二维图像的分形维数
    
    参数:
        Z: 二维数组，输入图像
        threshold: 阈值，用于二值化图像
        
    返回:
        分形维数值
    """
    assert(len(Z.shape) == 2), "输入必须为2D数组"
    # 二值化：这里假设像素值小于阈值的视为前景
    Z = (Z < threshold)
    def boxcount(Z, k):
        S = np.add.reduceat(
                np.add.reduceat(Z, np.arange(0, Z.shape[0], k), axis=0),
                                   np.arange(0, Z.shape[1], k), axis=1)
        return len(np.where((S > 0) & (S < k*k))[0])
    # 最小尺寸
    p = min(Z.shape)
    n = 2**np.floor(np.log2(p))
    sizes = 2**np.arange(np.log2(n), 1, -1)
    counts = []
    for size in sizes:
        counts.append(boxcount(Z, int(size)))
    counts = np.array(counts)
    # 用线性回归拟合对数关系，斜率即分形维数的负值
    coeffs = np.polyfit(np.log(sizes), np.log(counts), 1)
    return -coeffs[0]

# 计算纹理特征函数 - 基于灰度共生矩阵(GLCM)
def calculate_texture_features(image, distances=[1], angles=[0, np.pi/4, np.pi/2, 3*np.pi/4]):
    """
    计算图像的纹理特征
    
    参数:
        image: 输入图像
        distances: 像素距离列表
        angles: 角度列表
        
    返回:
        包含各种纹理特征的字典
    """
    # 确保图像是8位灰度图
    if image.dtype != np.uint8:
        image = (image * 255).astype(np.uint8)
    
    # 计算灰度共生矩阵
    glcm = graycomatrix(image, distances=distances, angles=angles, 
                       levels=256, symmetric=True, normed=True)
    
    # 计算各种纹理特征
    contrast = graycoprops(glcm, 'contrast').mean()
    dissimilarity = graycoprops(glcm, 'dissimilarity').mean()
    homogeneity = graycoprops(glcm, 'homogeneity').mean()
    energy = graycoprops(glcm, 'energy').mean()
    correlation = graycoprops(glcm, 'correlation').mean()
    ASM = graycoprops(glcm, 'ASM').mean()  # 角二阶矩
    
    return {
        '对比度': contrast,
        '非相似性': dissimilarity,
        '同质性': homogeneity,
        '能量': energy,
        '相关性': correlation,
        '角二阶矩': ASM
    }

# 计算形状特征函数
def calculate_shape_features(prop):
    """
    计算区域的形状特征
    
    参数:
        prop: regionprops对象中的一个区域属性
        
    返回:
        包含各种形状特征的字典
    """
    area = prop.area  # 面积
    perimeter = prop.perimeter  # 周长
    eccentricity = prop.eccentricity  # 偏心率
    solidity = prop.solidity  # 坚实度（区域面积与其凸包面积之比）
    extent = prop.extent  # 区域面积与其边界框面积之比
    
    # 计算复杂度（形状因子）
    complexity = perimeter ** 2 / (4 * np.pi * area) if area > 0 else np.nan
    
    # 计算圆形度（圆度）
    circularity = (4 * np.pi * area) / (perimeter ** 2) if perimeter > 0 else np.nan
    
    # 计算矩形度
    bbox_area = prop.bbox_area
    rectangularity = area / bbox_area if bbox_area > 0 else np.nan
    
    # 计算长宽比
    if hasattr(prop, 'major_axis_length') and hasattr(prop, 'minor_axis_length'):
        if prop.minor_axis_length > 0:
            aspect_ratio = prop.major_axis_length / prop.minor_axis_length
        else:
            aspect_ratio = np.nan
    else:
        aspect_ratio = np.nan
    
    return {
        '面积': area,
        '周长': perimeter,
        '偏心率': eccentricity,
        '坚实度': solidity,
        '范围': extent,
        '复杂度': complexity,
        '圆形度': circularity,
        '矩形度': rectangularity,
        '长宽比': aspect_ratio
    }

import matplotlib.pyplot as plt
import numpy as np
import matplotlib.patches as patches
from matplotlib.colors import LinearSegmentedColormap

def visualize_ice_block(labeled_ice, gray_image, thickness_image, target_block, props, thickness_list, fd_values, texture_features_list=None):
    """
    Visualize a single ice block and its features

    Parameters:
        labeled_ice: Labeled ice image
        gray_image: Original grayscale image
        thickness_image: Thickness image
        target_block: Target ice block ID
        props: List of region properties returned by regionprops
        thickness_list: List of thickness values
        fd_values: List of fractal dimension values
        texture_features_list: List of texture features
    """
    if target_block < 1 or target_block > len(props):
        print(f"Error: Ice block ID {target_block} out of range (1-{len(props)})")
        return

    # Create a mask to keep only the target ice block
    target_mask = (labeled_ice == target_block)

    # Get the target ice block properties
    prop = props[target_block - 1]

    # Get shape features
    shape_features = calculate_shape_features(prop)

    # Get thickness and fractal dimension
    thickness = thickness_list[target_block - 1]
    fd = fd_values[target_block - 1]

    # Print the extracted information
    print(f"\nIce Block {target_block} Features:")
    print(f"面积: {shape_features['面积']:.1f} pixels")
    print(f"周长: {shape_features['周长']:.2f} pixels")
    print(f"偏心率: {shape_features['偏心率']:.4f}")
    print(f"复杂度: {shape_features['复杂度']:.4f}")
    print(f"圆形度: {shape_features['圆形度']:.4f}")
    print(f"矩形度: {shape_features['矩形度']:.4f}")
    print(f"长宽比: {shape_features['长宽比']:.4f}")
    print(f"厚度: {thickness:.4f}")
    print(f"分型维度: {fd:.4f}\n")

    if texture_features_list is not None and target_block <= len(texture_features_list):
        texture_features = texture_features_list[target_block - 1]
        print("Texture Features:")
        for key, value in texture_features.items():
            print(f"{key}: {value:.4f}")

    # Create a 2x2 subplot layout with improved visualization
    fig, axes = plt.subplots(2, 2, figsize=(16, 12), dpi=150)

    # 1. Show the ice block in the original grayscale image with better highlighting
    axes[0, 0].imshow(gray_image, cmap='gray', origin='upper')
    # Create a better mask overlay with bright color for selected region
    mask_overlay = np.zeros((*target_mask.shape, 4))  # RGBA
    mask_overlay[target_mask] = [1, 0, 0, 0.6]  # Semi-transparent red
    axes[0, 0].imshow(mask_overlay, origin='upper')
    axes[0, 0].set_title(f"海冰块 {target_block} 在原始图像中的位置", fontsize=14, fontweight='bold')
    axes[0, 0].axis('off')

    # Add contour for better visibility
    contours = cv2.findContours(target_mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if len(contours) == 2:  # OpenCV 4.x returns 2 values
        contours = contours[0]
    else:  # OpenCV 3.x returns 3 values
        contours = contours[1]

    for contour in contours:
        contour = contour.squeeze()
        if len(contour.shape) == 2 and contour.shape[0] > 2:
            axes[0, 0].plot(contour[:, 0], contour[:, 1], 'yellow', linewidth=2)

    # 2. Show the ice block mask with better contrast
    axes[0, 1].imshow(target_mask, cmap='RdYlBu_r', origin='upper')
    axes[0, 1].set_title(f"海冰块 {target_block} 掩膜", fontsize=14, fontweight='bold')
    axes[0, 1].axis('off')

    # 3. Display thickness image with mask overlay
    thickness_masked = thickness_image.copy()
    thickness_masked[~target_mask] = np.nan  # Hide non-selected areas

    im3 = axes[1, 0].imshow(thickness_image, cmap='coolwarm', origin='upper', alpha=0.3)
    im3_overlay = axes[1, 0].imshow(thickness_masked, cmap='coolwarm', origin='upper')
    axes[1, 0].set_title(f"海冰块 {target_block} 厚度信息", fontsize=14, fontweight='bold')
    axes[1, 0].axis('off')

    # Add colorbar for thickness
    plt.colorbar(im3_overlay, ax=axes[1, 0], fraction=0.046, pad=0.04, label='厚度 (归一化)')

    # 4. Show the bounding box and centroid of the ice block
    axes[1, 1].imshow(gray_image, cmap='gray', origin='upper')

    # Draw bounding box
    y_start, x_start, y_stop, x_stop = prop.bbox
    width = x_stop - x_start
    height = y_stop - y_start
    rect = patches.Rectangle((x_start, y_start), width, height,
                           linewidth=3, edgecolor='red', facecolor='none', linestyle='--')
    axes[1, 1].add_patch(rect)

    # Draw centroid
    centroid_y, centroid_x = prop.centroid
    axes[1, 1].plot(centroid_x, centroid_y, 'ro', markersize=10, markerfacecolor='yellow',
                   markeredgecolor='red', markeredgewidth=2, label='质心')

    # Draw orientation line if available
    if hasattr(prop, 'orientation'):
        y0, x0 = prop.centroid
        orientation = prop.orientation
        x1 = x0 + np.cos(orientation) * 0.5 * prop.major_axis_length
        y1 = y0 - np.sin(orientation) * 0.5 * prop.major_axis_length
        x2 = x0 - np.cos(orientation) * 0.5 * prop.major_axis_length
        y2 = y0 + np.sin(orientation) * 0.5 * prop.major_axis_length
        axes[1, 1].plot([x1, x2], [y1, y2], 'g-', linewidth=2, label='主轴方向')

    axes[1, 1].set_title(f"海冰块 {target_block} 边界框和几何特征", fontsize=14, fontweight='bold')
    axes[1, 1].axis('off')
    axes[1, 1].legend(loc='upper right')

    # Add text box with key statistics
    stats_text = f"""面积: {shape_features['面积']:.0f} 像素
周长: {shape_features['周长']:.1f} 像素
复杂度: {shape_features['复杂度']:.3f}
圆形度: {shape_features['圆形度']:.3f}
厚度: {thickness:.3f}
分形维数: {fd:.3f}"""

    axes[1, 1].text(0.02, 0.98, stats_text, transform=axes[1, 1].transAxes,
                   fontsize=10, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.show()

# ===================== 主函数 =====================
def analyze_sea_ice(ice_image, gray_image=None, show_plots=True, interactive=True):
    """
    海冰分析主函数
    
    参数:
        ice_image: 二值化的海冰图像（numpy数组），白色（255）表示海冰，黑色（0）表示背景
        gray_image: 原始灰度图像，用于厚度估计，如果为None则使用全1矩阵
        show_plots: 是否显示可视化结果图表
        interactive: 是否启用交互式查看单个海冰块功能
        
    返回:
        dict: 包含分析结果的字典，包括：
            - labeled_ice: 标记后的海冰图像
            - num_features: 识别出的海冰块数量
            - props: 区域属性列表
            - thickness_list: 厚度列表
            - fd_values: 分形维数列表
            - texture_features_list: 纹理特征列表
    """
    # 检查输入参数
    if ice_image is None:
        print("错误：未提供海冰图像！")
        return None
    
    # 如果未提供灰度图像，则创建一个全1矩阵作为替代
    if gray_image is None:
        gray_image = np.ones_like(ice_image, dtype=np.uint8) * 128  # 使用中间灰度值
    
    # ===================== 1. 海冰连通区域识别 =====================
    # 识别连通区域，每个连通区域可以视为一块独立的海冰
    labeled_ice, num_features = ndimage_label(ice_image)
    
    if show_plots:
        # 可视化每块海冰(用颜色区分)
        plt.figure(figsize=(10, 8))
        plt.imshow(labeled_ice, cmap='nipy_spectral')
        plt.title(f"海冰块识别结果: {num_features}块", fontsize=14)
        plt.axis("off")
        plt.colorbar(label='海冰块编号')
        plt.show()
    
    # ===================== 2. 海冰厚度估计 =====================
    # 使用灰度值估算厚度
    scaler = MinMaxScaler(feature_range=(0, 1))  # 归一化到0-1范围
    gray_image_flattened = gray_image.flatten().reshape(-1, 1)
    normalized_gray_image_flattened = scaler.fit_transform(gray_image_flattened)
    thickness_image = normalized_gray_image_flattened.reshape(gray_image.shape)
    
    if show_plots:
        # 可视化厚度分布
        plt.figure(figsize=(10, 8))
        plt.imshow(thickness_image, cmap='jet')
        plt.title("海冰厚度估计分布", fontsize=14)
        plt.colorbar(label='估计厚度 (归一化单位)')
        plt.axis("off")
        plt.show()
    
    # ===================== 3. 海冰特征计算 =====================
    # 计算每块海冰的面积
    areas = ndimage_sum(np.ones_like(gray_image), labels=labeled_ice, index=np.arange(1, num_features + 1))
    
    # 计算每块海冰的平均厚度
    thickness_list = ndimage_mean(thickness_image, labels=labeled_ice, index=range(1, num_features + 1))
    
    # 使用regionprops计算形状特征
    props = regionprops(labeled_ice, intensity_image=gray_image)
    
    # 计算分形维数
    fd_values = []
    for i in range(1, num_features + 1):
        binary_ice = (labeled_ice == i).astype(np.uint8)
        fd = fractal_dimension(binary_ice, threshold=0.5)
        fd_values.append(fd)
    
    # 计算纹理特征
    texture_features_list = []
    for i in range(1, num_features + 1):
        # 提取当前海冰块的掩膜和对应的灰度图像区域
        mask = (labeled_ice == i)
        # 创建一个只包含当前海冰块的图像
        ice_block_image = np.zeros_like(gray_image)
        ice_block_image[mask] = gray_image[mask]
        
        # 获取边界框，只处理边界框内的区域以提高效率
        props_i = props[i-1]
        y_start, x_start, y_stop, x_stop = props_i.bbox
        roi = ice_block_image[y_start:y_stop, x_start:x_stop]
        
        # 如果区域太小，可能无法计算纹理特征
        if roi.shape[0] < 3 or roi.shape[1] < 3:
            texture_features_list.append({
                '对比度': np.nan,
                '非相似性': np.nan,
                '同质性': np.nan,
                '能量': np.nan,
                '相关性': np.nan,
                '角二阶矩': np.nan
            })
        else:
            # 计算纹理特征
            texture_features = calculate_texture_features(roi)
            texture_features_list.append(texture_features)
    
    # 准备返回结果
    results = {
        'labeled_ice': labeled_ice,
        'num_features': num_features,
        'props': props,
        'thickness_list': thickness_list,
        'fd_values': fd_values,
        'texture_features_list': texture_features_list,
        'thickness_image': thickness_image
    }
    
    # ===================== 4. 交互式查看单个海冰块 =====================
    if interactive:
        while True:
            try:
                print("\n请输入要查看的海冰块ID (1-{})，输入0退出: ".format(num_features))
                target_block = int(input())
                
                if target_block == 0:
                    break
                elif 1 <= target_block <= num_features:
                    # 可视化选定的海冰块
                    visualize_ice_block(labeled_ice, gray_image, thickness_image, 
                                       target_block, props, thickness_list, 
                                       fd_values, texture_features_list)
                    
                else:
                    print(f"错误：请输入1到{num_features}之间的整数")
            except ValueError:
                print("错误：请输入有效的整数")
            except Exception as e:
                print(f"错误：{e}")
    
    return results

