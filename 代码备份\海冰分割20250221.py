import torch
import torch.nn as nn
import torch.optim as optim

# U-Net 网络设计
class UNetEncoderBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(UNetEncoderBlock, self).__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)

    def forward(self, x):
        return self.pool(self.conv(x)), self.conv(x)

class UNetDecoderBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(UNetDecoderBlock, self).__init__()
        self.upconv = nn.ConvTranspose2d(in_channels, out_channels, kernel_size=2, stride=2)
        self.conv = nn.Sequential(
            nn.Conv2d(2*out_channels, out_channels, kernel_size=3, padding=1), # 输入通道数翻倍，因为有跳跃连接
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x, skip):
        x = self.upconv(x)
        x = torch.cat((x, skip), dim=1) # 跳跃连接
        return self.conv(x)

class UNet(nn.Module):
    def __init__(self, in_channels=1, out_channels=1, features=[64, 128, 256, 512]):
        super(UNet, self).__init__()
        self.encoders = nn.ModuleList()
        self.decoders = nn.ModuleList()
        self.input_conv = nn.Sequential(
            nn.Conv2d(in_channels, features[0], kernel_size=3, padding=1),
            nn.BatchNorm2d(features[0]),
            nn.ReLU(inplace=True),
            nn.Conv2d(features[0], features[0], kernel_size=3, padding=1),
            nn.BatchNorm2d(features[0]),
            nn.ReLU(inplace=True)
        )

        # 编码器
        for feature in features:
            self.encoders.append(UNetEncoderBlock(features[features.index(feature)-1] if feature != features[0] else features[0] , feature))
        # 解码器
        for feature in reversed(features):
            self.decoders.append(UNetDecoderBlock(feature, features[features.index(feature)-1] if features.index(feature) != 0 else features[0]))

        self.bottleneck_conv = nn.Sequential(
            nn.Conv2d(features[-1], features[-1]*2, kernel_size=3, padding=1),
            nn.BatchNorm2d(features[-1]*2),
            nn.ReLU(inplace=True),
            nn.Conv2d(features[-1]*2, features[-1], kernel_size=3, padding=1),
            nn.BatchNorm2d(features[-1]),
            nn.ReLU(inplace=True)
        )

        self.output_conv = nn.Conv2d(features[0], out_channels, kernel_size=1)

    def forward(self, x):
        skips = []
        x = self.input_conv(x)
        for encoder in self.encoders:
            x, skip = encoder(x)
            skips.append(skip)

        x = self.bottleneck_conv(x)

        skips = reversed(skips)
        for decoder, skip in zip(self.decoders, skips):
            x = decoder(x, skip)

        return self.output_conv(x)


# 判别模型设计 (GAN)
class Discriminator(nn.Module):
    def __init__(self, in_channels=1, features=[64, 128, 256, 512]):
        super(Discriminator, self).__init__()
        self.discriminator_blocks = nn.ModuleList()

        def discriminator_block(in_channels, out_channels, stride):
            return nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=4, stride=stride, padding=1, bias=False, padding_mode="reflect"),
                nn.BatchNorm2d(out_channels),
                nn.LeakyReLU(0.2, inplace=True),
            )

        self.initial_block = nn.Sequential(
            nn.Conv2d(in_channels, features[0], kernel_size=4, stride=2, padding=1, padding_mode="reflect"),
            nn.LeakyReLU(0.2, inplace=True),
        )

        in_channels_list = features
        for feature in features[1:]:
            self.discriminator_blocks.append(discriminator_block(in_channels_list[features.index(feature)-1], feature, stride=2 if feature != features[-1] else 1))

        self.final_block = nn.Sequential(
            nn.Conv2d(features[-1], features[-1]*2, kernel_size=4, padding=1),
            nn.BatchNorm2d(features[-1]*2),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(features[-1]*2, 1, kernel_size=4, padding=1), # 输出 1 通道，代表真假
            nn.Sigmoid() # 输出概率
        )

    def forward(self, x):
        x = self.initial_block(x)
        for block in self.discriminator_blocks:
            x = block(x)
        return self.final_block(x)


# ConvLSTM 模型设计
class ConvLSTMCell(nn.Module):
    def __init__(self, input_channels, hidden_channels, kernel_size=3):
        super(ConvLSTMCell, self).__init__()

        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.kernel_size = kernel_size
        self.padding = kernel_size // 2

        self.conv_i = nn.Conv2d(input_channels + hidden_channels, hidden_channels, kernel_size, padding=self.padding)
        self.conv_f = nn.Conv2d(input_channels + hidden_channels, hidden_channels, kernel_size, padding=self.padding)
        self.conv_g = nn.Conv2d(input_channels + hidden_channels, hidden_channels, kernel_size, padding=self.padding)
        self.conv_o = nn.Conv2d(input_channels + hidden_channels, hidden_channels, kernel_size, padding=self.padding)

    def forward(self, x, hidden_state):
        h_prev, c_prev = hidden_state

        combined = torch.cat((x, h_prev), dim=1)  # concatenate along channel dimension

        i = torch.sigmoid(self.conv_i(combined))
        f = torch.sigmoid(self.conv_f(combined))
        g = torch.tanh(self.conv_g(combined))
        o = torch.sigmoid(self.conv_o(combined))

        c_cur = f * c_prev + i * g
        h_cur = o * torch.tanh(c_cur)

        return h_cur, c_cur

class ConvLSTM(nn.Module):
    def __init__(self, input_channels, hidden_channels, num_layers, kernel_size=3, output_channels=1):
        super(ConvLSTM, self).__init__()

        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.num_layers = num_layers

        cell_list = []
        for i in range(0, num_layers):
            cur_input_channels = input_channels if i == 0 else hidden_channels[i-1]
            cell_list.append(ConvLSTMCell(cur_input_channels, hidden_channels[i], kernel_size))

        self.cell_list = nn.ModuleList(cell_list)
        self.output_conv = nn.Conv2d(hidden_channels[-1], output_channels, kernel_size=1) # 输出层卷积

    def forward(self, input_tensor, hidden_state=None):
        """
        Args:
            input_tensor (torch.Tensor):  具有形状 (b, t, c, h, w) 的时间序列，其中
              b - 批次大小
              t - 时间步长
              c - 通道数
              h - 高度
              w - 宽度
            hidden_state (list of tuples): 初始化隐藏状态和细胞状态。
        Returns:
            layer_output_list, last_state_list
        """
        batch_size, time_steps, _, height, width = input_tensor.size()
        if hidden_state is None:
            hidden_state = self._init_hidden(batch_size=batch_size, image_size=(height, width))

        layer_output_list = []
        seq_len = time_steps
        current_input = input_tensor

        for layer_idx in range(self.num_layers):
            output_inner = []
            for t in range(seq_len):
                hidden_state[layer_idx] = self.cell_list[layer_idx](input_tensor=current_input[:, t, :, :, :],
                                                     hidden_state=hidden_state[layer_idx])
                output_inner.append(hidden_state[layer_idx][0])

            layer_output = torch.stack(output_inner, dim=1)
            current_input = layer_output
            layer_output_list.append(layer_output)

        # 仅取最后一个时间步的输出进行预测，并经过一个卷积层调整通道数
        last_output = layer_output_list[-1][:, -1, :, :, :] # (b, c, h, w)
        next_frame_prediction = self.output_conv(last_output) # (b, output_channels, h, w)

        return next_frame_prediction

    def _init_hidden(self, batch_size, image_size):
        init_states = []
        for i in range(self.num_layers):
            init_states.append((torch.zeros(batch_size, self.hidden_channels[i], *image_size, requires_grad=self.trainable).cuda(),
                                 torch.zeros(batch_size, self.hidden_channels[i], *image_size, requires_grad=self.trainable).cuda()))
        return init_states


# 损失函数
def reconstruction_loss(output, target):
    # 例如：均方误差损失 (MSE Loss)
    return nn.MSELoss()(output, target)

def adversarial_loss(判别器输出, 标签):
    # 例如：二元交叉熵损失 (Binary Cross Entropy Loss)
    return nn.BCELoss()(判别器输出, 标签)

# 训练参数设置
lr = 0.0002
beta1 = 0.5
num_epochs = 100
batch_size = 32
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 初始化模型
unet_generator = UNet(in_channels=1, out_channels=1).to(device) # 输入输出通道数为1，假设流场数据是单通道灰度图像
discriminator = Discriminator(in_channels=1).to(device)
convlstm_predictor = ConvLSTM(input_channels=1, hidden_channels=[64, 64], num_layers=2, output_channels=1).to(device) # ConvLSTM 输入通道数为1

# 初始化优化器
optimizer_G = optim.Adam(unet_generator.parameters(), lr=lr, betas=(beta1, 0.999))
optimizer_D = optim.Adam(discriminator.parameters(), lr=lr, betas=(beta1, 0.999))
optimizer_ConvLSTM = optim.Adam(convlstm_predictor.parameters(), lr=lr, betas=(beta1, 0.999))


# 示例数据 (需要替换为真实数据加载)
def generate_dummy_data(num_time_steps=1000, low_res_size=(20, 50), high_res_size=(200, 500), batch_size=batch_size):
    # 模拟低分辨率数据 (Y) 和 高分辨率数据 (X)
    low_res_data = torch.randn(num_time_steps, 1, *low_res_size) # (T, C, H, W)
    high_res_data = torch.randn(num_time_steps, 1, *high_res_size) # (T, C, H, W)

    # 模拟 ConvLSTM 的输入数据，例如使用过去 5 个时间步的低分辨率数据预测下一个高分辨率
    sequence_length = 5
    convlstm_input_data = []
    convlstm_target_data = []
    for i in range(sequence_length, num_time_steps):
        convlstm_input_data.append(low_res_data[i-sequence_length:i]) # 输入序列
        convlstm_target_data.append(high_res_data[i]) # 目标高分辨率数据

    convlstm_input_data = torch.stack(convlstm_input_data) # (N, seq_len, C, H, W)
    convlstm_target_data = torch.stack(convlstm_target_data) # (N, C, H, W)

    # 转换为 Dataset 和 DataLoader (简化示例，实际应用中需要更完善的数据处理)
    from torch.utils.data import TensorDataset, DataLoader
    unet_dataset = TensorDataset(low_res_data, high_res_data)
    unet_dataloader = DataLoader(unet_dataset, batch_size=batch_size, shuffle=True)

    convlstm_dataset = TensorDataset(convlstm_input_data, convlstm_target_data)
    convlstm_dataloader = DataLoader(convlstm_dataset, batch_size=batch_size, shuffle=True)

    return unet_dataloader, convlstm_dataloader

unet_dataloader, convlstm_dataloader = generate_dummy_data()


# 训练循环 (简化示例，实际应用中需要更完善的训练流程)
def train_unet_gan(unet_generator, discriminator, unet_dataloader, optimizer_G, optimizer_D, num_epochs, device):
    bce_loss = nn.BCELoss()
    l1_loss = nn.L1Loss() # 或 MSELoss() 作为重建损失
    adversarial_loss_factor = 0.01 # 对抗损失权重
    l1_loss_factor = 1.0 # 重建损失权重

    for epoch in range(num_epochs):
        for i, (low_res_batch, high_res_batch) in enumerate(unet_dataloader):
            low_res_batch = low_res_batch.to(device)
            high_res_batch = high_res_batch.to(device)

            # 训练判别器
            optimizer_D.zero_grad()
            fake_high_res = unet_generator(low_res_batch)
            real_label = torch.ones((low_res_batch.size(0), 1, 15, 37)).to(device) # 判别器输出尺寸需要根据网络结构调整
            fake_label = torch.zeros((low_res_batch.size(0), 1, 15, 37)).to(device) # 判别器输出尺寸需要根据网络结构调整

            output_real = discriminator(high_res_batch)
            loss_D_real = bce_loss(output_real, real_label)
            output_fake = discriminator(fake_high_res.detach()) # detach() 避免梯度传到生成器
            loss_D_fake = bce_loss(output_fake, fake_label)
            loss_D = (loss_D_real + loss_D_fake) / 2
            loss_D.backward()
            optimizer_D.step()

            # 训练生成器
            optimizer_G.zero_grad()
            output_fake_G = discriminator(fake_high_res) # 再次通过判别器，为了计算生成器的对抗损失
            adversarial_G_loss = bce_loss(output_fake_G, real_label) # 生成器希望判别器判断为真
            l1_G_loss = l1_loss(fake_high_res, high_res_batch) # 重建损失
            loss_G = adversarial_loss_factor * adversarial_G_loss + l1_loss_factor * l1_G_loss
            loss_G.backward()
            optimizer_G.step()

            if (i+1) % 10 == 0:
                print(f"Epoch [{epoch+1}/{num_epochs}], Step [{i+1}/{len(unet_dataloader)}], Loss D: {loss_D.item():.4f}, Loss G: {loss_G.item():.4f}")


def train_convlstm(convlstm_predictor, convlstm_dataloader, optimizer_ConvLSTM, num_epochs, device):
    mse_loss = nn.MSELoss()

    for epoch in range(num_epochs):
        for i, (input_seq_batch, target_next_frame_batch) in enumerate(convlstm_dataloader):
            input_seq_batch = input_seq_batch.to(device)
            target_next_frame_batch = target_next_frame_batch.to(device)

            optimizer_ConvLSTM.zero_grad()
            next_frame_pred = convlstm_predictor(input_seq_batch) # 输入形状 (b, t, c, h, w)
            loss_convlstm = mse_loss(next_frame_pred, target_next_frame_batch)
            loss_convlstm.backward()
            optimizer_ConvLSTM.step()

            if (i+1) % 10 == 0:
                print(f"Epoch [{epoch+1}/{num_epochs}], Step [{i+1}/{len(convlstm_dataloader)}], ConvLSTM Loss: {loss_convlstm.item():.4f}")


# 模型训练
print("开始 U-Net 和 GAN 训练...")
train_unet_gan(unet_generator, discriminator, unet_dataloader, optimizer_G, optimizer_D, num_epochs, device)
print("U-Net 和 GAN 训练完成!")

print("开始 ConvLSTM 训练...")
train_convlstm(convlstm_predictor, convlstm_dataloader, optimizer_ConvLSTM, num_epochs, device)
print("ConvLSTM 训练完成!")

print("代码已生成，训练过程已启动 (示例数据). 请替换为真实数据并根据需要调整模型和训练参数.")