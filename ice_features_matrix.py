import numpy as np
import cv2
from scipy.ndimage import zoom
from skimage.measure import regionprops
from scipy.ndimage import label as ndimage_label, sum as ndimage_sum, mean as ndimage_mean
from sklearn.preprocessing import MinMaxScaler
from ice_information import analyze_sea_ice, calculate_texture_features, calculate_shape_features, fractal_dimension
from enhanced_ice_visualization import calculate_breakability_index

def create_feature_matrices(ice_image, gray_image=None, target_size=(256, 256), return_list=True):
    """
    将海冰的厚度、面积、形状复杂度、纹理对比度和分形维数等特征转换为指定大小的矩阵形式
    
    参数:
        ice_image: 二值化的海冰图像（numpy数组），白色（255）表示海冰，黑色（0）表示背景
        gray_image: 原始灰度图像，用于厚度估计，如果为None则使用全1矩阵
        target_size: 输出特征矩阵的目标大小，默认为(256, 256)
        return_list: 是否以列表形式返回特征矩阵，默认为True
        
    返回:
        如果return_list=True，返回list: 包含各个特征矩阵的列表，顺序为[厚度, 面积, 复杂度, 纹理对比度, 分形维数, 可破坏性]
        如果return_list=False，返回dict: 包含各个特征矩阵的字典
    """
    # 首先使用海冰分析函数获取基本特征
    print("正在进行海冰特征分析...")
    results = analyze_sea_ice(ice_image, gray_image, show_plots=False, interactive=False)
    
    if results is None:
        print("错误：海冰分析失败！")
        return None
    
    # 提取分析结果
    labeled_ice = results['labeled_ice']
    num_features = results['num_features']
    props = results['props']
    thickness_list = results['thickness_list']
    fd_values = results['fd_values']
    texture_features_list = results['texture_features_list']
    thickness_image = results['thickness_image']
    
    # 获取原始图像尺寸
    h, w = labeled_ice.shape
    
    # 创建特征矩阵
    thickness_matrix = np.zeros((h, w))
    area_matrix = np.zeros((h, w))
    complexity_matrix = np.zeros((h, w))
    texture_contrast_matrix = np.zeros((h, w))
    fd_matrix = np.zeros((h, w))
    breakability_matrix = np.zeros((h, w))
    
    # 填充特征矩阵
    print("正在生成特征矩阵...")
    for i, prop in enumerate(props):
        # 获取各项特征
        thickness = thickness_list[i]
        area = prop.area
        perimeter = prop.perimeter
        
        # 计算形状复杂度（周长^2 / (4 * π * 面积)）
        complexity = perimeter**2 / (4 * np.pi * area) if area > 0 else 0
        
        # 获取纹理对比度
        texture_contrast = texture_features_list[i]['对比度'] if not np.isnan(texture_features_list[i]['对比度']) else 0
        
        # 获取分形维数
        fd = fd_values[i]
        
        # 计算可破坏性指数
        breakability = calculate_breakability_index(thickness, area, perimeter, texture_contrast, fd)
        
        # 创建掩膜
        mask = (labeled_ice == i+1)
        
        # 将特征值分配给对应的海冰块区域
        thickness_matrix[mask] = thickness
        area_matrix[mask] = area
        complexity_matrix[mask] = complexity
        texture_contrast_matrix[mask] = texture_contrast
        fd_matrix[mask] = fd
        breakability_matrix[mask] = breakability
    
    # 归一化特征矩阵（确保所有特征都在0-1范围内）
    print("正在归一化特征矩阵...")
    
    # 厚度归一化（确保在0-1范围内）
    thickness_min = np.min(thickness_matrix)
    thickness_max = np.max(thickness_matrix)
    if thickness_max > thickness_min:
        thickness_matrix = (thickness_matrix - thickness_min) / (thickness_max - thickness_min)
    
    # 面积归一化
    if np.max(area_matrix) > 0:
        area_matrix = area_matrix / np.max(area_matrix)
    
    # 复杂度归一化（通常复杂度值大于1，圆形为1）
    complexity_min = np.min(complexity_matrix)
    complexity_max = np.max(complexity_matrix)
    if complexity_max > complexity_min:
        complexity_matrix = (complexity_matrix - complexity_min) / (complexity_max - complexity_min)
    
    # 纹理对比度归一化
    contrast_min = np.min(texture_contrast_matrix)
    contrast_max = np.max(texture_contrast_matrix)
    if contrast_max > contrast_min:
        texture_contrast_matrix = (texture_contrast_matrix - contrast_min) / (contrast_max - contrast_min)
    
    # 分形维数归一化
    fd_min = np.min(fd_matrix)
    fd_max = np.max(fd_matrix)
    if fd_max > fd_min:
        fd_matrix = (fd_matrix - fd_min) / (fd_max - fd_min)
    
    # 可破坏性指数归一化（确保在0-1范围内）
    breakability_min = np.min(breakability_matrix)
    breakability_max = np.max(breakability_matrix)
    if breakability_max > breakability_min:
        breakability_matrix = (breakability_matrix - breakability_min) / (breakability_max - breakability_min)
    
    # 调整特征矩阵大小到目标尺寸
    print(f"正在调整特征矩阵大小到{target_size}...")
    zoom_factor_h = target_size[0] / h
    zoom_factor_w = target_size[1] / w
    
    thickness_matrix_resized = zoom(thickness_matrix, (zoom_factor_h, zoom_factor_w), order=1)
    area_matrix_resized = zoom(area_matrix, (zoom_factor_h, zoom_factor_w), order=1)
    complexity_matrix_resized = zoom(complexity_matrix, (zoom_factor_h, zoom_factor_w), order=1)
    texture_contrast_matrix_resized = zoom(texture_contrast_matrix, (zoom_factor_h, zoom_factor_w), order=1)
    fd_matrix_resized = zoom(fd_matrix, (zoom_factor_h, zoom_factor_w), order=1)
    breakability_matrix_resized = zoom(breakability_matrix, (zoom_factor_h, zoom_factor_w), order=1)
    
    # 创建特征矩阵字典
    feature_matrices_dict = {
        'thickness': thickness_matrix_resized,
        'area': area_matrix_resized,
        'complexity': complexity_matrix_resized,
        'texture_contrast': texture_contrast_matrix_resized,
        'fractal_dimension': fd_matrix_resized,
        'breakability': breakability_matrix_resized
    }
    
    print("特征矩阵生成完成！")
    
    # 根据参数决定返回字典还是列表
    if return_list:
        # 创建一个列表，按指定顺序包含所有特征矩阵
        feature_matrices_list = [
            thickness_matrix_resized,      # 厚度
            area_matrix_resized,           # 面积
            complexity_matrix_resized,     # 复杂度
            texture_contrast_matrix_resized, # 纹理对比度
            fd_matrix_resized,             # 分形维数
            breakability_matrix_resized    # 可破坏性
        ]
        return feature_matrices_list
    else:
        return feature_matrices_dict

# 示例使用方法
def main():
    # 加载海冰图像（这里需要替换为实际的图像路径）
    try:
        # 尝试加载示例图像
        ice_image_path = "sea_ice/sea_ice_001.jpg"  # 替换为实际路径
        original_image = cv2.imread(ice_image_path)
        
        if original_image is None:
            print(f"无法加载图像: {ice_image_path}")
            return
        
        # 转换为灰度图
        gray_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
        
        # 简单二值化处理，将海冰区域设为255（白色），背景设为0（黑色）
        # 实际应用中可能需要更复杂的分割方法
        _, ice_image = cv2.threshold(gray_image, 127, 255, cv2.THRESH_BINARY)
        
        # 生成特征矩阵（以字典形式获取，方便打印和保存）
        feature_matrices = create_feature_matrices(ice_image, gray_image, return_list=False)
        
        # 同时获取列表形式的特征矩阵，用于后续处理
        feature_matrices_list = create_feature_matrices(ice_image, gray_image, return_list=True)
        
        if feature_matrices is not None:
            # 打印每个特征矩阵的形状和值范围
            for name, matrix in feature_matrices.items():
                print(f"{name} 矩阵形状: {matrix.shape}, 值范围: [{np.min(matrix):.4f}, {np.max(matrix):.4f}]")
            
            # 可以选择保存特征矩阵为numpy数组文件
            for name, matrix in feature_matrices.items():
                np.save(f"{name}_feature_matrix.npy", matrix)
                print(f"已保存 {name}_feature_matrix.npy")
                
            # 打印列表形式的特征矩阵信息
            print("\n列表形式的特征矩阵:")
            feature_names = ['厚度', '面积', '复杂度', '纹理对比度', '分形维数', '可破坏性']
            for i, matrix in enumerate(feature_matrices_list):
                print(f"{feature_names[i]} 矩阵形状: {matrix.shape}, 值范围: [{np.min(matrix):.4f}, {np.max(matrix):.4f}]")
            
            # 保存整个特征矩阵列表为单个numpy数组
            feature_array = np.stack(feature_matrices_list, axis=2)  # 将列表中的矩阵堆叠为三维数组
            np.save("ice_features_array.npy", feature_array)
            print(f"已保存完整特征数组，形状: {feature_array.shape}")
            
            print("\n可以通过索引访问列表中的特征矩阵，例如:")
            print("feature_matrices_list[0] - 获取厚度矩阵")
            print("feature_matrices_list[1] - 获取面积矩阵")
            print("...等等")
    
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()