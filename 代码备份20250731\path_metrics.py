# -*- coding: utf-8 -*-
"""
Created on Thu Apr 10 10:44:00 2025

@author: 29607
"""

# path_metrics.py
import math
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional

# =============================================================================
# 路径插值与基础函数
# =============================================================================

def bresenham_line(start: tuple, end: tuple) -> list:
    """利用Bresenham算法得到离散直线上的整数坐标点列表。

    Args:
        start (tuple): 起点坐标 (row, col) 或 (x, y)，取决于调用约定。
                       内部计算假设为整数坐标。
        end (tuple): 终点坐标 (row, col) 或 (x, y)。

    Returns:
        list: 包含从起点到终点（含两端）的整数坐标点 (tuple) 的列表。
    """
    x1, y1 = int(start[0]), int(start[1]) # 确保为整数
    x2, y2 = int(end[0]), int(end[1])
    dx = abs(x2 - x1)
    dy = -abs(y2 - y1)
    sx = 1 if x1 < x2 else -1
    sy = 1 if y1 < y2 else -1
    err = dx + dy
    line_points = []
    while True:
        line_points.append((x1, y1))
        if x1 == x2 and y1 == y2:
            break
        e2 = 2 * err
        if e2 >= dy:
            err += dy
            x1 += sx
        if e2 <= dx:
            err += dx
            y1 += sy
    return line_points

def interpolate_path(path: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
    """
    对路径进行插值，使其包含所有关键点之间连线上的所有栅格点。
    使用 Bresenham 线算法。

    Args:
        path (List[Tuple[int, int]]): 路径关键点的坐标列表，期望为 (row, col)。

    Returns:
        List[Tuple[int, int]]: 插值后的完整路径点列表，已去除重复点。
    """
    if not path or len(path) < 2:
        return [] # 返回空列表而不是None
    interpolated_path_with_duplicates = []
    for i in range(len(path) - 1):
        # Bresenham处理的是整数坐标点之间的连线
        line_points = bresenham_line(path[i], path[i+1])
        # 检查 line_points 是否为空或 None (理论上bresenham不应返回None)
        if line_points:
            # 第一个点会在上一段的末尾重复，除了路径起点
            if interpolated_path_with_duplicates:
                interpolated_path_with_duplicates.extend(line_points[1:])
            else:
                interpolated_path_with_duplicates.extend(line_points)

    # 使用字典快速去重并保持顺序 (Python 3.7+)
    # 或者使用 set 去重再排序（如果顺序不重要或路径点可哈希）
    # 这里用 dict.fromkeys 保序
    if not interpolated_path_with_duplicates:
         return []

    # 确认坐标是整数元组
    # seen = set()
    # unique_interpolated_path = []
    # for point in interpolated_path_with_duplicates:
    #     int_point = (int(point[0]), int(point[1]))
    #     if int_point not in seen:
    #         unique_interpolated_path.append(int_point)
    #         seen.add(int_point)
    # return unique_interpolated_path

    # 更简洁的去重（假设点是可哈希的元组）
    return list(dict.fromkeys(interpolated_path_with_duplicates))


# =============================================================================
# 路径指标计算函数
# =============================================================================

def compute_pixel_length(path: List[Tuple[int, int]]) -> int:
    """
    计算路径长度（插值后经过的唯一像素点数量）。

    Args:
        path (List[Tuple[int, int]]): 路径关键点的坐标列表 (row, col)。

    Returns:
        int: 路径上的像素点数量。
    """
    interpolated = interpolate_path(path)
    return len(interpolated)

def compute_euclidean_length(path: List[Tuple[int, int]]) -> float:
    """
    计算路径的欧几里得几何长度。

    Args:
        path (List[Tuple[int, int]]): 路径关键点的坐标列表 (row, col)。

    Returns:
        float: 路径的欧几里得长度。
    """
    length = 0.0
    if not path or len(path) < 2:
        return 0.0
    for i in range(len(path) - 1):
        p1 = path[i]
        p2 = path[i+1]
        length += math.hypot(p2[0] - p1[0], p2[1] - p1[1])
    return length

def compute_turn_count(path: List[Tuple[int, int]]) -> int:
    """
    计算路径在关键点处的转弯次数。

    Args:
        path (List[Tuple[int, int]]): 路径关键点的坐标列表 (row, col)。

    Returns:
        int: 转弯次数。
    """
    turn_count = 0
    if len(path) < 3:
        return 0
    # 计算初始方向向量 (归一化或仅方向即可)
    dx0, dy0 = path[1][0] - path[0][0], path[1][1] - path[0][1]

    for i in range(1, len(path) - 1):
        dx1, dy1 = path[i+1][0] - path[i][0], path[i+1][1] - path[i][1]
        # 检查方向是否改变 (避免浮点数问题，可以用向量叉乘或比较斜率，或者简单比较增量)
        # 简单比较增量可能因同一直线上的不同长度段而出错，最好比较方向
        # 检查方向向量是否不成比例 (叉乘不为0，考虑浮点误差)
        # 或者检查归一化向量是否不同
        if dx0 * dy1 != dx1 * dy0: # 简化的方向改变检查（如果向量平行则叉乘为0）
             # 需要更鲁棒的检查，例如检查向量夹角是否显著大于0
             # 或者，如果移动方向 (dx, dy) 的符号或比例发生变化
             # 简单的方向改变：只要后一段的 (dx, dy) 与前一段不同即可
             current_move = (dx1, dy1)
             prev_move = (dx0, dy0)
             # 处理零向量的情况
             if (dx1 != 0 or dy1 != 0) and (dx0 != 0 or dy0 != 0) and current_move != prev_move:
                 # 如果只是长度不同但方向相同，不算转弯
                 # 检查归一化方向是否相同
                 mag0 = math.hypot(dx0, dy0)
                 mag1 = math.hypot(dx1, dy1)
                 if mag0 > 1e-6 and mag1 > 1e-6: # 避免除零
                      norm0 = (dx0/mag0, dy0/mag0)
                      norm1 = (dx1/mag1, dy1/mag1)
                      # 比较归一化向量是否足够接近
                      if not math.isclose(norm0[0], norm1[0]) or not math.isclose(norm0[1], norm1[1]):
                           turn_count += 1
                 elif current_move != prev_move: # 如果有零向量，但移动段不同，也算转弯
                      turn_count += 1
        # 更新上一段的方向
        if dx1 != 0 or dy1 != 0: # 只有在实际移动时才更新方向
            dx0, dy0 = dx1, dy1
            
    return turn_count


def compute_smoothness(path: List[Tuple[int, int]]) -> float:
    """
    计算路径的平滑度，定义为所有关键点处转弯角度的总和（度）。
    角度总和越小，路径越平滑。

    Args:
        path (List[Tuple[int, int]]): 路径关键点的坐标列表 (row, col)。

    Returns:
        float: 平滑度指标 (角度总和)。
    """
    total_angle = 0.0
    if len(path) < 3:
        return 0.0

    def vector(p1, p2):
        """计算从 p1 指向 p2 的向量"""
        return (p2[0] - p1[0], p2[1] - p1[1])

    def angle_between(v1, v2):
        """计算两个向量之间的夹角（0-180度）"""
        dot = v1[0] * v2[0] + v1[1] * v2[1]
        mag1 = math.hypot(v1[0], v1[1])
        mag2 = math.hypot(v2[0], v2[1])
        if mag1 == 0 or mag2 == 0:
            return 0.0  # 零向量无法形成角度
        cos_theta = dot / (mag1 * mag2)
        # Clamp cos_theta to [-1, 1] due to potential floating point errors
        cos_theta = max(-1.0, min(1.0, cos_theta))
        angle_rad = math.acos(cos_theta)
        return math.degrees(angle_rad)

    for i in range(len(path) - 2):
        p0, p1, p2 = path[i], path[i+1], path[i+2]
        vec1 = vector(p0, p1)
        vec2 = vector(p1, p2)
        total_angle += angle_between(vec1, vec2)

    return total_angle

def compute_map_metric(path: List[Tuple[int, int]], value_map: np.ndarray) -> Tuple[float, float, float]:
    """
    计算路径在给定值地图上的指标（累计、最大、平均值）。
    内部使用 interpolate_path 获取路径上的所有栅格点。

    Args:
        path (List[Tuple[int, int]]): 路径关键点的坐标列表 (row, col)。
        value_map (np.ndarray): 包含相关值的地图（例如冰厚、风险）。

    Returns:
        Tuple[float, float, float]: (累计值, 最大值, 平均值)。如果路径无效或无有效点，则返回 (0, 0, 0)。
    """
    interpolated = interpolate_path(path)
    if not interpolated:
        return (0.0, 0.0, 0.0)

    height, width = value_map.shape[:2]
    values = []
    for r, c in interpolated:
        # 边界检查
        if 0 <= r < height and 0 <= c < width:
            values.append(value_map[r, c])
        # else: print(f"警告: 插值点 {(r, c)} 超出地图边界") # 可选警告

    if not values:
        return (0.0, 0.0, 0.0)

    total_value = float(np.sum(values))
    max_value = float(np.max(values))
    avg_value = float(np.mean(values))

    return (total_value, max_value, avg_value)

# =============================================================================
# 环境检查与可视化
# =============================================================================

def validate_environment(obstacle_map: np.ndarray, start: tuple, goal: tuple) -> bool:
    """
    检查起点终点是否在障碍物上，并进行简单的四向连通性检查。

    Args:
        obstacle_map (np.ndarray): 障碍物地图 (0=可通行, 非0=障碍)。
        start (tuple): 起点坐标 (row, col)。
        goal (tuple): 终点坐标 (row, col)。

    Returns:
        bool: 如果起点终点有效且存在四向连通路径，则返回 True，否则返回 False 或抛出 ValueError。

    Raises:
        ValueError: 如果起点或终点位于障碍物上。
    """
    height, width = obstacle_map.shape
    if not (0 <= start[0] < height and 0 <= start[1] < width and
            0 <= goal[0] < height and 0 <= goal[1] < width):
         raise ValueError("起点或终点坐标超出地图范围")

    if obstacle_map[start[0], start[1]] != 0:
        raise ValueError(f"起点 {start} 位于障碍物上")
    if obstacle_map[goal[0], goal[1]] != 0:
        raise ValueError(f"终点 {goal} 位于障碍物上")

    # 简单连通性检查 (BFS)
    queue = [start]
    visited = {start} # 使用集合提高查找效率

    while queue:
        r, c = queue.pop(0)

        if (r, c) == goal:
            return True # 找到目标

        for dr, dc in [(-1, 0), (1, 0), (0, -1), (0, 1)]: # 四个方向
            nr, nc = r + dr, c + dc

            # 检查边界、是否障碍物、是否已访问
            if (0 <= nr < height and 0 <= nc < width and
                    obstacle_map[nr, nc] == 0 and (nr, nc) not in visited):
                visited.add((nr, nc))
                queue.append((nr, nc))

    print("警告: 简单的四向连通性检查未能找到从起点到终点的路径。")
    return False


def plot_path(background_image: np.ndarray,
              path: Optional[List[Tuple[int, int]]],
              start: Tuple[int, int],
              end: Tuple[int, int],
              title: str = "Path"):
    """
    绘制路径搜索结果。

    Args:
        background_image (np.ndarray): 用于绘制背景的地图图像。
        path (Optional[List[Tuple[int, int]]]): 路径点的坐标列表 (row, col)。如果为 None 或空，则不绘制路径。
        start (Tuple[int, int]): 起点坐标 (row, col)。
        end (Tuple[int, int]): 终点坐标 (row, col)。
        title (str): 图形标题。
    """
    plt.figure(figsize=(8, 8), dpi=150) # 调整figsize和dpi以获得合适大小
    # 显示背景图像，origin='upper' 使 (0,0) 在左上角
    plt.imshow(background_image, cmap='gray', origin='upper', interpolation='nearest')

    if path:
        path_array = np.array(path)
        # 绘图时需要 (x, y) 坐标，即 (col, row)
        plt.plot(path_array[:, 1], path_array[:, 0], color='red', linewidth=2, marker='.', markersize=4, label='Path') # 调整标记大小

    # 绘制起点和终点
    plt.scatter(start[1], start[0], color='lime', s=150, marker='o', label='Start', edgecolors='black', zorder=5) # 调整颜色、大小、标记、边缘和层级
    plt.scatter(end[1], end[0], color='blue', s=150, marker='X', label='End', edgecolors='black', zorder=5) # 调整标记

    plt.title(title)
    plt.xlabel("列 (Column / X)") # 添加轴标签
    plt.ylabel("行 (Row / Y)")
    plt.legend(loc='upper right', bbox_to_anchor=(1.15, 1)) # 调整图例位置
    plt.grid(True, linestyle='--', alpha=0.6) # 调整网格样式
    plt.gca().invert_yaxis() # 通常图像的y轴向下，但这里origin='upper'处理了，所以可能不需要反转，取决于你的坐标习惯
    plt.axis('on') # 确保坐标轴可见
    plt.tight_layout() # 自动调整布局
    plt.show()

# 可以在此文件末尾添加一些简单的测试代码
if __name__ == '__main__':
    # 创建一个简单的测试地图和路径
    test_map = np.zeros((10, 10))
    test_map[1:3, 5] = 1 # 障碍
    test_map[5, 2:7] = 1
    start_pt = (0, 0)
    end_pt = (9, 9)
    # 假设一个路径 (row, col)
    test_path = [(0, 0), (1, 1), (1, 2), (1, 3), (1, 4), (2, 4), (3, 4), (4, 4), (4, 5), (4, 6), (4, 7), (5, 7), (6, 8), (7, 9), (8, 9), (9, 9)]

    print("--- 测试 path_metrics.py ---")

    try:
        connected = validate_environment(test_map, start_pt, end_pt)
        print(f"环境连通性检查: {connected}")
    except ValueError as e:
        print(f"环境检查失败: {e}")

    interp = interpolate_path(test_path)
    print(f"插值路径点数: {len(interp)}")
    # print(f"插值路径: {interp}")

    pix_len = compute_pixel_length(test_path)
    print(f"像素长度: {pix_len}")

    euc_len = compute_euclidean_length(test_path)
    print(f"欧氏长度: {euc_len:.2f}")

    turns = compute_turn_count(test_path)
    print(f"转弯次数: {turns}")

    smooth = compute_smoothness(test_path)
    print(f"平滑度 (角度和): {smooth:.2f}")

    # 假设 test_map 也代表风险或冰厚
    total, max_val, avg_val = compute_map_metric(test_path, test_map)
    print(f"地图指标 (基于障碍图): Total={total:.2f}, Max={max_val:.2f}, Avg={avg_val:.2f}")

    # 创建一个简单的风险图
    risk_map = np.random.rand(10, 10) * 0.5
    total_r, max_r, avg_r = compute_map_metric(test_path, risk_map)
    print(f"地图指标 (基于随机风险图): Total={total_r:.2f}, Max={max_r:.2f}, Avg={avg_r:.2f}")

    print("绘制路径...")
    plot_path(test_map, test_path, start_pt, end_pt, title="测试路径")