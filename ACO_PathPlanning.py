"""
修复后的蚁群优化算法用于路径规划
适配海冰分割路径规划系统
"""

import numpy as np
import random
import time
import math
from typing import List, Tuple, Optional, Dict, Any
import matplotlib.pyplot as plt

# 尝试导入基础类，如果不存在则定义简化版本
try:
    from path_planning_base import PathPlannerBase, PlanningResult, AlgorithmParams, PathPlanningUtils
except ImportError:
    # 定义简化的基础类
    class PathPlannerBase:
        def __init__(self, start, goal, grid_map, params=None):
            self.start = start
            self.goal = goal
            self.grid_map = np.array(grid_map)
            self.map_height, self.map_width = self.grid_map.shape
            self.params = params

    class PlanningResult:
        def __init__(self, path=None, success=False, cost=0.0, computation_time=0.0,
                     iterations=0, algorithm_name="", additional_data=None):
            self.path = path
            self.success = success
            self.cost = cost
            self.computation_time = computation_time
            self.iterations = iterations
            self.algorithm_name = algorithm_name
            self.additional_data = additional_data or {}

    class AlgorithmParams:
        def __init__(self, max_iterations=1000, step_size=1.0, goal_bias=0.1, timeout=30.0):
            self.max_iterations = max_iterations
            self.step_size = step_size
            self.goal_bias = goal_bias
            self.timeout = timeout
            self.algorithm_specific = {}

    class PathPlanningUtils:
        @staticmethod
        def distance(p1, p2):
            return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

        @staticmethod
        def is_valid_point(point, grid_map):
            row, col = point
            height, width = grid_map.shape
            if row < 0 or row >= height or col < 0 or col >= width:
                return False
            return grid_map[row, col] == 0

        @staticmethod
        def check_collision_line(start, end, grid_map, step_size=0.5):
            start_row, start_col = start
            end_row, end_col = end
            distance = np.sqrt((end_row - start_row)**2 + (end_col - start_col)**2)
            if distance == 0:
                return False
            num_steps = int(distance / step_size) + 1
            for i in range(num_steps + 1):
                t = i / num_steps if num_steps > 0 else 0
                check_row = int(start_row + t * (end_row - start_row))
                check_col = int(start_col + t * (end_col - start_col))
                if not PathPlanningUtils.is_valid_point((check_row, check_col), grid_map):
                    return True
            return False


class ACOPlanner(PathPlannerBase):
    """
    修复后的蚁群优化路径规划器

    主要改进：
    1. 简化信息素更新机制
    2. 改进路径构建策略
    3. 更好的收敛判断
    4. 统一的接口和数据结构
    """

    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int],
                 grid_map: np.ndarray, params: Optional[AlgorithmParams] = None):
        """
        初始化ACO规划器

        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图
            params: 算法参数
        """
        super().__init__(start, goal, grid_map, params)

        # ACO特定参数
        self.max_iter = params.max_iterations if params else 100
        self.num_ants = 20
        self.alpha = 1.0  # 信息素重要性
        self.beta = 2.0   # 启发式重要性
        self.evaporation_rate = 0.1
        self.q0 = 0.9     # 贪婪选择概率
        self.initial_pheromone = 1.0

        # 从算法特定参数中获取额外配置
        if params and params.algorithm_specific:
            self.max_iter = params.algorithm_specific.get('max_iterations', self.max_iter)
            self.num_ants = params.algorithm_specific.get('num_ants', self.num_ants)
            self.alpha = params.algorithm_specific.get('alpha', self.alpha)
            self.beta = params.algorithm_specific.get('beta', self.beta)
            self.evaporation_rate = params.algorithm_specific.get('evaporation_rate', self.evaporation_rate)
            self.q0 = params.algorithm_specific.get('q0', self.q0)

        # 初始化信息素矩阵
        self.pheromone = np.full((self.map_height, self.map_width), self.initial_pheromone)

        # 初始化启发式信息矩阵（距离目标的倒数）
        self.heuristic = self._initialize_heuristic()

        # 最优解跟踪
        self.best_path = None
        self.best_cost = float('inf')

        # 统计信息
        self.convergence_history = []

    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return "ACO"

    def _initialize_heuristic(self) -> np.ndarray:
        """初始化启发式信息矩阵"""
        heuristic = np.zeros((self.map_height, self.map_width))

        for row in range(self.map_height):
            for col in range(self.map_width):
                if self.grid_map[row, col] == 0:  # 可通行区域
                    distance = PathPlanningUtils.distance((row, col), self.goal)
                    heuristic[row, col] = 1.0 / (distance + 1e-6)
                else:
                    heuristic[row, col] = 0.0  # 障碍物区域

        return heuristic

    def plan(self) -> PlanningResult:
        """
        执行ACO路径规划

        Returns:
            PlanningResult: 规划结果
        """
        self.start_time = time.time()
        self.iterations = 0

        try:
            for iteration in range(self.max_iter):
                self.iterations = iteration + 1

                # 检查超时
                if time.time() - self.start_time > self.params.timeout:
                    break

                # 构建蚂蚁路径
                iteration_best_path = None
                iteration_best_cost = float('inf')

                for ant_id in range(self.num_ants):
                    path = self._construct_ant_path(self.start)

                    if path and len(path) > 1:
                        # 检查路径是否到达目标
                        if self._path_reaches_goal(path):
                            cost = self._calculate_path_cost(path)

                            if cost < iteration_best_cost:
                                iteration_best_cost = cost
                                iteration_best_path = path

                # 更新全局最优
                if iteration_best_path and iteration_best_cost < self.best_cost:
                    self.best_cost = iteration_best_cost
                    self.best_path = iteration_best_path

                # 更新信息素
                self._update_pheromone(iteration_best_path)

                # 记录收敛历史
                self.convergence_history.append(self.best_cost)

                # 早期停止判断
                if (self.best_path and iteration > 50 and
                    iteration % 20 == 0 and len(self.convergence_history) >= 20):
                    recent_improvement = (self.convergence_history[-20] - self.best_cost) / self.convergence_history[-20]
                    if recent_improvement < 0.01:  # 改进小于1%
                        break

            # 构建结果
            computation_time = time.time() - self.start_time

            if self.best_path:
                return PlanningResult(
                    path=self.best_path,
                    success=True,
                    cost=self.best_cost,
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={'convergence_history': self.convergence_history}
                )
            else:
                return PlanningResult(
                    path=None,
                    success=False,
                    cost=float('inf'),
                    computation_time=computation_time,
                    iterations=self.iterations,
                    algorithm_name=self.get_algorithm_name(),
                    additional_data={'convergence_history': self.convergence_history}
                )

        except Exception as e:
            computation_time = time.time() - self.start_time if self.start_time else 0.0
            return PlanningResult(
                path=None,
                success=False,
                cost=float('inf'),
                computation_time=computation_time,
                iterations=self.iterations,
                algorithm_name=self.get_algorithm_name(),
                additional_data={'error': str(e)}
            )

    def _construct_ant_path(self, ant_start_pos: Tuple[int, int]) -> Optional[List[Tuple[int, int]]]:
        """
        构建单只蚂蚁的路径

        Args:
            ant_start_pos: 蚂蚁起始位置

        Returns:
            Optional[List]: 路径，如果构建失败返回None
        """
        path = [ant_start_pos]
        current_pos = ant_start_pos
        visited = set([ant_start_pos])
        max_steps = self.map_height * self.map_width  # 防止无限循环

        for step in range(max_steps):
            # 检查是否到达目标
            if PathPlanningUtils.distance(current_pos, self.goal) <= 1.5:
                path.append(self.goal)
                return path

            # 获取候选下一步位置
            candidates = self._get_valid_neighbors(current_pos, visited)

            if not candidates:
                # 如果没有候选位置，尝试回退或随机移动
                if len(path) > 1:
                    path.pop()
                    current_pos = path[-1]
                    visited.remove(current_pos)
                    continue
                else:
                    break  # 无法继续

            # 选择下一个位置
            next_pos = self._select_next_position(current_pos, candidates)

            if next_pos is None:
                break

            path.append(next_pos)
            current_pos = next_pos
            visited.add(next_pos)

        return path if len(path) > 1 else None

    def _get_valid_neighbors(self, position: Tuple[int, int],
                           visited: set) -> List[Tuple[int, int]]:
        """
        获取有效的邻居位置

        Args:
            position: 当前位置
            visited: 已访问位置集合

        Returns:
            List: 有效邻居列表
        """
        neighbors = []
        row, col = position

        # 8个方向的移动
        directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1),
                     (0, 1), (1, -1), (1, 0), (1, 1)]

        for dr, dc in directions:
            new_row, new_col = row + dr, col + dc
            new_pos = (new_row, new_col)

            # 检查边界和障碍物
            if (0 <= new_row < self.map_height and
                0 <= new_col < self.map_width and
                self.grid_map[new_row, new_col] == 0 and
                new_pos not in visited):
                neighbors.append(new_pos)

        return neighbors

    def _select_next_position(self, current_pos: Tuple[int, int],
                            candidates: List[Tuple[int, int]]) -> Optional[Tuple[int, int]]:
        """
        基于ACO规则选择下一个位置

        Args:
            current_pos: 当前位置
            candidates: 候选位置列表

        Returns:
            Optional[Tuple]: 选择的位置，如果无法选择返回None
        """
        if not candidates:
            return None

        # 计算每个候选位置的吸引力
        attractions = []
        for pos in candidates:
            row, col = pos

            # 信息素强度
            pheromone_strength = self.pheromone[row, col]

            # 启发式信息
            heuristic_value = self.heuristic[row, col]

            # 计算吸引力
            attraction = (pheromone_strength ** self.alpha) * (heuristic_value ** self.beta)
            attractions.append(attraction)

        attractions = np.array(attractions)

        # 避免所有吸引力为0的情况
        if np.sum(attractions) == 0:
            return random.choice(candidates)

        # 贪婪选择 vs 概率选择
        if random.random() < self.q0:
            # 贪婪选择：选择吸引力最大的位置
            best_idx = np.argmax(attractions)
            return candidates[best_idx]
        else:
            # 概率选择：根据吸引力分布随机选择
            probabilities = attractions / np.sum(attractions)
            selected_idx = np.random.choice(len(candidates), p=probabilities)
            return candidates[selected_idx]

    def _path_reaches_goal(self, path: List[Tuple[int, int]]) -> bool:
        """检查路径是否到达目标"""
        if not path:
            return False

        last_pos = path[-1]
        return (last_pos == self.goal or
                PathPlanningUtils.distance(last_pos, self.goal) <= 1.0)

    def _calculate_path_cost(self, path: List[Tuple[int, int]]) -> float:
        """计算路径代价"""
        if not path or len(path) < 2:
            return float('inf')

        total_cost = 0.0
        for i in range(len(path) - 1):
            total_cost += PathPlanningUtils.distance(path[i], path[i + 1])

        return total_cost

    def _update_pheromone(self, best_path: Optional[List[Tuple[int, int]]]):
        """更新信息素"""
        # 信息素蒸发
        self.pheromone *= (1 - self.evaporation_rate)

        # 如果有最优路径，增强其信息素
        if best_path and len(best_path) > 1:
            pheromone_deposit = 1.0 / self.best_cost

            for i in range(len(best_path) - 1):
                row, col = best_path[i]
                if 0 <= row < self.map_height and 0 <= col < self.map_width:
                    self.pheromone[row, col] += pheromone_deposit


def create_aco_params(max_iterations: int = 100, num_ants: int = 20,
                     alpha: float = 1.0, beta: float = 2.0,
                     evaporation_rate: float = 0.1, q0: float = 0.9,
                     timeout: float = 30.0) -> AlgorithmParams:
    """
    创建ACO算法参数

    Args:
        max_iterations: 最大迭代次数
        num_ants: 蚂蚁数量
        alpha: 信息素重要性因子
        beta: 启发式重要性因子
        evaporation_rate: 信息素蒸发率
        q0: 贪婪选择概率
        timeout: 超时时间

    Returns:
        AlgorithmParams: 算法参数
    """
    params = AlgorithmParams(max_iterations, 1.0, 0.1, timeout)
    params.algorithm_specific = {
        'max_iterations': max_iterations,
        'num_ants': num_ants,
        'alpha': alpha,
        'beta': beta,
        'evaporation_rate': evaporation_rate,
        'q0': q0
    }
    return params


def test_aco_planner():
    """测试ACO规划器"""
    print("🧪 测试ACO规划器...")

    # 创建测试环境
    grid_map = np.zeros((20, 20))
    grid_map[5:15, 8] = 1  # 添加障碍物
    grid_map[10, 5:15] = 1

    start = (1, 1)
    goal = (18, 18)

    print(f"测试环境: {grid_map.shape}, 起点: {start}, 终点: {goal}")

    # 创建ACO规划器
    params = create_aco_params(
        max_iterations=100,
        num_ants=15,
        alpha=1.0,
        beta=2.0,
        evaporation_rate=0.1,
        q0=0.9
    )
    planner = ACOPlanner(start, goal, grid_map, params)

    # 执行规划
    result = planner.plan()

    # 输出结果
    print(f"✅ ACO规划完成:")
    print(f"   成功: {result.success}")
    print(f"   路径长度: {len(result.path) if result.path else 0}")
    print(f"   代价: {result.cost:.2f}")
    print(f"   时间: {result.computation_time:.3f}s")
    print(f"   迭代次数: {result.iterations}")

    if result.success and result.path:
        print(f"   起点: {result.path[0]}")
        print(f"   终点: {result.path[-1]}")

    return result


if __name__ == "__main__":
    test_aco_planner()
