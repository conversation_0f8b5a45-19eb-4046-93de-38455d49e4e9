import cv2
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from skimage.segmentation import random_walker
from skimage.filters import gaussian, threshold_otsu
from skimage.measure import label, regionprops_table
import pandas as pd
from skimage.measure import regionprops
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler
from scipy.ndimage import binary_dilation
import matplotlib.pyplot as plt
import math
# ===================== Step 0: 读取图像 =====================
# 读取输入图像
# 根据需要修改图像路径
image = cv2.imread("sea_ice_593.jpg")  # 替换为你的图像路径
# 检查图像是否成功读取
if image is None:
    raise ValueError("图像加载失败，请检查路径是否正确！")
######=====================A.图像预处理 =====================####### 
# ===================== Step 1: 转为灰度图像 =====================
# 转换为灰度图像，简化计算，突出亮度差异，适用于大多数分割算法
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
# ===================== Step 2: 图像平滑处理（滤波降噪） =====================
# 高斯滤波平滑图像，减少高频噪声，保持边缘信息
smoothed_image = gaussian(gray_image, sigma=1)
# 中值滤波进一步去除椒盐噪声
median_image = cv2.medianBlur((smoothed_image * 255).astype(np.uint8), 5)
# ===================== Step 3: 增强对比度 =====================
# 使用直方图均衡化增强对比度，突出图像细节
# enhanced_image = cv2.equalizeHist(median_image)
# 自适应直方图均衡化（CLAHE），避免过度增强，适用于局部对比度提升
clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
enhanced_image = clahe.apply(median_image)
# ===================== Step 4: 边缘增强与纹理特征提取 =====================
# Canny 边缘检测，提取海冰与水体的边缘特征
edges = cv2.Canny(enhanced_image, threshold1=50, threshold2=150)
# 使用拉普拉斯滤波器进一步增强边缘，突出细节变化
# laplacian_edges = cv2.Laplacian(enhanced_image, cv2.CV_64F)
# ===================== Step 5: 可视化结果 =====================
# 显示原始图像和处理后的图像
plt.figure(figsize=(12, 8))
plt.subplot(2, 3, 1)
plt.title("Original Image")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.axis('off')
plt.subplot(2, 3, 2)
plt.title("Grayscale Image")
plt.imshow(gray_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 3, 3)
plt.title("Smoothed Image")
plt.imshow(median_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 3, 4)
plt.title("median_image")
plt.imshow(median_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 3, 5)
plt.title("Contrast Enhanced")
plt.imshow(enhanced_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 3, 6)
plt.title("Canny Edges")
plt.imshow(edges, cmap='gray')
plt.axis('off')
plt.tight_layout()
plt.show()
# ===================== 3D 可视化灰度图像 =====================
h, w = gray_image.shape
x = np.linspace(0, w, w)
y = np.linspace(0, h, h)
x, y = np.meshgrid(x, y)  # 生成网格坐标
z = gray_image  # 将灰度值作为 z 高度
z = enhanced_image  # 将灰度值作为 z 高度
# 创建 3D 绘图
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')
# 绘制曲面
surface = ax.plot_surface(x, y, z, cmap="gray", edgecolor='none')
# 添加颜色条
fig.colorbar(surface, ax=ax, shrink=0.5, aspect=10)
# 设置标题和轴标签
ax.set_title("3D Visualization of Grayscale Image")
ax.set_xlabel("X-axis")
ax.set_ylabel("Y-axis")
ax.set_zlabel("Pixel Intensity")
plt.show()
######=====================B.图像分割 =====================####### 
image_seg=enhanced_image
# ===================== 方法 1: 应用 Otsu 阈值分割 =====================
# 使用 Otsu 方法自动确定阈值进行二值化
_, otsu_thresh = cv2.threshold((image_seg * 255).astype(np.uint8), 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
# ===================== 方法 2: 应用 Gabor 滤波器 + K-Means 聚类分割=====================
def apply_gabor_filter(image, ksize=4, sigma=3.0, theta=np.pi/4, lambd=10.0, gamma=0.5):
    """
    应用 Gabor 滤波器来提取特定方向和频率的纹理特征。
    参数：
        image: 输入灰度图像。
        ksize: Gabor 核的大小。
        sigma: Gabor 核的高斯标准差。
        theta: 滤波器方向（弧度）。
        lambd: 波长。
        gamma: 空间纵横比。
    返回：
        filtered_image: 滤波后的图像。
    """
    gabor_kernel = cv2.getGaborKernel((ksize, ksize), sigma, theta, lambd, gamma, 0, ktype=cv2.CV_32F)
    filtered_image = cv2.filter2D(image, cv2.CV_8UC3, gabor_kernel)
    return filtered_image
# 应用 Gabor 滤波器
filtered_image = apply_gabor_filter(image_seg)
# 将 Gabor 滤波后的图像展平为二维数组（因为是灰度图像，只有一个通道）
pixel_values = image_seg.reshape((-1, 1))
#pixel_values = filtered_image.reshape((-1, 1))
pixel_values = np.float32(pixel_values)  # 转为浮点型，便于 K-Means 处理
# 定义 K-Means 聚类参数
criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 100, 0.2)  # 迭代停止条件
k = 3  # 目标分为两类（海冰和水）
_, labels, centers = cv2.kmeans(pixel_values, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
# 将聚类结果映射回图像形状
segmented_image_kmeans = labels.reshape(filtered_image.shape)
# ===================== 方法 3: 阈值分割（固定阈值） =====================
_, fixed_thresh = cv2.threshold(image_seg, 127, 255, cv2.THRESH_BINARY)
# ===================== 方法 4: 随机游走方法 =====================
# 初始化标记矩阵，0 表示未标记的像素
markers = np.zeros_like(image_seg, dtype=np.int32)
# 手动设置前景（海冰）和背景（水）的标记
markers[image_seg < 100] = 1  # 背景标记
markers[image_seg > 150] = 2  # 前景标记
# 使用随机游走算法对图像进行分割
labels_rw = random_walker(smoothed_image, markers, beta=10, mode='bf')
# ===================== 最终可视化对比 =====================
plt.figure(figsize=(18, 12))

# 原始图像
plt.subplot(2, 3, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.title("Original Image")
plt.axis("off")

# Otsu 阈值分割
plt.subplot(2, 3, 2)
plt.imshow(otsu_thresh, cmap="gray")
plt.title("Otsu Thresholding")
plt.axis("off")

# Gabor 滤波器 + K-Means 分割
plt.subplot(2, 3, 3)
plt.imshow(segmented_image_kmeans, cmap="jet")
plt.title("Gabor + K-Means")
plt.axis("off")

# 固定阈值分割
plt.subplot(2, 3, 4)
plt.imshow(fixed_thresh, cmap="gray")
plt.title("Fixed Threshold")
plt.axis("off")

# 随机游走分割
plt.subplot(2, 3, 5)
plt.imshow(labels_rw, cmap="gray")
plt.title("Random Walker")
plt.axis("off")
plt.tight_layout()
plt.show()
# ===================== 可视化随机游走分割结果 =====================
plt.figure(figsize=(15, 10))
# 原始灰度图像
plt.subplot(1, 3, 1)
plt.imshow(gray_image, cmap='gray')
plt.title("Original Grayscale Image")
plt.axis("off")
# 标记结果
plt.subplot(1, 3, 2)
plt.imshow(markers, cmap='jet')
plt.title("Markers")
plt.axis("off")
# 随机游走分割结果
plt.subplot(1, 3, 3)
plt.imshow(labels_rw, cmap='gray')
plt.title("Segmented Image (Random Walker)")
plt.axis("off")
plt.tight_layout()
plt.show()

# =====================高级统计分析功能=====================
ice_image=fixed_thresh
from scipy import ndimage
import matplotlib.patches as patches
#使用分割得到的标签图，每个连通区域可以视为一块独立的海冰。
# 识别连通区域,进行单一的颜色识别
labeled_ice, num_features = ndimage.label(ice_image)

# 可视化每块海冰
plt.figure(figsize=(10, 8))
plt.imshow(labeled_ice, cmap='nipy_spectral')
plt.title(f"Identified Sea Ice Blocks: {num_features}")
plt.axis("off")
plt.show()

# 创建图像
fig, ax = plt.subplots(figsize=(10, 8))
ax.imshow(labeled_ice, cmap='nipy_spectral')
ax.set_title(f"Identified Sea Ice Blocks: {num_features}")
ax.axis("off")

# 识别每个连通区域的边界框
objects = ndimage.find_objects(labeled_ice)
# 绘制红色方框标记每块海冰
for obj_slice in objects:
    if obj_slice is not None:
        y_start, y_stop = obj_slice[0].start, obj_slice[0].stop
        x_start, x_stop = obj_slice[1].start, obj_slice[1].stop
        width = x_stop - x_start
        height = y_stop - y_start
        # 添加红色方框
        rect = patches.Rectangle((x_start, y_start), width, height, 
                                 linewidth=2, edgecolor='red', facecolor='none')
        ax.add_patch(rect)
plt.show()

#2.1 海冰的膨胀安全距离

#定义膨胀操作以模拟 "充气" 海冰，保证安全距离
safety_margin = 1  # 安全边距
inflated_ice = binary_dilation(fixed_thresh, iterations=safety_margin)

#创建加权图像，初步为空白区域（0值）和冰厚区域（根据 gray_image 加权）
weighted_image = np.copy(gray_image)
ice_image = np.copy(gray_image)
ice_image[inflated_ice == 1] = 1  # 膨胀区域赋予最大冰厚值
ice_image[inflated_ice == 0] = 0  # 其他区域保留原冰厚值

#设置膨胀区域为障碍物，其他空白区域根据冰厚值加权
weighted_image[inflated_ice == 1] = 1  # 膨胀区域赋予最大冰厚值
weighted_image[inflated_ice == 0] = gray_image[inflated_ice == 0]  # 其他区域保留原冰厚值

# 可视化
plt.figure(figsize=(8, 6))
plt.imshow(weighted_image, cmap='jet', origin='upper')
plt.axis('off')
plt.show()

#2.1 海冰厚度的估计（基于灰度特征）
#厚度估算改进： 如果有真实厚度数据，可以通过回归模型建立更精准的灰度-厚度映射。
#假设灰度值与厚度呈一定的物理关系，可以通过以下方式估算：
# 假设 gray_image 是你的冰厚图像
scaler = MinMaxScaler()
# 将 gray_image 扁平化为一维数组，MinMaxScaler 要求二维数据
gray_image_flattened = weighted_image.flatten().reshape(-1, 1)
# 对数据进行归一化
normalized_gray_image_flattened = scaler.fit_transform(gray_image_flattened)
# 将归一化后的图像重新调整为原来的形状
normalized_image = normalized_gray_image_flattened.reshape(gray_image.shape)

plt.figure(figsize=(8, 6))
plt.imshow(normalized_image, cmap='jet', origin='upper')
plt.axis('off')
plt.show()

# 计算每块海冰的平均灰度值（作为厚度的近似）
thickness_list = ndimage.mean(normalized_image, labels=labeled_ice, index=range(1, num_features + 1))
# 输出厚度信息
for i, thickness in enumerate(thickness_list, 1):
    print(f"Sea Ice Block {i}: Estimated Thickness = {thickness:.2f}")
    
#2.2 图片的冰区覆盖率
ice_area = np.sum(ice_image > 0)  # 冰层区域的面积
total_area = ice_image.size  # 图像的总面积
coverage = (ice_area / total_area) * 100  # 覆盖率（百分比）
print(f"Ice Coverage: {coverage:.2f}%")

#2.3 图片的冰区密集度
# 假设图像被划分为网格，例如每个网格为10x10像素
grid_size = 20
grid_rows = gray_image.shape[0] // grid_size
grid_cols = gray_image.shape[1] // grid_size

#2.3 冰层覆盖率
density_map = np.zeros((grid_rows, grid_cols))
for row in range(grid_rows):
    for col in range(grid_cols):
        # 提取当前网格区域
        grid_area = ice_image[row * grid_size:(row + 1) * grid_size, col * grid_size:(col + 1) * grid_size]
        # 计算当前网格的冰层覆盖率
        ice_in_grid = np.sum(grid_area > 0)
        total_grid_area = grid_area.size
        density_map[row, col] = (ice_in_grid / total_grid_area) * 100  # 覆盖率百分比

# 可视化冰层分布密度
plt.figure(figsize=(8, 6))
plt.imshow(density_map, cmap='jet', interpolation='nearest')
plt.colorbar(label='Ice Coverage (%)')
plt.title('Ice Distribution Density')
plt.axis('off')
plt.show()

import numpy as np
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
# 计算缩放比例
# 原网格大小为 (10, 10)，目标大小为 (517, 518)
zoom_factor_row = 517 / 10
zoom_factor_col = 518 / 10
# 使用 zoom 函数进行插值，order=1 表示使用双线性插值（也可以选择 order=3 等）
density_map_resized = zoom(density_map, (zoom_factor_row, zoom_factor_col), order=1)
# 检查新数组的形状
print("新网格大小：", density_map_resized.shape)
# 可视化原始与插值后的网格
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.imshow(density_map, cmap='viridis', interpolation='none')
plt.title("原始 10×10 网格")
plt.colorbar()
plt.subplot(1, 2, 2)
plt.imshow(density_map_resized, cmap='viridis', interpolation='none')
plt.title("插值后 517×518 网格")
plt.colorbar()
plt.show()

# 3. 海冰大小的统计（面积和形状特征）
# 可以通过计算每块海冰的像素数量来估算面积：
# 计算每块海冰的面积（像素数）
area_list = ndimage.sum(np.ones_like(gray_image), labels=labeled_ice, index=range(1, num_features + 1))

# 输出面积信息
for i, area in enumerate(area_list, 1):
    print(f"Sea Ice Block {i}: Area = {area} pixels")
# 计算形状特征
props = regionprops(labeled_ice, intensity_image=gray_image)
for i, prop in enumerate(props, 1):
    print(f"Sea Ice Block {i}: Area = {prop.area}, Perimeter = {prop.perimeter}, Eccentricity = {prop.eccentricity}")

# 厚度分布
plt.figure(figsize=(8, 4))
sns.histplot(thickness_list, kde=True)
plt.title("Distribution of Sea Ice Thickness")
plt.xlabel("Estimated Thickness")
plt.ylabel("Frequency")
plt.show()

# 面积分布
plt.figure(figsize=(8, 4))
sns.histplot(area_list, kde=True, color="orange")
plt.title("Distribution of Sea Ice Area")
plt.xlabel("Area (pixels)")
plt.ylabel("Frequency")
plt.show()

# 4. 单一海冰的特征
#关联labeled_ice和厚度，形状特征，可以选择绘制某个海冰到一个图中，并展示相关信息进行绘制
# 假设你已经有了 labeled_ice 和 thickness_list、props 等信息
# 选择你想要显示的海冰块，例如选择海冰块1
target_block = 20
# 创建一个掩膜，仅保留目标海冰块
target_mask = labeled_ice == target_block
# 可视化该目标海冰块（白色）
plt.figure(figsize=(10, 8))
plt.imshow(target_mask, cmap='gray')
plt.title(f"Sea Ice Block {target_block}: Thickness = {thickness_list[target_block - 1]:.2f}")
plt.axis("off")
plt.show()
# 输出该目标海冰块的相关形状特征
target_prop = props[target_block - 1]
print(f"Sea Ice Block {target_block}:")
print(f"Estimated Thickness = {thickness_list[target_block - 1]:.2f}")
print(f"Area = {target_prop.area} pixels")
print(f"Perimeter = {target_prop.perimeter:.2f}")
print(f"Eccentricity = {target_prop.eccentricity:.2f}")


# ===================== 路径规划功能 =====================
import gym
from gym import spaces
import numpy as np
import heapq
import math
import sys
import matplotlib.pyplot as plt
from typing import Any, List, Tuple, Dict, Optional

# -------------------------------
# 参数设置：各项指标的权重（新目标函数版本）
weights = {
    'length': 5.0,    # 每步移动消耗
    'turn': 1.0,      # 转弯惩罚
    'smooth': 1.0,    # 平滑性惩罚（转弯附加惩罚）
    'feasible': 1.0,  # 冰厚安全性惩罚（冰越厚惩罚越高）
    'risk': 1.0       # 海冰风险惩罚（风险越高惩罚越高）
}

# =============================================================================
# =============================================================================
# A.图搜索算法通常将工作空间离散化为网格或图，然后在图中进行搜索，寻找从起点到终点的最优路径
# =============================================================================
# Dijkstra 算法：
# Dijkstra 算法是一种基于广度优先搜索的算法，通过逐步扩展节点，找出从起点到所有节点的最短路径，进而求出最短路径解。
# 优点：
# 最优性：在边权为非负的图中能保证找到全局最优路径。
# 完整性：只要存在一条可达路径，算法就能找到。
# 缺点：
# 计算量大：在大规模图或高分辨率网格中，搜索节点数激增，计算时间长。
# 没有启发信息：对目标信息没有利用，效率相对较低。
from DijkstraClassic import DijkstraClassic
# =============================================================================
# A* 算法：
# 简介：A* 算法在 Dijkstra 算法的基础上引入启发式函数（Heuristic），使搜索朝着目标方向引导，减少不必要的节点扩展。
# 优点：
# 高效性：合理的启发式函数能大幅缩小搜索空间，提高速度。
# 最优性和完整性：当启发函数满足一致性条件时，A* 能保证找到全局最优路径。
# 缺点：
# 启发函数依赖：启发函数的设计直接影响性能；设计不当可能导致效率下降甚至失去最优性保证。
# 内存消耗大：需要保存开放列表和闭合列表，对于高分辨率问题可能内存开销较大。
from AstarClassic import AstarClassic
# 新的目标函数
from AdvancedAStar import AdvancedAStar
from AdvancedDijkstra import AdvancedDijkstra
# =============================================================================
# Theta* 算法：经典版本（基于网格障碍物）
from ThetastarClassic import ThetastarClassic
# =============================================================================
# B.采样（随机）基方法
# 采样方法不对整个空间进行离散化，而是在连续空间中随机采样生成点，然后构造连接图，再在图上搜索路径。这类方法在高维空间中具有较好的扩展性。
# =============================================================================
# RRT 算法：经典版本（快速探索随机树）
# 简介：RRT 算法从起点开始，通过随机采样空间，并以采样点为目标逐步扩展树，快速覆盖整个搜索空间，直至连接到目标区域。
# 优点：
# 探索效率高：能快速扩展到大部分自由空间，适合高维、复杂约束问题。
# 简单易实现：算法思想直观，易于编码实现。
# 缺点：
# 路径不最优：生成的路径通常较长且拐弯较多，不平滑。
# 后处理需求：常需进行路径平滑和优化以满足实际应用要求。
from RRT import RRT
# =============================================================================
# C.仿生智能算法
# =============================================================================
# 蚁群算法（Ant Colony Optimization）
from ACOPathPlanner import ACOPathPlanner
# =============================================================================
# D.基于势场的方法
# =============================================================================
from PotentialFieldPlanner import PotentialFieldPlanner

# =============================================================================
# 指标计算函数
def compute_path_length(path):
    return len(path) - 1 if path else 0

def compute_turn_count(path):
    turn_count = 0
    if len(path) < 3:
        return 0
    prev_move = (path[1][0] - path[0][0], path[1][1] - path[0][1])
    for i in range(1, len(path)-1):
        curr_move = (path[i+1][0] - path[i][0], path[i+1][1] - path[i][1])
        if curr_move != prev_move:
            turn_count += 1
        prev_move = curr_move
    return turn_count

def compute_smoothness(path):
    """
    计算路径的平滑度，基于路径中相邻向量之间的夹角。
    平滑度 = 所有转角度数的总和。
    参数：
        path (list of tuple): 路径点的坐标列表，如 [(x1, y1), (x2, y2), ...]

    返回：
        float: 平滑度，角度总和（单位：度）
    """
    smoothness = 0
    if len(path) < 3:
        return 0  # 不足以形成转弯
    def vector(p1, p2):
        return (p2[0] - p1[0], p2[1] - p1[1])
    def angle_between(v1, v2):
        # 计算两个向量之间的夹角（单位：度）
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]
        magnitude_v1 = math.hypot(v1[0], v1[1])
        magnitude_v2 = math.hypot(v2[0], v2[1])
        if magnitude_v1 == 0 or magnitude_v2 == 0:
            return 0  # 避免除以零     
        # 计算余弦值，防止浮点数误差导致超出[-1, 1]范围
        cos_theta = max(min(dot_product / (magnitude_v1 * magnitude_v2), 1), -1)
        angle_rad = math.acos(cos_theta)
        return math.degrees(angle_rad)
    for i in range(1, len(path) - 1):
        v1 = vector(path[i - 1], path[i])
        v2 = vector(path[i], path[i + 1])
        smoothness += angle_between(v1, v2)
    return smoothness

def compute_feasibility(path, normalized_image):
    return sum(1 - normalized_image[pos[0], pos[1]] for pos in path) if path else 0

def compute_risk(path, density_map):
    return sum(density_map[pos[0], pos[1]] for pos in path) if path else 0
# =============================================================================
# 绘制路径函数
def plot_path(ice_image, path, start, goal, title="Path"):
    plt.figure(figsize=(8, 8))
    plt.imshow(1 - ice_image, cmap='gray', interpolation='none')
    if path:
        path_arr = np.array(path)
        plt.plot(path_arr[:, 1], path_arr[:, 0], 'r-', linewidth=2)
    plt.scatter(start[1], start[0], c='green', s=100, marker='o', label='Start')
    plt.scatter(goal[1], goal[0], c='blue', s=100, marker='x', label='Goal')
    plt.title(title)
    plt.legend()
    plt.gca().invert_yaxis()  # 保持y轴方向与数组一致
    plt.show()
    
 # 在调用算法前添加环境检查
def validate_environment(ice_image, start, goal):
    # 检查起点终点是否可达
    if ice_image[start] != 0 or ice_image[goal] != 0:
        raise ValueError("起点或终点位于障碍物上")
    
    # 简单连通性检查
    tmp_env = ice_image.copy()
    queue = [start]
    tmp_env[start] = 2  # 标记已访问
    while queue:
        x, y = queue.pop(0)
        for dx, dy in [(-1,0),(1,0),(0,-1),(0,1)]:
            nx, ny = x+dx, y+dy
            if 0<=nx<tmp_env.shape[0] and 0<=ny<tmp_env.shape[1]:
                if tmp_env[nx, ny] == 0:
                    if (nx, ny) == goal:
                        return True
                    tmp_env[nx, ny] = 2
                    queue.append((nx, ny))
    return False   
# =============================================================================
# 测试主函数
if __name__ == '__main__':
    # 设置起点和终点（这里起点设为右下角，终点设为左上角）
    start = (ice_image.shape[0] - 1, ice_image.shape[1] - 1)
    goal = (0, 0)
    if not validate_environment(ice_image, start, goal):
        print("错误：起点和终点之间没有连通路径！")
        sys.exit(1)

    # ----------------------------
    # 经典版本
    print("【A* 算法 - 经典版本】开始路径规划...")
    astar_classic = AstarClassic(start, goal, ice_image)
    astar_classic.search()
    astar_classic_path = astar_classic.get_path()
    if astar_classic_path:
        print("路径找到，步数：", compute_path_length(astar_classic_path))
        plot_path(ice_image, astar_classic_path, start, goal, title="A* Classic")
    else:
        print("未找到路径！")

    print("【Dijkstra 算法 - 经典版本】开始路径规划...")
    dijkstra_classic = DijkstraClassic(start, goal, ice_image)
    dijkstra_classic.search()
    dijkstra_classic_path = dijkstra_classic.get_path()
    if dijkstra_classic_path:
        print("路径找到，步数：", compute_path_length(dijkstra_classic_path))
        plot_path(ice_image, dijkstra_classic_path, start, goal, title="Dijkstra Classic")
    else:
        print("未找到路径！")
        
    # ----------------------------
    # 新目标函数版本
    print("【A* 算法 - 新目标函数版本】开始路径规划...")
    astar_new_path = AdvancedAStar(ice_image, normalized_image, density_map_resized, start, goal, weights)
    if astar_new_path:
        print("路径找到，步数：", compute_path_length(astar_new_path))
        plot_path(ice_image, astar_new_path, start, goal, title="A* New Objective Function")
    else:
        print("未找到路径！")

    print("【Dijkstra 算法 - 新目标函数版本】开始路径规划...")
    dijkstra_new_path = AdvancedDijkstra(ice_image, normalized_image, density_map_resized, start, goal, weights)
    if dijkstra_new_path:
        print("路径找到，步数：", compute_path_length(dijkstra_new_path))
        plot_path(ice_image, dijkstra_new_path, start, goal, title="Dijkstra New Objective Function")
    else:
        print("未找到路径！")

     # 新增算法测试
    print("\n【RRT 算法】开始路径规划...")
    rrt = RRT(start, goal, ice_image)
    found = rrt.search()
    rrt_path = rrt.get_path()
    if rrt_path:
        print("路径找到，步数:", compute_path_length(rrt_path))
        plot_path(ice_image, rrt_path, start, goal, title="RRT Path")
    else:
        print("未找到路径!")

    print("\n【Theta* 算法】开始路径规划...")
    theta_star_path = ThetastarClassic(ice_image, start, goal)
    if theta_star_path:
        print("路径找到，步数:", compute_path_length(theta_star_path))
        plot_path(ice_image, theta_star_path, start, goal, title="Theta* Path")
    else:
        print("未找到路径!")

    # 新增算法测试
    print("\n【蚁群算法】开始路径规划...")
    aco = ACOPathPlanner(ice_image, start, goal)
    aco_path = aco.search()
    if aco_path:
        print("路径找到，步数:", compute_path_length(aco_path))
        plot_path(ice_image, aco_path, start, goal, title="ACO Path")
    else:
        print("未找到路径!")
        
    print("\n【势场法】开始路径规划...")
    pf = PotentialFieldPlanner(ice_image, start, goal)
    pf_path = pf.search()
    if pf_path:
        print("路径找到，步数:", compute_path_length(pf_path))
        plot_path(ice_image, pf_path, start, goal, title="Potential Field Path")
    else:
        print("未找到路径!")
        
    # ----------------------------
    # 对比各算法的路径指标
    methods = {
        "A* New": astar_new_path,
        "Dijkstra New": dijkstra_new_path,
        "A* Classic": astar_classic_path,
        "Dijkstra Classic": dijkstra_classic_path,
        "RRT": rrt_path,
        "Theta*": theta_star_path,
        "ACO": aco_path,
        "Potential Field": pf_path
    }
    for method, path in methods.items():
        if path:
            print(f"\n【{method}】路径指标：")
            print(f"  路径长度（步数）: {compute_path_length(path)}")
            print(f"  转弯次数: {compute_turn_count(path)}")
            print(f"  平滑性（累计转弯角度）: {compute_smoothness(path)}°")
            print(f"  可行性代价（冰厚）: {compute_feasibility(path, normalized_image):.2f}")
            print(f"  风险指数（海冰风险）: {compute_risk(path, density_map_resized):.2f}")
